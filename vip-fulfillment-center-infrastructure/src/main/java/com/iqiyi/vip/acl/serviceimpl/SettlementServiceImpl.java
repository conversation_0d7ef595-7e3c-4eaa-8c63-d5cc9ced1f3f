package com.iqiyi.vip.acl.serviceimpl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import com.iqiyi.vip.domain.rights.entity.ReceiveRightRecord;
import com.iqiyi.vip.domain.settlement.entity.Settlement;
import com.iqiyi.vip.domain.settlement.service.SettlementService;
import com.iqiyi.vip.enums.SettlementTypeEnum;

/**
 * <AUTHOR>
 * @date 2023/9/11 21:07
 */
@Slf4j
@Service("settlementService")
public class SettlementServiceImpl implements SettlementService {

    @Override
    public void saveSettlementInfo(ReceiveRightRecord receiveRightRecord, Settlement settlement) {
        if (null == settlement.getSettleCalculateType()) {
            log.info("[null settleCalculateType ignore][receiveRightRecord:{}]", receiveRightRecord);
            return;
        }
        if (SettlementTypeEnum.FIX_PRICE.equals(settlement.getSettleCalculateType())) {
            if (receiveRightRecord.getSettlementPrice() != null && receiveRightRecord.getSettlementPrice() > 0) {
                log.warn("[settlement already receiveRightRecord:{} ]", JSON.toJSONString(receiveRightRecord));
                return;
            }
            receiveRightRecord.setSettlementPrice(settlement.getSettlePrice().longValue());
            log.info("[setSettlementPrice success][receiveRightRecord:{}]", receiveRightRecord);
        } else if (SettlementTypeEnum.PERCENT.equals(settlement.getSettleCalculateType())) {
            if (receiveRightRecord.getSettlementPercent() != null && receiveRightRecord.getSettlementPercent() > 0) {
                log.warn("[settlement already receiveRightRecord:{} ]", JSON.toJSONString(receiveRightRecord));
                return;
            }
            receiveRightRecord.setSettlementPercent(settlement.getSettlePercent());
            log.info("[setSettlementPercent success][receiveRightRecord:{}]", receiveRightRecord);
        }
    }
}
