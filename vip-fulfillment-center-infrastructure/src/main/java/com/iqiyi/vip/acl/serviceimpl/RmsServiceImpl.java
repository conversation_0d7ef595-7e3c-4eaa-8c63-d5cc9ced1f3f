package com.iqiyi.vip.acl.serviceimpl;

import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

import com.iqiyi.vip.domain.rms.repository.RmsRepository;
import com.iqiyi.vip.domain.rms.service.RmsService;

/**
 * <AUTHOR>
 * @date 2023/10/12 21:15
 */
@Slf4j
@Service("rmsService")
public class RmsServiceImpl implements RmsService {

    @Resource
    private RmsRepository rmsRepository;

    @Override
    @Cacheable(value = "fulfillAssetRmsSkuIdList", cacheManager = "caffeineCacheManager")
    public List<String> queryAssetRMSSkuIdList() {
        String skuIdListStr = rmsRepository.queryAssetRmsSkuIdList();
        List<String> skuIdList = StringUtils.isBlank(skuIdListStr) ? Collections.EMPTY_LIST : Splitter.on(",").splitToList(skuIdListStr);
        return skuIdList;
    }
}
