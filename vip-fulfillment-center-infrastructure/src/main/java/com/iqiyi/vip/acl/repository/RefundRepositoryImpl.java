package com.iqiyi.vip.acl.repository;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Repository;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import com.qiyi.vip.commons.web.dto.WebResult;
import com.iqiyi.vip.domain.refund.RefundRepository;
import com.iqiyi.vip.domain.refund.entity.RetreatResult;
import com.iqiyi.vip.enums.CodeEnum;

import static com.iqiyi.vip.domain.refund.entity.RetreatResult.CANCEL;
import static com.iqiyi.vip.domain.refund.entity.RetreatResult.RETRY;
import static com.iqiyi.vip.domain.refund.entity.RetreatResult.SUCCESS;
import static com.iqiyi.vip.log.Constants.*;

/**
 * @author: linpeihui
 * @createTime: 2025/01/15
 */
@Slf4j
@Repository
public class RefundRepositoryImpl implements RefundRepository {
    @Value("${refund.qsm.switch:true}")
    private Boolean refundQsmSwitch;

    private static final ImmutableMap<String, String> Code2Result = new ImmutableMap.Builder<String, String>()
        .put("A00000", SUCCESS)  //回收成功
        .put("REFUND_SUCCESS_EXISTED", SUCCESS)  //该订单已处理
        .put("Q00367", RETRY)  //查询会员信息失败(会员信息接口返回非200)
        .put("Q00301", CANCEL)  //参数错误(未查询到订单信息、未查询到sku信息、非兑换升级订单)
        .put("Q00368", CANCEL)  //用户没有会员权益
        .put("Q00369", CANCEL)  //用户身份不满足条件
        .put("Q00398", CANCEL)  //兑换升级处理中，请勿重复操作
        .build();

    private static final String RETREAT_LOWER_RIGHT_URL = "/refundService/order/retreatLowerRightByExchangeOrder";

    @Value("${refund.eureka.host:http://VIPTRADE-REFUNDSERVICE-API}")
    private String refundEurekaHost;
    @Value("${refund.qsm.host:http://viptrade-refundservice-api.qsm.qiyi.middle}")
    private String refundQsmHost;

    @Resource(name = "refundLbRestTemplate")
    private RestTemplate refundLbRestTemplate;
    @Resource(name = "refundRestTemplate")
    private RestTemplate refundRestTemplate;


    @Override
    public RetreatResult retreatLowerRight(String upgradedOrderCode) {
        RetreatResult result = new RetreatResult();
        ResponseEntity<WebResult<List<String>>> respEntity = null;
        String url = getRefundHost() + RETREAT_LOWER_RIGHT_URL;
        try {
            log.info("Retreat lower right start. orderCode:{}, url:{}", upgradedOrderCode, url);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            headers.add(SIGNATURE_HEADER, "universalKey");
            headers.add(SOURCE_HEADER, "vip-fulfillment-center");

            Map<String, String> reqParams = Maps.newHashMap();
            reqParams.put("exchangeOrderCode", upgradedOrderCode);
            MultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<>();
            for (Map.Entry<String, String> entry : reqParams.entrySet()) {
                multiValueMap.add(entry.getKey(), entry.getValue());
            }

            long startTime = System.currentTimeMillis();
            HttpEntity entity = new HttpEntity(multiValueMap, headers);
            respEntity = getRefundRestTemplate().exchange(url, HttpMethod.POST, entity, new ParameterizedTypeReference<WebResult<List<String>>>() {
            });
            log.info("Retreat lower right respEntity. url:{}, params:{}, status:{}, body:{}, ct:{}",
                url, reqParams, respEntity.getStatusCode(), respEntity.getBody(), System.currentTimeMillis() - startTime);
            if (respEntity.getStatusCode() != HttpStatus.OK || respEntity.getBody() == null || respEntity.getBody().getCode() == null) {
                return result;
            }
            WebResult<List<String>> respData = respEntity.getBody();
            String status = Code2Result.getOrDefault(respData.getCode(), RETRY);
            result.setStatus(status);
            return result;
        } catch (Exception e) {
            log.error("Retreat lower right error. orderCode:{}, url:{}, resp:{} ", upgradedOrderCode, url, respEntity, e);
            result.setStatus(RETRY);
            return result;
        }
    }

    private String getRefundHost() {
        if (refundQsmSwitch) {
            return refundQsmHost;
        }
        return refundEurekaHost;
    }

    private RestTemplate getRefundRestTemplate() {
        if (refundQsmSwitch) {
            return refundRestTemplate;
        }
        return refundLbRestTemplate;
    }

}
