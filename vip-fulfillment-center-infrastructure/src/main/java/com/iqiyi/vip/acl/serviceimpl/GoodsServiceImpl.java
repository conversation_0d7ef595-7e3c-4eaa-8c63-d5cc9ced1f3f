package com.iqiyi.vip.acl.serviceimpl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.iqiyi.vip.domain.goods.entity.Goods;
import com.iqiyi.vip.domain.goods.repository.GoodsRepository;
import com.iqiyi.vip.domain.goods.service.GoodsService;

/**
 * <AUTHOR>
 * @date 2023/11/8 11:21
 */
@Slf4j
@Service("goodsService")
public class GoodsServiceImpl implements GoodsService {

    @Resource
    private GoodsRepository goodsRepository;

    @Override
    public String querySkuIdByPromotionCode(String promotionCode) {
        if (StringUtils.isBlank(promotionCode)) {
            return null;
        }
        Goods goods = goodsRepository.queryByPromotionCodeFromCache(promotionCode);
        if (null != goods && StringUtils.isNotBlank(goods.getSkuId())) {
            return goods.getSkuId();
        }
        return null;
    }
}
