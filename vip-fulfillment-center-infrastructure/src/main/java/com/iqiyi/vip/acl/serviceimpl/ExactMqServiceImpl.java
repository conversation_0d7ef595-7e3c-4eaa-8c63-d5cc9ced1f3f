package com.iqiyi.vip.acl.serviceimpl;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.iqiyi.vip.domain.exact.service.ExactMqService;
import com.iqiyi.vip.domain.order.service.OrderService;
import com.iqiyi.vip.domain.rights.factory.ReceiveRightRecordFactory;
import com.iqiyi.vip.dto.exact.RightsExactMsg;
import com.iqiyi.vip.dto.exact.RightsExactTaskMsg;

/**
 * 精准触达MQ消息
 *
 * <AUTHOR>
 * @date 2023/9/12 10:29
 */
@Slf4j
@Service("exactMqService")
@ConditionalOnProperty(prefix = "rocketmq.producer", name = "enabled", havingValue = "true")
public class ExactMqServiceImpl implements ExactMqService {

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @Value("${rocketmq.producers[0].topic}")
    private String topic;

    @Resource
    private OrderService orderService;

    /**
     * http://wiki.qiyi.domain/pages/viewpage.action?pageId=1215206132
     */
    public void sendRightsMsg(RightsExactMsg msg) {
        log.info("[msg:{}]", msg);
        rocketMQTemplate.syncSend(topic, MessageBuilder.withPayload(msg).build());
    }

    @Override
    public void sendRightsMsg(RightsExactTaskMsg taskMsg) {
        sendRightsMsg(ReceiveRightRecordFactory.buildRightsExactMsg(taskMsg, orderService));
    }
}
