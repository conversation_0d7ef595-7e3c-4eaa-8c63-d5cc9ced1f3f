package com.iqiyi.vip.acl.repository;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Map;

import com.iqiyi.vip.component.HttpComponent;
import com.iqiyi.vip.domain.thirdauth.repository.ThirdAuthRepository;
import com.iqiyi.vip.domain.utils.BeanCovertUtil;
import com.iqiyi.vip.dto.http.HttpResDTO;
import com.iqiyi.vip.dto.thirdauth.ThirdAuthQuery;
import com.iqiyi.vip.dto.thirdauth.ThirdAuthQueryRes;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.YesOrNoEnum;
import com.iqiyi.vip.utils.Md5Utils;

/**
 * 第三方授权服务
 *
 * <AUTHOR>
 * @date 2024/8/13 13:51
 */
@Slf4j
@Repository
public class ThirdAuthRepositoryImpl implements ThirdAuthRepository {

    @Value("${third.auth.signKey:ce6942568b4b1d18259f8487}")
    private String thirdAuthSignKey;

    @Value("${third.auth.sourceId:6}")
    private String thirdAuthSourceId;

    @Value("${third.auth.api.eureka.host:http://VIP-THIRD-AUTH-API-TEST/thirdauth/}")
    private String thirdAuthApiEurekaHost;

    @Resource
    private HttpComponent httpComponent;

    /**
     * 查询是否可切换账号 https://iq.feishu.cn/wiki/GuMEwSRjsivgp5kv90zcBB5VnNg?open_in_browser=true
     */
    @Override
    public Integer queryChangeAccount(ThirdAuthQuery thirdAuthQuery) {
        try {
            Map<String, Object> params = BeanCovertUtil.transBean2Map(thirdAuthQuery);
            putSign(params);
            HttpResDTO<ThirdAuthQueryRes> response = httpComponent.soonGetForEntity(true,
                thirdAuthApiEurekaHost + "/queryByUid/users"
                , params, new TypeReference<ThirdAuthQueryRes>() {
                });
            if (null != response && CodeEnum.SUCCESS.getCode().equals(response.getCode())) {
                return response.getData().getCanAuthorize();
            }
        } catch (Exception e) {
            log.error("[Exception][thirdAuthQuery:{}]", thirdAuthQuery, e);
        }
        return YesOrNoEnum.NO.getValue();
    }

    private Map<String, Object> putSign(Map<String, Object> param) {
        param.put("timestamp", System.currentTimeMillis());
        param.put("sourceId", thirdAuthSourceId);
        param.put("sign", Md5Utils.signNotNull(param, thirdAuthSignKey));
        return param;
    }
}
