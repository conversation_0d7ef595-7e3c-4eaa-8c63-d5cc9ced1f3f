package com.iqiyi.vip.acl.repository;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

import com.iqiyi.vip.domain.rights.entity.ReceiveRightRecord;
import com.iqiyi.vip.domain.rights.repository.ReceiveRecordRepository;
import com.iqiyi.vip.domain.rights.repository.RightsRecordRepository;
import com.iqiyi.vip.dto.rights.ReceiveRecordByOrderCodeQry;
import com.iqiyi.vip.dto.rights.ReceiveRightRecordQryCon;
import com.iqiyi.vip.dto.rights.RecordByOrderCodeSkuIdQry;

/**
 * <AUTHOR>
 * @date 2023/8/31 10:55
 */
@Slf4j
@Repository
public class ReceiveRecordRepositoryImpl implements ReceiveRecordRepository {

    @Resource
    private RightsRecordRepository rightsRecordRepository;

    @Override
    public int insertRecord(ReceiveRightRecord receiveRightRecord) {
        return rightsRecordRepository.insertRecord(receiveRightRecord);
    }

    @Override
    public Integer updateByOrderCodeSelective(ReceiveRightRecord receiveRightRecord) {
        return rightsRecordRepository.updateByOrderCodeSelective(receiveRightRecord);
    }

    @Override
    public Integer updateCallBackStatus(ReceiveRightRecord receiveRightRecord) {
        return rightsRecordRepository.updateCallBackStatus(receiveRightRecord);
    }

    @Override
    public List<ReceiveRightRecord> queryByCon(ReceiveRightRecordQryCon con) {
        return rightsRecordRepository.queryByCon(con);
    }

    @Override
    public List<ReceiveRightRecord> queryByConFromMySql(ReceiveRightRecordQryCon con) {
        return rightsRecordRepository.queryByConFromMySql(con);
    }

    @Override
    public ReceiveRightRecord queryByOrderCode(ReceiveRecordByOrderCodeQry qry) {
        return rightsRecordRepository.queryByOrderCode(qry);
    }

    @Override
    public ReceiveRightRecord queryByOrderCode(RecordByOrderCodeSkuIdQry qry) {
        return rightsRecordRepository.queryByOrderCode(qry);
    }

    @Override
    public List<ReceiveRightRecord> queryCanReceiveOrderList(ReceiveRecordByOrderCodeQry receiveRecordByorderCodeQry) {
        return rightsRecordRepository.queryCanReceiveOrderList(receiveRecordByorderCodeQry);
    }

}
