package com.iqiyi.vip.acl.serviceimpl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.iqiyi.vip.domain.passport.entity.PassportUserInfo;
import com.iqiyi.vip.domain.passport.repository.PassportRepository;
import com.iqiyi.vip.domain.passport.service.PassportService;

/**
 * <AUTHOR>
 * @date 2023/11/29 14:27
 */
@Slf4j
@Service("passportService")
public class PassportServiceImpl implements PassportService {

    @Resource
    private PassportRepository passportRepository;

    @Override
    public String queryBindPhone(Long uid) {
        PassportUserInfo passportUserInfo = passportRepository.queryUserInfo(uid);
        return null == passportUserInfo ? null : passportUserInfo.getPhone();
    }
}
