package com.iqiyi.vip.acl.serviceimpl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.iqiyi.v.eagle.EagleMonitor;
import com.iqiyi.v.eagle.bo.EagleParam;
import com.iqiyi.vip.domain.benefit.repository.BenefitRepository;
import com.iqiyi.vip.domain.constraint.entity.RefundConstraint;
import com.iqiyi.vip.domain.constraint.factory.ConstraintFactory;
import com.iqiyi.vip.domain.gift.entity.Gift;
import com.iqiyi.vip.domain.gift.entity.SkuGiftDTO;
import com.iqiyi.vip.domain.gift.service.GiftService;
import com.iqiyi.vip.domain.inventory.service.InventoryService;
import com.iqiyi.vip.domain.limit.service.LimitService;
import com.iqiyi.vip.domain.passport.service.PassportService;
import com.iqiyi.vip.domain.rights.service.RightsService;
import com.iqiyi.vip.domain.ruleengine.service.RuleEngineService;
import com.iqiyi.vip.domain.sku.entity.Sku;
import com.iqiyi.vip.domain.sku.entity.SkuBeforeBuyCheckAggregate;
import com.iqiyi.vip.domain.sku.factory.SkuFactory;
import com.iqiyi.vip.domain.sku.repository.SkuRepository;
import com.iqiyi.vip.domain.sku.service.SkuService;
import com.iqiyi.vip.domain.spufulfillment.repository.SpuFulfillmentRepository;
import com.iqiyi.vip.domain.thirdauth.repository.ThirdAuthRepository;
import com.iqiyi.vip.domain.urlconfig.entity.UrlConfig;
import com.iqiyi.vip.domain.urlconfig.repository.UrlConfigRepository;
import com.iqiyi.vip.dto.sku.*;
import com.iqiyi.vip.dto.thirdauth.ThirdAuthQuery;
import com.iqiyi.vip.enums.*;
import com.iqiyi.vip.log.Constants;
import com.iqiyi.vip.utils.AssertUtils;
import com.iqiyi.vip.utils.EncodeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/9/28 11:50
 */
@Slf4j
@Service("skuService")
public class SkuServiceImpl implements SkuService {

    @Resource
    private GiftService giftService;
    @Resource
    private UrlConfigRepository urlConfigRepository;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private SkuRepository skuRepository;
    @Resource
    private PassportService passportService;
    @Resource
    private LimitService limitService;
    @Resource
    private InventoryService inventoryService;
    @Resource
    private RightsService rightsService;
    @Resource
    private SpuFulfillmentRepository spuFulfillmentRepository;
    @Resource
    private BenefitRepository benefitRepository;
    @Resource
    private ThirdAuthRepository thirdAuthRepository;
    @Resource
    private RuleEngineService ruleEngineService;
    @Resource
    private SkuService skuService;

    @Override
    public YesOrNoEnum canOnlineRefund(Sku sku) {
        Gift gift = giftService.queryBySkuId(sku);
        //非履约系统的商品，默认都可线上回收权益
        if (null == gift) {
            return YesOrNoEnum.YES;
        }
        //会员品类没配refundUrl，但是也是能够线上回收权益的
        if (GiftCodeEnum.VIP_GIFT_CODE.getGiftCode().equals(gift.getCode())) {
            return YesOrNoEnum.YES;
        }
        RefundConstraint refundConstraint = ConstraintFactory.buildRefundConstraint(sku, null);
        if (RefundPartnerType.REFUND_RIGHTS.getType() != refundConstraint.getRefundPartnerType()) {
            return YesOrNoEnum.NO;
        }
        if (refundConstraint.getRefundPartnerDeadline().before(new Date())) {
            return YesOrNoEnum.NO;
        }
        //没配合作方退单接口，返回不能线上退单
        if (null == gift.getRefundNotifyUrlId()) {
            return YesOrNoEnum.NO;
        }
        UrlConfig urlConfig = urlConfigRepository.queryByIdFromCache(gift.getSendNotifyUrlId());
        if (null == urlConfig || StringUtils.isBlank(urlConfig.getSyncUrl())) {
            return YesOrNoEnum.NO;
        }
        return YesOrNoEnum.YES;
    }

    /**
     * 查询sku和promotionCode关联关系
     */
//    @Override
//    @Cacheable(value = "fulfillSkuId2PromotionMap", cacheManager = "caffeineCacheManager")
//    public Map<String, SkuPromotionRes> skuId2PromotionMapFromCache(String skuIds) {
//        return querySkuId2PromotionMap(skuIds);
//    }
    @Override
    public Map<String, SkuPromotionRes> skuId2PromotionMapFromCache(String skuIds) {
        try {
            String cacheKey = RedisKeyEnum.SKU_PROMOTION.getRedisKey(EncodeUtils.MD5(skuIds, "UTF-8"));
            Object cacheValue = redisTemplate.opsForValue().get(cacheKey);
//            log.info("[skuIds:{}][cacheKey:{},cacheValue:{}]", skuIds, cacheKey, cacheValue);
            if (cacheValue != null && StringUtils.isNotBlank(cacheValue.toString())) {
                return JSON.parseObject(cacheValue.toString(), new TypeReference<Map<String, SkuPromotionRes>>() {
                });
            } else {
                Map<String, SkuPromotionRes> skuId2SkuPromotionMap = querySkuId2PromotionMap(skuIds);
                if (MapUtils.isNotEmpty(skuId2SkuPromotionMap)) {
                    redisTemplate.opsForValue().set(cacheKey, JSON.toJSONString(skuId2SkuPromotionMap), 12, TimeUnit.HOURS);
                }
                return skuId2SkuPromotionMap;
            }
        } catch (Exception e) {
            log.error("[skuIds:{}]", skuIds, e);
            return querySkuId2PromotionMap(skuIds);
        }
    }

    public Map<String, SkuPromotionRes> querySkuId2PromotionMap(String skuIds) {
        Map<String, Sku> skuResponseMap = skuRepository.batchQueryFromCache(SkuBatchQuery.builder().skuIds(skuIds).onlyQueryValid(false).build());
        if (MapUtils.isEmpty(skuResponseMap)) {
            return MapUtils.EMPTY_MAP;
        }

        Map<String, SkuPromotionRes> skuId2SkuPromotionMap = Maps.newHashMap();
        for (Map.Entry<String, Sku> entry : skuResponseMap.entrySet()) {
            Sku skuDetailResponse = entry.getValue();
            if (null == skuDetailResponse) {
                continue;
            }
            SkuPromotionRes skuPromotionRes = new SkuPromotionRes();
            skuPromotionRes.setSkuId(skuDetailResponse.getSkuId());
            skuPromotionRes.setSpuId(skuDetailResponse.getSpuId());
            skuPromotionRes.setPromotionCode(skuDetailResponse.getSpecAttributes().getPromotionCode());
            skuId2SkuPromotionMap.put(skuDetailResponse.getSkuId(), skuPromotionRes);
        }
        return skuId2SkuPromotionMap;
    }

    @Override
    public Map<String, List<String>> queryPackSkuId2PromotionCodeFromCache(String packSkuIds) {
        try {
            String cacheKey = RedisKeyEnum.PACK_SKU_PROMOTION.getRedisKey(EncodeUtils.MD5(packSkuIds, "UTF-8"));
            Object cacheValue = redisTemplate.opsForValue().get(cacheKey);
            if (cacheValue != null && StringUtils.isNotBlank(cacheValue.toString())) {
                return JSON.parseObject(cacheValue.toString(), new TypeReference<Map<String, List<String>>>() {
                });
            } else {
                Map<String, List<String>> skuId2SkuPromotionMap = queryPackSkuId2PromotionCodeListMap(packSkuIds);
                if (MapUtils.isNotEmpty(skuId2SkuPromotionMap)) {
                    redisTemplate.opsForValue().set(cacheKey, JSON.toJSONString(skuId2SkuPromotionMap), 12, TimeUnit.HOURS);
                }
                return skuId2SkuPromotionMap;
            }
        } catch (Exception e) {
            log.error("[packSkuIds:{}]", packSkuIds, e);
            return queryPackSkuId2PromotionCodeListMap(packSkuIds);
        }
    }

    @Override
    public List<SkuBeforeBuyCheckDetailRes> beforeBuyCheck(SkuBeforeBuyCheckReq req) {
        checkBeforeBuyCheckParams(req);

        //1.验证商品有效性
        String skuIdsStr = req.getSkuCheckList()
            .stream()
            .map(SkuCheckReq::getSkuId)
            .filter(s -> StringUtils.isNotBlank(s))
            .collect(Collectors.joining(","));

        //调用商品中心批量查询商品信息
        Map<String, Sku> skuId2SkuMap = skuRepository.batchQueryFromCache(SkuBatchQuery.builder().skuIds(skuIdsStr).onlyQueryValid(true).build());
        log.info("[skuId2SkuMap:{}]", skuId2SkuMap);
        List<SkuBeforeBuyCheckAggregate> skuBeforeBuyCheckAggregateList = SkuFactory.buildSkuBeforeBuyCheckAggregateList(skuId2SkuMap, req.getSkuCheckList());

        //2.验证商品限购
        ruleEngineService.checkSkuLimit(skuBeforeBuyCheckAggregateList, req);

        //3.验证库存
        inventoryService.checkSkuStock(SkuFactory.filterNextCheckList(skuBeforeBuyCheckAggregateList));

        //4.验证履约信息
        checkFulfillment(SkuFactory.filterNextCheckList(skuBeforeBuyCheckAggregateList));

        //5.验证限次
        limitService.checkPurchaseLimit(req.getUid(), SkuFactory.filterNextCheckList(skuBeforeBuyCheckAggregateList));

        //6.验证福利 todo 临时，当商品是通过福利系统生成的，这个商品又在加价购活动上卖，产品要求需要调用福利系统验证福利的次数和库存，未来福利活动次数限制写入商品中心、库存限制写入库存中心就不需要单独调用福利校验了
        benefitRepository.batchAskPartnerCanBuy(req.getUid(), SkuFactory.filterNextCheckList(skuBeforeBuyCheckAggregateList));

        //7.调用合作方接口验证用户能否购买
        rightsService.batchAskPartnerCanBuy(req.getUid(), SkuFactory.filterNextCheckList(skuBeforeBuyCheckAggregateList));

        //返回结果
        Map<String, SkuBeforeBuyCheckAggregate> map = skuBeforeBuyCheckAggregateMap(skuBeforeBuyCheckAggregateList);
        List<SkuBeforeBuyCheckDetailRes> checkResList = Lists.newArrayList();
        for (SkuCheckReq skuCheckReq : req.getSkuCheckList()) {
            if (StringUtils.isBlank(skuCheckReq.getSkuId())) {
                continue;
            }
            checkResList.add(buildSkuBeforeBuyCheckRes(skuCheckReq, map));
        }
        batchCheckEagleMonitor(checkResList, skuId2SkuMap);
        return checkResList;
    }

    @Override
    public FastCheckRes fastCheck(SkuBeforeBuyCheckReq req) {
        try{
            List<SkuBeforeBuyCheckDetailRes> res = beforeBuyCheck(req);
            Integer canBuy = res.stream().allMatch(r -> YesOrNoEnum.YES.getValue().equals(r.getCanBuy()))
                    ? YesOrNoEnum.YES.getValue() : YesOrNoEnum.NO.getValue();
            String message = canBuy.equals(YesOrNoEnum.YES.getValue()) ? "success" :
                    res.stream().filter(r -> YesOrNoEnum.NO.getValue().equals(r.getCanBuy())).map(SkuBeforeBuyCheckDetailRes::getCheckMsg).findFirst().get();
            return FastCheckRes.builder()
                    .canBuy(canBuy)
                    .message(message)
                    .build();
        }catch (Exception e){
            log.error("[fastCheck error][req:{}]", req, e);
            return FastCheckRes.builder()
                    .canBuy(YesOrNoEnum.NO.getValue())
                    .message(e.getMessage())
                    .build();
        }
    }

    private void checkFulfillment(List<SkuBeforeBuyCheckAggregate> skuBeforeBuyCheckAggregateList) {
        if (CollectionUtils.isEmpty(skuBeforeBuyCheckAggregateList)) {
            return;
        }
        for (SkuBeforeBuyCheckAggregate skuBeforeBuyCheckAggregate : skuBeforeBuyCheckAggregateList) {
            checkFulfillment(skuBeforeBuyCheckAggregate);
        }
    }

    private void checkFulfillment(SkuBeforeBuyCheckAggregate skuBeforeBuyCheckAggregate) {
        String reqSkuId = skuBeforeBuyCheckAggregate.getSkuId();
        Sku sku = skuBeforeBuyCheckAggregate.getSku();
        SkuGiftDTO skuGiftDTO = sku.obtainSkuGiftDTO(spuFulfillmentRepository);
        if (null == skuGiftDTO) {
            log.info("[skuGiftDTO null ignore][catalogId:{},spuId:{},skuId:{}]", sku.getCatalogId(), sku.getSpuId(), sku.getSkuId());
            skuBeforeBuyCheckAggregate.setCheckResult(new SkuBeforeBuyCheckDetailRes(reqSkuId, skuBeforeBuyCheckAggregate.getSkuAmount(), CodeEnum.SUCCESS));
            skuBeforeBuyCheckAggregate.setNeedNextCheck(false);
            return;
        }
        Gift gift = giftService.queryByCode(skuGiftDTO.getGiftCode());
        if (null == gift) {
            //其他品类商品不在本系统履约，直接返回校验通过
            log.info("[giftCode null ignore][catalogId:{},spuId:{},skuId:{}]", sku.getCatalogId(), sku.getSpuId(), sku.getSkuId());
            skuBeforeBuyCheckAggregate.setCheckResult(new SkuBeforeBuyCheckDetailRes(reqSkuId, skuBeforeBuyCheckAggregate.getSkuAmount(), CodeEnum.SUCCESS));
            skuBeforeBuyCheckAggregate.setNeedNextCheck(false);
            return;
        }
        skuBeforeBuyCheckAggregate.setSpuFulfillmentConfig(skuGiftDTO.getSpuFulfillmentConfig());
        skuBeforeBuyCheckAggregate.setGift(gift);
    }

    private Map<String, SkuBeforeBuyCheckAggregate> skuBeforeBuyCheckAggregateMap(List<SkuBeforeBuyCheckAggregate> skuBeforeBuyCheckAggregateList) {
        if (CollectionUtils.isEmpty(skuBeforeBuyCheckAggregateList)) {
            return MapUtils.EMPTY_MAP;
        }
        Map<String, SkuBeforeBuyCheckAggregate> map = Maps.newHashMap();
        for (SkuBeforeBuyCheckAggregate skuBeforeBuyCheckAggregate : skuBeforeBuyCheckAggregateList) {
            map.put(getCheckUniqueKey(skuBeforeBuyCheckAggregate.getSkuId(), skuBeforeBuyCheckAggregate.getSkuAmount()), skuBeforeBuyCheckAggregate);
        }
        return map;
    }

    private static String getCheckUniqueKey(String skuId, Integer skuAmount) {
        return skuId + "_" + skuAmount;
    }

    /**
     * 购买前校验接口返回上报鹰眼
     */
    private void batchCheckEagleMonitor(List<SkuBeforeBuyCheckDetailRes> checkDetailResVOS, Map<String, Sku> skuId2SkuMap) {
        try {
            if (CollectionUtils.isEmpty(checkDetailResVOS)) {
                return;
            }
            for (SkuBeforeBuyCheckDetailRes resVO : checkDetailResVOS) {
                if (YesOrNoEnum.YES.getValue().equals(resVO.getCanBuy())) {
                    continue;
                }
                Sku sku = skuId2SkuMap.get(resVO.getSkuId());
                EagleParam eagleParam = new EagleParam(EagleParamNameEnum.BIZ_CODE_COUNTER.getName())
                    .tag("uri", "/sku/beforeBuyCheck")
                    .tag("bizCode", resVO.getCheckCode())
                    .tag("bizMsg", resVO.getCheckMsg())
                    .tag("skuId", resVO.getSkuId())
                    .tag("skuName", sku != null ? sku.getSkuName() : "");
                EagleMonitor.counterInc(eagleParam);
            }
        } catch (Exception e) {
            log.warn("checkDetailResVOS:{}", checkDetailResVOS, e);
        }
    }

    private static SkuBeforeBuyCheckDetailRes buildSkuBeforeBuyCheckRes(SkuCheckReq skuCheckReq, Map<String, SkuBeforeBuyCheckAggregate> skuId2SkuBeforeBuyCheckAggregate) {
        SkuBeforeBuyCheckAggregate currentAggregate = skuId2SkuBeforeBuyCheckAggregate.get(getCheckUniqueKey(skuCheckReq.getSkuId(), skuCheckReq.getSkuAmount()));
        if (null == currentAggregate) {
            return new SkuBeforeBuyCheckDetailRes(skuCheckReq, CodeEnum.SUCCESS);
        }
        SkuBeforeBuyCheckDetailRes currentCheckRes =
            null == currentAggregate.getCheckResult() ? new SkuBeforeBuyCheckDetailRes(skuCheckReq, CodeEnum.SUCCESS)
                : currentAggregate.getCheckResult();
        if (!YesOrNoEnum.YES.getValue().equals(currentCheckRes.getCanBuy())) {
            return currentCheckRes;
        }
        if (!SpuEnum.isPackageSpu(currentAggregate.getSku().getSpuId())) {
            return currentCheckRes;
        }
        //打包购商品若子商品校验不通过,使用子商品的校验结果,不允许购买
        List<SkuBeforeBuyCheckDetailRes> subSkuCheckResList = Lists.newArrayList();
        for (Sku subSku : currentAggregate.getSku().getSubSkuList()) {
            Integer skuAmount = Constants.PACK_SUB_SKU_DEFAULT_SKU_AMOUNT;
            SkuBeforeBuyCheckAggregate subSkuAggregate = skuId2SkuBeforeBuyCheckAggregate.get(getCheckUniqueKey(subSku.getSkuId(), skuAmount));
            if (null == subSkuAggregate) {
                continue;
            }
            SkuBeforeBuyCheckDetailRes subSkuCheckResult = subSkuAggregate.getCheckResult();
            if (null == subSkuCheckResult) {
                subSkuCheckResult = new SkuBeforeBuyCheckDetailRes(subSku.getSkuId(), skuAmount, CodeEnum.SUCCESS);
            }
            subSkuCheckResList.add(subSkuCheckResult);
            if (!YesOrNoEnum.YES.getValue().equals(subSkuCheckResult.getCanBuy())) {
                BeanUtils.copyProperties(subSkuCheckResult, currentCheckRes, "skuId");
            }
        }
        currentCheckRes.setSubSkuCheckResList(subSkuCheckResList);
        return currentCheckRes;
    }

    private void checkBeforeBuyCheckParams(SkuBeforeBuyCheckReq req) {
        AssertUtils.notEmpty(req.getSkuCheckList(), CodeEnum.ERROR_PARAM);
        if (YesOrNoEnum.YES.getValue().equals(req.getCheckBindPhone())) {
            AssertUtils.notNull(req.getUid(), CodeEnum.ERROR_NOT_LOG);
            String bindPhone = passportService.queryBindPhone(req.getUid());
            AssertUtils.notBlank(bindPhone, CodeEnum.ERROR_UNBIND_MOBILE);
        }
    }

    public Map<String, List<String>> queryPackSkuId2PromotionCodeListMap(String packSkuIds) {
        AssertUtils.notBlank(packSkuIds, CodeEnum.ERROR_PARAM);
        Map<String, Sku> packSkuResponseMap = skuRepository.batchQueryFromCache(SkuBatchQuery.builder().skuIds(packSkuIds).onlyQueryValid(false).build());
        if (MapUtils.isEmpty(packSkuResponseMap)) {
            return MapUtils.EMPTY_MAP;
        }
        Map<String, List<String>> packSkuId2RuleCodeListMap = Maps.newHashMap();
        for (Map.Entry<String, Sku> entry : packSkuResponseMap.entrySet()) {
            Sku packSkuDetailResponse = entry.getValue();
            if (null == packSkuDetailResponse) {
                continue;
            }
            List<Sku> subSkuList = packSkuDetailResponse.getSubSkuList();
            if (CollectionUtils.isEmpty(subSkuList)) {
                continue;
            }
            List<String> ruleCodeList = Lists.newArrayList();
            for (Sku subSku : subSkuList) {
                if (null != subSku && null != subSku.getSpecAttributes() && StringUtils.isNotBlank(subSku.getSpecAttributes().getRuleCode())) {
                    ruleCodeList.add(subSku.getSpecAttributes().getRuleCode());
                }
            }
            packSkuId2RuleCodeListMap.put(entry.getKey(), ruleCodeList);
        }
        return packSkuId2RuleCodeListMap;
    }

    @Override
    public void resetChangeAccount(Long uid, Map<String, Sku> skuId2SkuMap) {
        //判定是否需要调用授权服务获取changeAccount
        Set<ThirdAuthTypeEnum> authTypeSet = Sets.newHashSet();
        List<Map.Entry<String, Sku>> skusToUpdate = Lists.newArrayList();
        for (Map.Entry<String, Sku> skuMap : skuId2SkuMap.entrySet()) {
            SkuAccountTypeEnum skuAccountTypeEnum = SkuAccountTypeEnum.getByType(skuMap.getValue().getSpecAttributes().getAccountType());
            if (null != skuAccountTypeEnum && YesOrNoEnum.YES.equals(skuAccountTypeEnum.getResetChangeAccountByThirdAuth())
                && null != skuAccountTypeEnum.getThirdAuthTypeEnum()) {
                authTypeSet.add(skuAccountTypeEnum.getThirdAuthTypeEnum());
                skusToUpdate.add(skuMap);
            }
        }
        if (authTypeSet.isEmpty()) {
            return;
        }

        //调用授权服务获取changeAccount
        Map<Integer, Integer> authTypeToChangeAccount = Maps.newHashMap();
        for (ThirdAuthTypeEnum authType : authTypeSet) {
            ThirdAuthQuery thirdAuthQuery = new ThirdAuthQuery();
            thirdAuthQuery.setUid(uid);
            thirdAuthQuery.setAuthType(authType.getType());
            authTypeToChangeAccount.put(authType.getType(), thirdAuthRepository.queryChangeAccount(thirdAuthQuery));
        }

        //重置changeAccount
        for (Map.Entry<String, Sku> entry : skusToUpdate) {
            Sku sku = entry.getValue();
            SkuAccountTypeEnum skuAccountTypeEnum = SkuAccountTypeEnum.getByType(sku.getSpecAttributes().getAccountType());
            Integer changeAccount = authTypeToChangeAccount.get(skuAccountTypeEnum.getThirdAuthTypeEnum().getType());
            sku.getSpecAttributes().setChangeAccount(changeAccount);
        }
    }
}
