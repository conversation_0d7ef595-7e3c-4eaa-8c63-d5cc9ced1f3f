package com.iqiyi.vip.acl.repository;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Joiner;
import com.iqiyi.vip.component.HttpComponent;
import com.iqiyi.vip.domain.ruleengine.entity.RuleCheckReqDto;
import com.iqiyi.vip.domain.ruleengine.entity.RuleEngineRespDto;
import com.iqiyi.vip.domain.ruleengine.repository.RuleEngineRepository;
import com.iqiyi.vip.dto.http.HttpResDTO;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.StatusEnum;
import com.iqiyi.vip.exception.BizRuntimeException;
import com.iqiyi.vip.utils.RetryUtil;
import com.qiyi.vip.commons.util.SecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Repository;
import org.springframework.web.client.ResourceAccessException;

import javax.annotation.Resource;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.util.*;
import java.util.concurrent.TimeoutException;

/**
 * <AUTHOR>
 * @date 2025/3/07 13:19
 */
@Slf4j
@Repository
public class RuleEngineRepositoryImpl implements RuleEngineRepository {

    @Value("${ruleEngine.key:Emds7VZbeFC8F8uZ}")
    private String ruleEngineSignKey;

    @Value("${ruleEngine.eureka.switch:1}")
    private Integer ruleEngineEurekaSwitch;

    @Value("${ruleEngine.http.url:http://rule.engine.api.test.qiyi.qae/rule/api/v1/checkLimit}")
    private String ruleEngineUrl;

    @Value("${ruleEngine.eureka.url:http://VIP-RULE-ENGINE-API-DEV/rule/api/v1/checkLimit}")
    private String ruleEngineCloudUrl;

    @Resource
    private HttpComponent httpComponent;

    @Value("${ruleEngine.retry.max-attempts:2}")
    private int maxRetryAttempts;

    /**
     * 规则引擎接口文档：https://iq.feishu.cn/wiki/W1PUwDyt2i8XkDklhNUcAUCZnDd
     */
    @Override
    public List<RuleEngineRespDto> queryRuleEngine(RuleCheckReqDto ruleEngineReqDto) {
        if (ruleEngineReqDto == null || StringUtils.isBlank(ruleEngineReqDto.getLimitCode())) {
            return new ArrayList<>();
        }
        return RetryUtil.executeWithRetry(() -> {
            Map<String, Object> reqMap = ruleEngineReqDto.toMap();
            reqMap.put("sign", getSign(reqMap, ruleEngineSignKey));
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            log.info("[queryRuleEngine start][reqMap:{}]", reqMap);
            HttpResDTO<List<RuleEngineRespDto>> httpResDTO = httpComponent.soonPost(eurekaSwitch(),
                    getUrl(), reqMap, headers, new TypeReference<List<RuleEngineRespDto>>(){
                    });
            log.info("[queryRuleEngine end][reqMap:{}][response:{}]", ruleEngineReqDto.toMap(), httpResDTO);
            if (null == httpResDTO || !httpResDTO.suc()) {
                throw new BizRuntimeException(CodeEnum.HTTP_ERROR);
            }
            return httpResDTO.getData();
        }, maxRetryAttempts, TimeoutException.class, ConnectException.class, SocketTimeoutException.class, ResourceAccessException.class);
    }

    private String getSign(Map<String, Object> params, String signKey) {
        SortedMap<String, Object> sortedParams = new TreeMap<>(params);
        String strForSign = Joiner.on("&")
                .useForNull("")
                .withKeyValueSeparator("=")
                .join(sortedParams)
                .concat(signKey);
        return SecurityUtil.MD5(strForSign, "UTF-8");
    }

    private String getUrl() {
        return eurekaSwitch() ? ruleEngineCloudUrl : ruleEngineUrl;
    }

    private boolean eurekaSwitch() {
        return ruleEngineEurekaSwitch.equals(StatusEnum.VALID.getStatus());
    }

}
