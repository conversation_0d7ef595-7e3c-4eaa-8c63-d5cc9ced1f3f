package com.iqiyi.vip.acl.repository;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;
import org.springframework.util.MultiValueMap;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.util.Map;

import com.iqiyi.vip.component.HttpComponent;
import com.iqiyi.vip.domain.outgateway.repository.OutGatewayRepository;
import com.iqiyi.vip.dto.http.HttpResDTO;
import com.iqiyi.vip.utils.Md5Utils;

/**
 * <AUTHOR>
 * @date 2023/12/19 11:39
 */
@Slf4j
@Repository
public class OutGatewayRepositoryImpl implements OutGatewayRepository {


    /**
     * 网关服务地址
     */
    @Value("${gateway.api.baseurl:http://g.vip.qiyi.domain}")
    private String apiBaseUrl;

    /**
     * 网关域名
     */
    @Value("${gateway.domain:g.vip.qiyi.domain}")
    private String gatewayDomain;

    /**
     * 网关API - token参数
     */
    @Value("${gateway.api.token:3674d09968919b63}")
    private String gatewayToken;

    /**
     * 网关API - sys参数
     */
    @Value("${gateway.api.sys:cobrand}")
    private String system;

    /**
     * 网关API - sys参数
     */
    @Value("${gateway.api.signKey:a1817bcab1f30a30}")
    private String signKey;

    /**
     * 可选TokenName
     */
    private static final String[] POSSIBLE_TOKEN_NAMES = new String[]{
        "x-token",  // 第一个为默认
        "token",
        "_token"
    };

    /**
     * 获取原地址API 路径
     */
    private static final String API_PATH_GET_ORIGINAL_URL = "/api/app/getOriginalUrl";

    @Resource
    private HttpComponent httpComponent;

    @Override
    public String getOriginalUrl(String gatewayUrl) {
        try {
            // url为空，不做转换
            if (StringUtils.isBlank(gatewayUrl)) {
                return gatewayUrl;
            }

            // 不是网关地址，不需要做转换
            if (!isGatewayUrl(gatewayUrl)) {
                return gatewayUrl;
            }
            // token参数拼在url最后，去除gatewayUrl中的token
            String url = gatewayUrl;
            boolean tokenFound = false;
            for (String tokenName : POSSIBLE_TOKEN_NAMES) {
                String tokenParam = tokenName + "=" + gatewayToken;
                if (StringUtils.endsWithIgnoreCase(url, "?" + tokenParam)) {
                    url = StringUtils.removeEnd(url, "?" + tokenParam);
                    tokenFound = true;
                    break;
                } else if (StringUtils.endsWithIgnoreCase(url, "&" + tokenParam)) {
                    url = StringUtils.removeEnd(url, "&" + tokenParam);
                    tokenFound = true;
                    break;
                }
            }

            // 未找到token，不做转换(非网关地址)
            if (!tokenFound) {
                return gatewayUrl;
            }

            Map<String, Object> request = Maps.newHashMap();

            request.put("sys", system);
            request.put("timestamp", String.valueOf(System.currentTimeMillis()));
            request.put("url", url);

            String sign = Md5Utils.sign(request, signKey);
            request.put("sign", sign);

            HttpResDTO<Map<String, String>> response = httpComponent.post(false,
                apiBaseUrl + API_PATH_GET_ORIGINAL_URL
                , request, null, new TypeReference<Map<String, String>>() {
                });
            if (response.suc()) {
                String originalUrl = response.getData().get("originalUrl");
                return originalUrl;
            }

            // 转换失败
            return gatewayUrl;
        } catch (Exception e) {
            log.error("[getOriginalUrl fail][gatewayUrl:{}]", gatewayUrl, e);
            return gatewayUrl;
        }
    }

    /**
     * 判断地址是否是网关地址
     */
    private boolean isGatewayUrl(String url) {
        try {
            UriComponents uri = UriComponentsBuilder.fromUriString(url).build();
            String host = uri.getHost();

            if (StringUtils.equalsIgnoreCase(host, gatewayDomain)) {
                MultiValueMap<String, String> parameters = uri.getQueryParams();
                for (String tokenName : POSSIBLE_TOKEN_NAMES) {
                    if (parameters.containsKey(tokenName)) {
                        return true;
                    }
                }
            }
            return false;
        } catch (IllegalArgumentException e) {
            log.warn("url: {} not valid", e);
            return false;
        }
    }
}
