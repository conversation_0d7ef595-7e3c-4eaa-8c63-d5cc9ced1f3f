package com.iqiyi.vip.acl.repository;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import com.iqiyi.vip.domain.order.entity.FreePayResult;
import com.iqiyi.vip.dto.rights.AskPartnerCanBuyByUrlIdDetailRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Repository;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.*;

import com.qiyi.vip.commons.constant.QueryConstants;
import com.qiyi.vip.trade.dataservice.client.DataServiceClient;
import com.qiyi.vip.trade.dataservice.client.dto.OrderDto;
import com.qiyi.vip.trade.dataservice.client.request.QueryOrdersRequest;
import com.qiyi.vip.trade.dataservice.client.response.QueryOrdersResponse;
import com.iqiyi.vip.component.HttpComponent;
import com.iqiyi.vip.domain.order.repository.OrderRepository;
import com.iqiyi.vip.domain.utils.BeanCovertUtil;
import com.iqiyi.vip.dto.http.HttpResDTO;
import com.iqiyi.vip.dto.rights.OrderDeliverReq;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.StatusEnum;
import com.iqiyi.vip.utils.Md5Utils;

/**
 * <AUTHOR>
 * @date 2023/9/12 21:18
 */
@Slf4j
@Repository("orderRepository")
public class OrderRepositoryImpl implements OrderRepository {

    @Value("${orderSys.qsm.switch:true}")
    private Boolean orderSysQsmSwitch;
    @Value("${orderSys.eurekaUrl:http://ORDER-SYSTEM-TEST}")
    private String orderSysEurekaHost;
    @Value("${orderSys.qsmUrl:http://order-system.qsm.qiyi.middle}")
    private String orderSysQsmHost;

    @Value("${freepay.eureka.switch:1}")
    private Boolean freePayEurekaSwitch;
    @Value("${freepay.eurekaUrl:http://VIPTRADE-FREE-ORDER/api/internal/free-pay/dopay.action}")
    private String freePayEurekaUrl;
    @Value("${freepay.qsmUrl:http://freepay-system.qsm.qiyi.middle/api/internal/free-pay/dopay.action}")
    private String freePayQsmUrl;
    @Value("${freePay.isTest:true}")
    private String isTest;
    @Value("${freePay.key:123456}")
    private String signKey;
    @Value("${freePay.actCode:payresultLottery}")
    private String actCode;

    @Resource(name = "dataServiceClient")
    private DataServiceClient dataServiceClient;
    @Resource(name = "orderSysLbRestTemplate")
    private RestTemplate orderSysLbRestTemplate;
    @Resource(name = "orderSysRestTemplate")
    private RestTemplate orderSysRestTemplate;
    @Resource
    private HttpComponent httpComponent;

    /**
     * 订单回调接口
     * http://atlas.qiyi.domain/system/interface/detail?interfaceId=bb6968a7688344f08c786afad7f49206&branch=master&version=currentVersion&path=undefined
     */
    @Override
    public HttpResDTO deliver(OrderDeliverReq req) {
        try {
            Map<String, Object> params = BeanCovertUtil.transBean2Map(req);
            HttpResDTO<String> response = httpComponent.postJson(
                getOrderSysHost() + "/api/order/deliver", params, getOrderSysRestTemplate(), new TypeReference<String>() {
                });
            log.info("[HTTP-post][end][req:{},response:{}]", req, response);
            return response;
        } catch (Exception e) {
            log.error("[HTTP-post][Exception][req:{}]", req, e);
            return HttpResDTO.create(CodeEnum.HTTP_ERROR);
        }
    }

    /**
     * 取消订单
     * http://atlas.qiyi.domain/system/interface/list?systemId=64abc6038970110010aa828d&branch=master
     * @param orderCode
     * @return
     */
    @Override
    public Boolean cancel(String orderCode) {
        HttpResDTO<String> response = null;
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("orderCode", orderCode);
            log.info("Cancel order start, orderCode:{}", orderCode);
            response = httpComponent.postJson(
                getOrderSysHost() + "/api/order/cancel", params, getOrderSysRestTemplate(), new TypeReference<String>() {
                });
            if (null == response || !CodeEnum.SUCCESS.getCode().equals(response.getCode())) {
                log.error("Cancel order failed, orderCode:{}, resp:{}", orderCode, response);
                return false;
            }
            log.info("Cancel order end, orderCode:{}, resp:{}", orderCode, response);
            return true;
        } catch (Exception e) {
            log.error("Cancel order error, orderCode:{}, resp:{}", orderCode, response);
            return false;
        }
    }

    @Override
    public OrderDto queryByOrderCode(String orderCode) {
        //调用交易系统根据第三方订单号，查询奇悦订单信息
        QueryOrdersRequest queryOrdersRequest = new QueryOrdersRequest();
        queryOrdersRequest.addParam("orderCode", orderCode);
        queryOrdersRequest.addParam(QueryConstants.QUERY_PARAM_LIMIT, String.valueOf(QueryConstants.MAX_PAGE_SIZE));
        QueryOrdersResponse response = dataServiceClient.execute(queryOrdersRequest);
        log.info("[queryByOrderCode][orderCode:{},response:{}]", orderCode, JSON.toJSON(response));
        if (!response.isSuccessful()) {
            return null;
        }
        if (CollectionUtils.isEmpty(response.getData()) || response.getData().size() > 1) {
            return null;
        }
        return response.getData().get(0);
    }

    @Override
    public OrderDto queryByTradeCode(String tradeCode) {
        QueryOrdersRequest queryOrdersRequest = new QueryOrdersRequest();
        queryOrdersRequest.addParam("tradeCode", tradeCode);
        queryOrdersRequest.addParam(QueryConstants.QUERY_PARAM_LIMIT, String.valueOf(QueryConstants.MAX_PAGE_SIZE));
        QueryOrdersResponse response = dataServiceClient.execute(queryOrdersRequest);
        log.info("[queryByTradeCode][tradeCode:{},response:{}]", tradeCode, JSON.toJSON(response));
        if (!response.isSuccessful()) {
            return null;
        }
        if (CollectionUtils.isEmpty(response.getData()) || response.getData().size() > 1) {
            return null;
        }
        return response.getData().get(0);
    }

    @Override
    public FreePayResult freePay(String skuId, OrderDto order, String traceId) {
        Map<String, Object> payParam = createDoPayParam(skuId, order, traceId);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        HttpResDTO<FreePayResult> response = httpComponent.fastPost(freePayEurekaSwitch, getFreePayUrl(), payParam,
            headers, new TypeReference<FreePayResult>() {
                });
        if (Objects.equals(CodeEnum.SUCCESS.getCode(), response.getCode())) {
            return response.getData();
        }
        log.error("调用交易freePay接口错误, skuId {} uid {} orderCode {}", skuId, order.getUserId(), traceId);
        //接口返回错误
        throw new RuntimeException("调用交易freePay接口错误");
    }

    private Map<String, Object> createDoPayParam(String skuId, OrderDto order, String traceId) {
        Map<String, Object> doPayParam = new HashMap<>();
        doPayParam.put("actCode", actCode);
        doPayParam.put("tradeCode", "pay_result_" + traceId);
        doPayParam.put("platform", order.getPlatform());
        doPayParam.put("payType", "305");
        doPayParam.put("skuAmount", 1);
        doPayParam.put("uid", String.valueOf(order.getUserId()));
        doPayParam.put("skuId", skuId);
        doPayParam.put("fc", "888ce11650e6994a");
        doPayParam.put("saleScene", "14001");
        doPayParam.put("isTest", isTest);
        doPayParam.put("sign", Md5Utils.sign(doPayParam, signKey));
        return doPayParam;
    }

    private String getFreePayUrl() {
        return freePayEurekaSwitch.equals(Boolean.TRUE) ? freePayEurekaUrl : freePayQsmUrl;
    }

    private String getOrderSysHost() {
        if (orderSysQsmSwitch) {
            return orderSysQsmHost;
        }
        return orderSysEurekaHost;
    }

    private RestTemplate getOrderSysRestTemplate() {
        if (orderSysQsmSwitch) {
            return orderSysRestTemplate;
        }
        return orderSysLbRestTemplate;
    }

}
