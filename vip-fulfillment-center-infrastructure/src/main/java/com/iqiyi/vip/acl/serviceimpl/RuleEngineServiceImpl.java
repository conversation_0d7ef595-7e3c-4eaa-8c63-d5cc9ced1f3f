package com.iqiyi.vip.acl.serviceimpl;

import com.iqiyi.vip.domain.ruleengine.entity.RuleCheckReqDto;
import com.iqiyi.vip.domain.ruleengine.entity.RuleEngineRespDto;
import com.iqiyi.vip.domain.ruleengine.repository.RuleEngineRepository;
import com.iqiyi.vip.domain.ruleengine.service.RuleEngineService;
import com.iqiyi.vip.domain.sku.entity.Sku;
import com.iqiyi.vip.domain.sku.entity.SkuBeforeBuyCheckAggregate;
import com.iqiyi.vip.dto.sku.SkuBeforeBuyCheckDetailRes;
import com.iqiyi.vip.dto.sku.SkuBeforeBuyCheckReq;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.YesOrNoEnum;
import com.iqiyi.vip.exception.BizRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/10 14:01
 */
@Service
@Slf4j
public class RuleEngineServiceImpl implements RuleEngineService {

    @Resource
    private RuleEngineRepository ruleEngineRepository;

    @Override
    public void checkSkuLimit(List<SkuBeforeBuyCheckAggregate> skuBeforeBuyCheckAggregateList, SkuBeforeBuyCheckReq req) {
        if (CollectionUtils.isEmpty(skuBeforeBuyCheckAggregateList)) {
            return;
        }
        try{
            List<String> limitCodeList = new ArrayList<>();
            List<String> skuAmountList = new ArrayList<>();
            skuBeforeBuyCheckAggregateList.stream()
                    .filter(aggregate -> (aggregate.getSku() != null && StringUtils.isNotBlank(aggregate.getSku().getSpecAttributes().getBuyLimitRuleCode())))
                    .forEach(item -> {
                        limitCodeList.add(item.getSku().getSpecAttributes().getBuyLimitRuleCode());
                        skuAmountList.add(String.valueOf(item.getSkuAmount()));
                    });
            skuBeforeBuyCheckAggregateList.stream()
                    .filter(aggregate -> StringUtils.isNotBlank(aggregate.getActCenterRuleCode()))
                    .forEach(item -> {
                        limitCodeList.add(item.getActCenterRuleCode());
                        skuAmountList.add(String.valueOf(item.getSkuAmount()));
                    });
            String limitCodes = String.join(",", limitCodeList);
            String skuAmounts = String.join(",", skuAmountList);
            RuleCheckReqDto ruleEngineReqDto = RuleCheckReqDto.builder()
                    .uid(String.valueOf(req.getUid()))
                    .deviceId(req.getQyid())
                    .limitCode(limitCodes)
                    .addAmount(skuAmounts)
                    .build();
            List<RuleEngineRespDto> ruleEngineRespDtoList = ruleEngineRepository.queryRuleEngine(ruleEngineReqDto);
            Map<String, Boolean> ruleCode2Result = ruleEngineRespDtoList.stream()
                    .collect(Collectors.toMap(RuleEngineRespDto::getCode, dto -> Boolean.FALSE.equals(dto.getTrust()) || dto.getResult()
                    , (existingValue, newValue) -> existingValue && newValue ));

            for (SkuBeforeBuyCheckAggregate skuBeforeBuyCheckAggregate : skuBeforeBuyCheckAggregateList) {
                if (skuBeforeBuyCheckAggregate.getSku() == null) {
                    skuBeforeBuyCheckAggregate.setCheckResult(new SkuBeforeBuyCheckDetailRes(skuBeforeBuyCheckAggregate.getSkuId(), skuBeforeBuyCheckAggregate.getSkuAmount(), CodeEnum.ERROR_SKU_NULL));
                    skuBeforeBuyCheckAggregate.setNeedNextCheck(false);
                    continue;
                }
                String skuLimitCode = skuBeforeBuyCheckAggregate.getSku().getSpecAttributes().getBuyLimitRuleCode();
                String actCenterRuleCode = skuBeforeBuyCheckAggregate.getActCenterRuleCode();
                if ((StringUtils.isNotBlank(skuLimitCode) && !ruleCode2Result.get(skuLimitCode))) {
                    skuBeforeBuyCheckAggregate.setCheckResult(new SkuBeforeBuyCheckDetailRes(skuBeforeBuyCheckAggregate.getSkuId(), skuBeforeBuyCheckAggregate.getSkuAmount(), CodeEnum.ERROR_SKU_TIMES_LIMIT));
                    skuBeforeBuyCheckAggregate.setNeedNextCheck(false);
                }
                if (StringUtils.isNotBlank(actCenterRuleCode) && !ruleCode2Result.get(actCenterRuleCode)) {
                    skuBeforeBuyCheckAggregate.setCheckResult(new SkuBeforeBuyCheckDetailRes(skuBeforeBuyCheckAggregate.getSkuId(), skuBeforeBuyCheckAggregate.getSkuAmount(), CodeEnum.ERROR_ACT_TIMES_LIMIT));
                    skuBeforeBuyCheckAggregate.setNeedNextCheck(false);
                }
            }
        }catch (Exception e){
            log.error("[Exception][needCheckStockSkuIdList:{}]", skuBeforeBuyCheckAggregateList, e);
        }
    }

}
