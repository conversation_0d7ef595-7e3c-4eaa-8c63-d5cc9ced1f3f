package com.iqiyi.vip.acl.repository;

import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

import com.iqiyi.vip.domain.fulfillment.repository.FulfillmentTaskRepository;
import com.iqiyi.vip.domain.fulfillment.service.FulfillmentEventService;
import com.iqiyi.vip.domain.task.SaveFulfillmentConfigTask;
import com.iqiyi.vip.dto.act.ActMsg;
import com.iqiyi.vip.repository.ClusterAsyncTaskManager;

/**
 * <AUTHOR>
 * @date 2023/7/31 16:15
 */
@Repository
public class FulfillmentTaskRepositoryImpl implements FulfillmentTaskRepository {

    @Resource
    private ClusterAsyncTaskManager clusterAsyncTaskManager;
    @Resource
    private FulfillmentEventService fulfillmentEventService;

    @Override
    public void saveFulfillmentConfigFailRetry(String skuId, ActMsg actMsg) {
        if (!fulfillmentEventService.save(skuId, actMsg)) {
            clusterAsyncTaskManager.insertTask(new SaveFulfillmentConfigTask(skuId, actMsg));
        }
    }
}
