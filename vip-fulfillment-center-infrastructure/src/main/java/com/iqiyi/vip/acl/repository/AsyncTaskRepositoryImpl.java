package com.iqiyi.vip.acl.repository;

import com.iqiyi.vip.domain.task.entity.AsyncTaskStat;
import com.iqiyi.vip.domain.task.repository.AsyncTaskRepository;
import com.iqiyi.vip.mapper.AsyncTaskMapper;
import com.iqiyi.vip.po.AsyncTaskRawPO;
import com.iqiyi.vip.utils.AsyncTaskDataProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 异步任务仓储实现类
 */
@Slf4j
@Repository
public class AsyncTaskRepositoryImpl implements AsyncTaskRepository {

    @Resource
    private AsyncTaskMapper asyncTaskMapper;

    @Override
    public List<AsyncTaskStat> queryExeCountStatWithRightsInfo() {
        try {
            // 1. 查询原始数据（避免SQL层JSON解析）
            List<AsyncTaskRawPO> rawDataList = asyncTaskMapper.selectExeCountStatWithRightsInfo();

            if (rawDataList == null || rawDataList.isEmpty()) {
                log.info("[AsyncTaskRepository] No raw data found");
                return Collections.emptyList();
            }

            // 2. 在程序层进行JSON解析和聚合
            List<AsyncTaskStat> result = AsyncTaskDataProcessor.processAndAggregate(rawDataList);

            // 3. 增强空指针检查
            if (result == null) {
                log.warn("[AsyncTaskRepository] AsyncTaskDataProcessor returned null, using empty list");
                result = Collections.emptyList();
            }

            log.info("[AsyncTaskRepository] Processed {} raw records into {} aggregated stats (WHERE: exe_count > 0 AND run_time > NOW())",
                    rawDataList.size(), result.size());

            return result;
        } catch (Exception e) {
            log.error("Failed to query async task execution statistics with rights info", e);
            return Collections.emptyList();
        }
    }
}