package com.iqiyi.vip.acl.serviceimpl;

import com.iqiyi.solar.config.client.spring.annotation.ConfigJsonValue;
import com.iqiyi.vip.domain.doublevip.entity.DoubleVipReq;
import com.iqiyi.vip.domain.doublevip.service.DoubleVipService;
import com.iqiyi.vip.domain.order.entity.FreePayResult;
import com.iqiyi.vip.domain.order.service.OrderService;
import com.iqiyi.vip.domain.sku.entity.Sku;
import com.iqiyi.vip.domain.sku.repository.SkuRepository;
import com.iqiyi.vip.dto.sku.SkuQuery;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.OrderTypeEnum;
import com.qiyi.vip.commons.web.dto.WebResult;
import com.qiyi.vip.trade.dataservice.client.dto.OrderDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/6/10 11:47
 */
@Service
@Slf4j
public class DoubleVipServiceImpl implements DoubleVipService {

    @ConfigJsonValue("${vipTypeToSkuId}")
    private Map<String, String> vipTypeToskuIdMap;

    @Resource
    private OrderService orderService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private SkuRepository skuRepository;

    @Override
    public WebResult giveDoubleVip(DoubleVipReq req) {
        OrderDto orderInfoDto = orderService.queryByOrderCode(req.getOrderCode());
        if (orderInfoDto == null) {
            return  new WebResult(CodeEnum.PAID_ORDER_QUERY_FAIL.getCode(),"订单不存在");
        }
        OrderDto finalOrder = null;
        if(CollectionUtils.isEmpty(orderInfoDto.getChildOrders())){
            finalOrder = orderInfoDto;
        } else {
            for(OrderDto childOrder : orderInfoDto.getChildOrders()){
                if(OrderTypeEnum.MAIN_PROD.name().equals(childOrder.getOrderType())){
                    finalOrder = childOrder;
                    break;
                }
            }
        }
        if(Objects.isNull(finalOrder)){
            return new WebResult(CodeEnum.PAID_ORDER_QUERY_FAIL.getCode(), "订单不存在");
        }
        String sendSku = calSendSku(finalOrder);
        String lockKey = "payResult" + ":" + req.getTraceId();
        RLock lock = redissonClient.getLock(lockKey);
        try{
            boolean getLock = lock.tryLock(0, 2, TimeUnit.DAYS);
            if (!getLock){
                log.info("获取锁失败, 已经赠送, traceId: {}, lockKey: {}", req.getTraceId(), lockKey);
                return new WebResult(CodeEnum.SUC_CODE_REPEAT.getCode(), "已经赠送");
            }
            try{
                FreePayResult freePayResult = orderService.freePay(sendSku, orderInfoDto, req.getTraceId());
                return WebResult.newSuccess(freePayResult);
            } catch (Exception e){
                log.info("赠送失败, traceId: {}, orderCode: {}, skuId: {}, error: {}", req.getTraceId(), req.getOrderCode(), sendSku, e.getMessage());
                if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
                return new WebResult(CodeEnum.ERROR_FREE_PAY.getCode(), "赠送失败，请稍后再试");
            }
        } catch (Exception e){
            log.info("获取锁失败, traceId: {}, lockKey: {}, error: {}", req.getTraceId(), lockKey, e.getMessage());
            return new WebResult(CodeEnum.ERR_CONCURRENT.getCode(), "获取锁失败，请稍后再试");
        }
    }

    private String calSendSku(OrderDto finalOrder){
        String skuId = finalOrder.getSkuId();
        SkuQuery skuQuery = SkuQuery.builder()
                .skuId(skuId)
                .onlyQueryValid(false)
                .build();
        Sku sku = skuRepository.queryFromCache(skuQuery);
        if(!"2".equals(String.valueOf(sku.getSpecAttributes().getPeriodUnit()))){
            return null;
        }
        String vipType = sku.getSpecAttributes().getVipType();
        String timelength = sku.getSpecAttributes().getTimeLength();
        return vipTypeToskuIdMap.get(vipType + "_" + timelength);
    }
}
