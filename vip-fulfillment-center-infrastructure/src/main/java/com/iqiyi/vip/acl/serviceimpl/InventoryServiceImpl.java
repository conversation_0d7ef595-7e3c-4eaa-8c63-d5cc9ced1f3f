package com.iqiyi.vip.acl.serviceimpl;

import com.google.common.collect.Maps;
import com.iqiyi.vip.domain.inventory.repository.InventoryRepository;
import com.iqiyi.vip.domain.inventory.service.InventoryService;
import com.iqiyi.vip.domain.order.service.OrderService;
import com.iqiyi.vip.domain.rights.entity.TerminateOrderAggregate;
import com.iqiyi.vip.domain.sku.entity.Sku;
import com.iqiyi.vip.domain.sku.entity.SkuBeforeBuyCheckAggregate;
import com.iqiyi.vip.domain.sku.repository.SkuRepository;
import com.iqiyi.vip.domain.task.DeductStockTask;
import com.iqiyi.vip.dto.order.OrderPaidMsg;
import com.iqiyi.vip.dto.sku.SkuBeforeBuyCheckDetailRes;
import com.iqiyi.vip.dto.sku.SkuQuery;
import com.iqiyi.vip.dto.stock.DeductStockReq;
import com.iqiyi.vip.dto.stock.QueryStockReq;
import com.iqiyi.vip.dto.stock.RevertStockReq;
import com.iqiyi.vip.dto.stock.StockQueryRes;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.RetryEnum;
import com.iqiyi.vip.enums.TradeOrderStatusEnum;
import com.iqiyi.vip.enums.YesOrNoEnum;
import com.iqiyi.vip.exception.BizRuntimeException;
import com.iqiyi.vip.repository.ClusterAsyncTaskManager;
import com.iqiyi.vip.utils.AssertUtils;
import com.iqiyi.vip.utils.OrderUtils;
import com.qiyi.vip.trade.dataservice.client.dto.OrderDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/9/8 16:27
 */
@Slf4j
@Service("inventoryService")
public class InventoryServiceImpl implements InventoryService {

    @Resource
    private InventoryRepository inventoryRepository;
    @Resource
    private SkuRepository skuRepository;
    @Resource
    private ClusterAsyncTaskManager clusterAsyncTaskManager;
    @Resource
    private OrderService orderService;

    /**
     * 是否启用库存中心进行验证
     */
    @Value("${stock.strict.use.switch:0}")
    private Integer stockStrictUseSwitch;

    @Override
    public void deductStockFailRetry(OrderPaidMsg orderPaidMsg) {
        RetryEnum retryEnum = deductStock(orderPaidMsg);
        if (RetryEnum.needRetry(retryEnum.getStatus())) {
            clusterAsyncTaskManager.insertTask(new DeductStockTask(orderPaidMsg));
        }
    }

    @Override
    public RetryEnum deductStock(OrderPaidMsg orderPaidMsg) {
        try {
            if (!orderPaidMsg.needDealOrder() || !TradeOrderStatusEnum.isPaidOrder(orderPaidMsg.getStatus())) {
                return RetryEnum.NO;
            }
            Sku sku = skuRepository.queryFromCache(SkuQuery.builder().skuId(orderPaidMsg.getSkuId()).onlyQueryValid(false).build());
            AssertUtils.notNull(sku, CodeEnum.ERROR_SKU_NULL);
            String stockActCode = OrderUtils.getStockActCode(orderPaidMsg.getRefer());
            //商品限制库存一定要扣减库存，即便商品不限库存，但是活动限制库存也需要扣减库存
            if (YesOrNoEnum.YES.getValue().equals(sku.getSpecAttributes().getInventoryValidFlag()) || StringUtils.isNotBlank(stockActCode)) {
                DeductStockReq deductStockReq = new DeductStockReq();
                deductStockReq.setSkuId(sku.getSkuId());
                if (StringUtils.isNotBlank(stockActCode)) {
                    deductStockReq.setActCode(stockActCode);
                }
                deductStockReq.setOrderId(orderPaidMsg.getOrderCode());
                deductStockReq.setInventoryNum(null == orderPaidMsg.getSkuAmount() ? 1 : orderPaidMsg.getSkuAmount());
                deductStockReq.setOrderCreateTime(orderPaidMsg.getCreateTime().getTime());
                boolean deductStockSuc = inventoryRepository.deductStock(deductStockReq);
                if (!deductStockSuc) {
                    log.error("[deduct stock fail][orderPaidMsg:{}]", orderPaidMsg);
                    if (YesOrNoEnum.YES.getValue().equals(stockStrictUseSwitch)) {
                        throw new BizRuntimeException(CodeEnum.ERROR_STOCK_DEDUCT);
                    }
                }
            }
            return RetryEnum.NO;
        } catch (Exception e) {
            log.error("[Exception][orderPaidMsg:{}]", orderPaidMsg, e);
            return RetryEnum.YES;
        }
    }

    @Override
    public void revertStock(TerminateOrderAggregate terminateOrderAggregate) {
        try {
            if (!YesOrNoEnum.YES.getValue().equals(terminateOrderAggregate.getSku().getSpecAttributes().getInventoryRecoveryFlag())) {
                return;
            }
            RevertStockReq req = new RevertStockReq();
            req.setSkuId(terminateOrderAggregate.getSku().getSkuId());
            req.setInventoryNum(terminateOrderAggregate.getRefundOrderInfo().getSkuAmount());
            req.setOrderId(terminateOrderAggregate.getRefundOrderInfo().getPaidOrderCode());
            OrderDto orderDto = orderService.queryByOrderCode(terminateOrderAggregate.getRefundOrderInfo().getPaidOrderCode());
            AssertUtils.notNull(orderDto, CodeEnum.PAID_ORDER_QUERY_FAIL);
            req.setOrderCreateTime(orderDto.getCreateTime().getTime());
            boolean revertStockSuc = inventoryRepository.revertStock(req);
            if (!revertStockSuc) {
                log.error("[revert stock fail][terminateOrderAggregate:{}]", terminateOrderAggregate);
                if (YesOrNoEnum.YES.getValue().equals(stockStrictUseSwitch)) {
                    throw new BizRuntimeException(CodeEnum.ERROR_STOCK_DEDUCT);
                }
            }
        } catch (Exception e) {
            log.error("[Exception][terminateOrderAggregate:{}]", terminateOrderAggregate, e);
        }
    }

    @Override
    public void checkSkuStock(List<SkuBeforeBuyCheckAggregate> skuBeforeBuyCheckAggregateList) {
        if (CollectionUtils.isEmpty(skuBeforeBuyCheckAggregateList)) {
            return;
        }

        try {
            //1.过滤出需要校验库存的sku
            List<String> needCheckStockSkuIds = skuBeforeBuyCheckAggregateList.stream()
                .filter(entry -> null != entry.getSku().getSpecAttributes() && YesOrNoEnum.YES.getValue()
                    .equals(entry.getSku().getSpecAttributes().getInventoryValidFlag()))
                .map(entry -> entry.getSkuId()).distinct()
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(needCheckStockSkuIds)) {
                return;
            }
            //2.调用库存中心批量查询库存
            List<QueryStockReq> needCheckStockSkuIdList = needCheckStockSkuIds.stream()
                .map(skuId -> {
                    QueryStockReq queryStockReq = new QueryStockReq();
                    queryStockReq.setSkuId(skuId);
                    return queryStockReq;
                }).collect(Collectors.toList());
            List<StockQueryRes> stockQueryResList = inventoryRepository.queryStockBatch(needCheckStockSkuIdList);
            if (CollectionUtils.isEmpty(stockQueryResList)) {
                return;
            }
            Map<String, Integer> skuId2SalesStockQuantity = Maps.newHashMap();
            for (StockQueryRes stockQueryRes : stockQueryResList) {
                skuId2SalesStockQuantity.put(stockQueryRes.getSkuId(), stockQueryRes.getSaleStockQuantity());
            }

            for (SkuBeforeBuyCheckAggregate skuBeforeBuyCheckAggregate : skuBeforeBuyCheckAggregateList) {
                if (null != skuId2SalesStockQuantity.get(skuBeforeBuyCheckAggregate.getSkuId())
                    && skuId2SalesStockQuantity.get(skuBeforeBuyCheckAggregate.getSkuId()) < skuBeforeBuyCheckAggregate.getSkuAmount()) {
                    log.warn("[not enough sku stock][skuBeforeBuyCheckAggregate:{}]", skuBeforeBuyCheckAggregate);
                    if (YesOrNoEnum.YES.getValue().equals(stockStrictUseSwitch)) {
                        skuBeforeBuyCheckAggregate.setCheckResult(new SkuBeforeBuyCheckDetailRes(skuBeforeBuyCheckAggregate.getSkuId(), skuBeforeBuyCheckAggregate.getSkuAmount(), CodeEnum.ERROR_INVENTORY_EXCEED));
                        skuBeforeBuyCheckAggregate.setNeedNextCheck(false);
                    }
                }
            }
        } catch (Exception e) {
            log.error("[Exception][needCheckStockSkuIdList:{}]", skuBeforeBuyCheckAggregateList, e);
        }
    }
}
