package com.iqiyi.vip.acl.serviceimpl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.iqiyi.vip.domain.jd.service.JdService;

/**
 * <AUTHOR>
 * @date 2023/10/9 14:43
 */
@Slf4j
@Service("jdService")
public class JdServiceImpl implements JdService {

    @Value("${jd.giftCode:129}")
    private String jdGiftCode;

    public static final int IQ_ORDER_BUY_JD_PLUS_DAYS_MAX = 365;

    @Override
    public boolean isJdGiftCode(String giftCode) {
        return jdGiftCode.equals(giftCode);
    }

    @Override
    public int jdRecordAmount() {
        return IQ_ORDER_BUY_JD_PLUS_DAYS_MAX;
    }

}
