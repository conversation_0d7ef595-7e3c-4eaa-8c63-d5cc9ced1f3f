package com.iqiyi.vip.acl.serviceimpl;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ScheduledThreadPoolExecutor;

import com.iqiyi.vip.domain.compensate.service.CompensateService;
import com.iqiyi.vip.domain.fulfillment.service.FulfillmentEventService;
import com.iqiyi.vip.enums.CompensateOptEnum;

/**
 * <AUTHOR>
 * @date 2024/2/23 14:31
 */
@Slf4j
@Service("compensateService")
public class CompensateServiceImpl implements CompensateService {

    public static final int WORKER_BATCH_COUNT = 200;
    private static final ScheduledThreadPoolExecutor executeService = new ScheduledThreadPoolExecutor(100);

    @Resource
    private FulfillmentEventService fulfillmentEventService;

    @Override
    public void batchFulOrder(String fileName, List<String> fileLineList, CompensateOptEnum compensateOptEnum) {
        try {
            long startTime = System.currentTimeMillis();
            int size = fileLineList.size();
            log.info("[start][fileName:{},size:{}]", fileName, size);
            List<List<String>> partitionList = Lists.partition(fileLineList, WORKER_BATCH_COUNT);
            CountDownLatch countDownLatch = new CountDownLatch(partitionList.size());
            for (List<String> partition : partitionList) {
                executeService.submit(new BatchFulOrderWorker(countDownLatch, partition, compensateOptEnum));
            }
            try {
                countDownLatch.await();
                log.info("[批量通知完成][fileName:{}][cost:{}ms]", fileName, System.currentTimeMillis() - startTime);
            } catch (InterruptedException e) {
                log.error("[批量通知被中断][fileName:{}][cost:{}ms]", fileName, System.currentTimeMillis() - startTime);
                return;
            }
        } catch (Exception e) {
            log.error("[Exception][批量处理异常][fileName:{}]", fileName, e);
        }
    }

    private class BatchFulOrderWorker implements Runnable {

        private CountDownLatch countDownLatch;
        private List<String> partitionLineList;
        private CompensateOptEnum compensateOptEnum;

        public BatchFulOrderWorker(CountDownLatch countDownLatch, List<String> partitionLineList, CompensateOptEnum compensateOptEnum) {
            this.countDownLatch = countDownLatch;
            this.partitionLineList = partitionLineList;
            this.compensateOptEnum = compensateOptEnum;
        }

        @Override
        public void run() {
            try {
                for (String line : partitionLineList) {
                    if (StringUtils.isBlank(line)) {
                        continue;
                    }
                    log.info("[BatchFulOrderWorker][orderCode:{}]", line);
                    try {
                        if (CompensateOptEnum.RE_FULFILL_ORDER.equals(compensateOptEnum)) {
                            fulfillmentEventService.fulfillOrder(line.trim());
                        } else if (CompensateOptEnum.RE_REFUND_ORDER_RIGHTS.equals(compensateOptEnum)) {
                            fulfillmentEventService.refundOrder(line.trim());
                        }
                    } catch (Exception e) {
                        log.error("[BatchFulOrderWorker fail] orderCode:{}, msg:{} ]", line, e.getMessage(), e);
                    }
                }
            } catch (Exception e) {
                log.error("[Exception]", e);
            } finally {
                this.countDownLatch.countDown();
            }
        }
    }
}
