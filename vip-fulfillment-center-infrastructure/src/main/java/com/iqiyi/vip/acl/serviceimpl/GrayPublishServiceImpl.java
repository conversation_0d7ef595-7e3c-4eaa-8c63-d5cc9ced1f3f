package com.iqiyi.vip.acl.serviceimpl;

import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.iqiyi.vip.domain.graypublish.service.GrayPublishService;
import com.iqiyi.vip.domain.rights.entity.FulfillOrderAggregate;
import com.iqiyi.vip.domain.sku.entity.Sku;
import com.iqiyi.vip.dto.sku.WhiteSkuDTO;
import com.iqiyi.vip.enums.DealModuleEnum;

/**
 * 灰度上线策略
 *
 * <AUTHOR>
 * @date 2023/9/28 16:24
 */
@Slf4j
@Service("grayPublishService")
public class GrayPublishServiceImpl implements GrayPublishService {

    public static final String ALL_WHITE = "allWhite";

    @Value("${white.vipFulfillment.skuList:spu_outside_goods:sku_379294216954777637&sku_372018595203586098,spu_voucher:allWhite}")
    private String whiteFulfillmentSkuIdList;

    @Value("${white.vipFulfillment.catalogIdList:noWhite}")
    private String whiteFulfillmentCatalogIdList;

    @Value("${white.vipFulfillment.uid.list:noWhite}")
    private String whiteUidList;

    /**
     * 白名单验证，返回true表示命中白名单，可以履约
     */
    @Override
    public boolean isFulfillWhiteOrder(FulfillOrderAggregate fulfillOrderAggregate) {
        try {
            //只有vip-worker模块才做具体sku、spu、catalogId白名单验证，其他直接返回可以履约，无白名单限制。
            if (null == fulfillOrderAggregate || null == fulfillOrderAggregate.getDealModule()
                || !DealModuleEnum.VIP_WORKER.equals(fulfillOrderAggregate.getDealModule())) {
                return true;
            }

            //1.uid白名单验证，符合条件的返回可以履约
            if (Splitter.on(",").splitToList(whiteUidList).contains(String.valueOf(fulfillOrderAggregate.getUid()))) {
                return true;
            }
            Sku sku = fulfillOrderAggregate.getSku();
            String spuId = sku.getSpuId();
            String skuId = sku.getSkuId();
            String catalogId = sku.getCatalogId();

            //2.catalogId白名单验证，符合条件的返回可以履约
            if (ALL_WHITE.equals(whiteFulfillmentCatalogIdList)) {
                return true;
            }
            if (Splitter.on(",").splitToList(whiteFulfillmentCatalogIdList).contains(catalogId)) {
                return true;
            }

            //3.skuId,spuId白名单验证，符合条件的返回可以履约
            return isWhiteSku(spuId, skuId);
        } catch (Exception e) {
            log.error("[fulfillOrderAggregate:{}]", fulfillOrderAggregate, e);
            return false;
        }
    }

    public boolean isWhiteSku(String spuId, String skuId) {
        try {
            if (ALL_WHITE.equals(whiteFulfillmentSkuIdList)) {
                return true;
            }
            if ("\"\"".equals(whiteFulfillmentSkuIdList)) {
                return false;
            }
            Map<String, List<String>> spuId2SkuIdList = Splitter.on(",").splitToList(whiteFulfillmentSkuIdList).stream().map(s -> {
                WhiteSkuDTO whiteSkuDTO = new WhiteSkuDTO();
                whiteSkuDTO.setSpuId(Splitter.on(":").splitToList(s).get(0));
                whiteSkuDTO.setSkuIds(Splitter.on("&").splitToList(Splitter.on(":").splitToList(s).get(1)));
                return whiteSkuDTO;
            }).collect(Collectors.toMap(WhiteSkuDTO::getSpuId, WhiteSkuDTO::getSkuIds, (key1, key2) -> key1));
            List<String> skuIdList = spuId2SkuIdList.get(spuId);
            if (CollectionUtils.isEmpty(skuIdList)) {
                return false;
            }
            if (skuIdList.contains(ALL_WHITE)) {
                //如果值配置成allWhite代表全品类白名单
                return true;
            }
            return skuIdList.contains(skuId);
        } catch (Exception e) {
            log.error("[spuId:{},skuId:{},whiteFulfillmentSkuIdList:{}]", spuId, skuId, whiteFulfillmentSkuIdList, e);
            return false;
        }
    }
}
