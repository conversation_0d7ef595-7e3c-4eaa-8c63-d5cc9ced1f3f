package com.iqiyi.vip.acl.repository;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

import com.iqiyi.vip.domain.gift.entity.Gift;
import com.iqiyi.vip.domain.gift.repository.GiftRepository;
import com.iqiyi.vip.mapper.GiftMapper;
import com.iqiyi.vip.struct.GiftStructMapper;

/**
 * <AUTHOR>
 * @date 2023/9/7 10:38
 */
@Slf4j
@Repository
public class GiftRepositoryImpl implements GiftRepository {

    @Resource
    private GiftMapper giftMapper;

    @Override
    @Cacheable(value = "queryGiftByCode", cacheManager = "caffeineCacheManager")
    public Gift queryByCodeFromCache(String giftCode) {
        return GiftStructMapper.INSTANCE.po2do(giftMapper.selectByCode(giftCode));
    }
}
