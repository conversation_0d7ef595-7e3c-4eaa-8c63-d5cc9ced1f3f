package com.iqiyi.vip.acl.serviceimpl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.iqiyi.v.eagle.EagleMonitor;
import com.iqiyi.v.eagle.bo.EagleParam;
import com.iqiyi.vip.domain.gift.entity.Gift;
import com.iqiyi.vip.domain.gift.entity.SkuGiftDTO;
import com.iqiyi.vip.domain.gift.service.GiftService;
import com.iqiyi.vip.domain.jd.service.JdService;
import com.iqiyi.vip.domain.monitors.service.MonitorService;
import com.iqiyi.vip.domain.outgateway.service.OutGatewayService;
import com.iqiyi.vip.domain.rights.entity.ReceiveRightRecord;
import com.iqiyi.vip.domain.rights.repository.ReceiveRecordRepository;
import com.iqiyi.vip.domain.sku.entity.Sku;
import com.iqiyi.vip.domain.sku.repository.SkuRepository;
import com.iqiyi.vip.domain.spufulfillment.repository.SpuFulfillmentRepository;
import com.iqiyi.vip.domain.urlconfig.entity.UrlConfig;
import com.iqiyi.vip.domain.urlconfig.repository.UrlConfigRepository;
import com.iqiyi.vip.dto.rights.ReceiveRecordByOrderCodeQry;
import com.iqiyi.vip.dto.sku.SkuBatchQuery;
import com.iqiyi.vip.enums.EagleParamNameEnum;
import com.iqiyi.vip.enums.MonitorTypeEnum;
import com.iqiyi.vip.enums.ReceiveRightRecordRefundStatus;
import com.iqiyi.vip.enums.RightsRecordSendStatusEnum;
import com.iqiyi.vip.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 监控相关
 *
 * <AUTHOR>
 * @date 2023/12/14 13:58
 */
@Slf4j
@Service("monitorService")
public class MonitorServiceImpl implements MonitorService {

    @Resource
    private ReceiveRecordRepository receiveRecordRepository;
    @Resource
    private SkuRepository skuRepository;
    @Resource
    private SpuFulfillmentRepository spuFulfillmentRepository;
    @Resource
    private GiftService giftService;
    @Resource
    private UrlConfigRepository urlConfigRepository;
    @Resource
    private OutGatewayService outGatewayService;
    @Resource
    private JdService jdService;

    @Value("${orderOpenFail.monitor.res.length:50}")
    private Integer orderOpenFailMonitorResLength;

    @Value("${partnerGoodsSend.monitor.noNeedAlarm:##aaa##bbb##}")
    private String noNeedAlarmResponseConfig;

    @Value("${record.monitor.time:2024-01-01 00:00:00}")
    private String recordMonitorTime;

    @Override
    public boolean orderOpenFailEagleMonitor(ReceiveRecordByOrderCodeQry orderQry) {
        try {
            ReceiveRightRecord receiveRightRecord = receiveRecordRepository.queryByOrderCode(orderQry);
            if (null == receiveRightRecord) {
                log.warn("[receiveRightRecord null][orderQry:{}]", orderQry);
                return true;
            }
            List<MonitorTypeEnum> monitorTypeEnumList = getMonitorType(receiveRightRecord);
            if (CollectionUtils.isEmpty(monitorTypeEnumList)) {
                log.info("[monitorTypeList is empty][orderQry:{}]", orderQry);
                return true;
            }
            String skuId = receiveRightRecord.getSkuId();
            if (StringUtils.isBlank(skuId)) {
                log.info("[skuId is blank ignore][orderQry:{}]", orderQry);
                return true;
            }
            if (null != receiveRightRecord.getUpdateTime()
                && receiveRightRecord.getUpdateTime().getTime() < DateUtils.str2LongTime(recordMonitorTime, "yyyy-MM-dd HH:mm:ss")) {
                log.info("[old record ignore monitor][orderQry:{}]", orderQry);
                return true;
            }
            Map<String, Sku> skuMap = skuRepository.batchQueryFromCache(SkuBatchQuery.builder().skuIds(skuId).onlyQueryValid(false).build());
            Sku sku = skuMap.get(skuId);
            if (null == sku) {
                log.error("[sku is null][orderQry:{}]", orderQry);
                return false;
            }
            SkuGiftDTO skuGiftDTO = sku.obtainSkuGiftDTO(spuFulfillmentRepository);
            if (null == skuGiftDTO) {
                log.error("[skuGiftDTO is null][orderQry:{}]", orderQry);
                return false;
            }
            //gift
            Gift gift = giftService.queryByCode(skuGiftDTO.getGiftCode());
            if (null == gift) {
                log.error("[gift is null][orderQry:{}]", orderQry);
                return false;
            }
            //正单监控上报
            if (monitorTypeEnumList.contains(MonitorTypeEnum.PAID_ORDER)) {
                Long sendNotifyUrlId = gift.getSendNotifyUrlId();
                if (null == sendNotifyUrlId) {
                    log.error("[sendNotifyUrlId is null][orderQry:{}]", orderQry);
                    return false;
                }
                UrlConfig urlConfig = urlConfigRepository.queryByIdFromCache(gift.getSendNotifyUrlId());
                if (null == urlConfig || StringUtils.isBlank(urlConfig.getSyncUrl())) {
                    log.error("[urlConfig is null][orderQry:{}]", orderQry);
                    return false;
                }
                String response = receiveRightRecord.getSendRes();
                if (needAlarm(response, gift.getCode())) {
                    uploadOrderOpenFailEagleMonitor(urlConfig.getSyncUrl(), response, sku, receiveRightRecord.getSendStatus());
                }
            }
            //退单监控上报
            if (monitorTypeEnumList.contains(MonitorTypeEnum.REFUND_ORDER)) {
                Long refundNotifyUrlId = gift.getRefundNotifyUrlId();
                if (null == refundNotifyUrlId) {
                    log.error("[refundNotifyUrlId is null][orderQry:{}]", orderQry);
                    return false;
                }
                UrlConfig urlConfig = urlConfigRepository.queryByIdFromCache(gift.getRefundNotifyUrlId());
                if (null == urlConfig || StringUtils.isBlank(urlConfig.getSyncUrl())) {
                    log.error("[urlConfig is null][orderQry:{}]", orderQry);
                    return false;
                }
                String response = receiveRightRecord.getRefundRes();
                if (needAlarm(response, gift.getCode())) {
                    uploadOrderOpenFailEagleMonitor(urlConfig.getSyncUrl(), response, sku, receiveRightRecord.getSendStatus());
                }
            }
            return true;
        } catch (Exception e) {
            log.error("[Exception][orderQry:{}]", orderQry, e);
            return false;
        }
    }

    @Override
    public boolean needAlarm(String response, String giftCode) {
        try {
            if (StringUtils.isBlank(response)) {
                return true;
            }

            List<String> noNeedAlarmResponse = Lists.newArrayList();
            noNeedAlarmResponse.add("{\"ret\":10031,\"msg\":\"用户不存在\",\"httpStatusCode\":\"200\"}");
            noNeedAlarmResponse.add("{\"ret\":10047,\"msg\":\"手机号格式错误\",\"httpStatusCode\":\"200\"}");
            noNeedAlarmResponse.add("{\"ret\":10032,\"msg\":\"用户被风控\",\"httpStatusCode\":\"200\"}");
            noNeedAlarmResponse.add("bind phone null");
            noNeedAlarmResponse.add("prepareFail");
            noNeedAlarmResponse.add("qq illegal");
            noNeedAlarmResponse.addAll(Splitter.on("##").omitEmptyStrings().splitToList(noNeedAlarmResponseConfig));

            for (String noNeedAlarm : noNeedAlarmResponse) {
                if (response.contains(noNeedAlarm)) {
                    return false;
                }
            }

            if (jdService.isJdGiftCode(giftCode)) {
                response = response.substring(response.lastIndexOf(",") + 1);
                List<String> jDNoNeedAlarmList = Lists.newArrayList();
                jDNoNeedAlarmList.add("3005");
                jDNoNeedAlarmList.add("2006");
                jDNoNeedAlarmList.add("2003");
                jDNoNeedAlarmList.add("2023");
                jDNoNeedAlarmList.add("2005");
                jDNoNeedAlarmList.add("2007");
                if (jDNoNeedAlarmList.contains(response)) {
                    return false;
                }
            }
        } catch (Exception e) {
            log.error("[Exception][giftCode:{},response:{}]", giftCode, response, e);
        }
        return true;
    }

    private void uploadOrderOpenFailEagleMonitor(String url, String response, Sku sku, Integer sendStatus) {
        //删除response里的空格，鹰眼是按照空格逗号分割数据的，这里将空格移除避免鹰眼无法告警
        response = StringUtils.deleteWhitespace(response);
        if (StringUtils.isBlank(response)) {
            return;
        }
        EagleParam eagleParam = new EagleParam(EagleParamNameEnum.ORDER_OPEN_FAIL_EAGLE_MONITOR.getName())
            .tag("uri", outGatewayService.getOriginalUrlFromCache(url))
            .tag("sendStatus", String.valueOf(sendStatus))
            .tag("response", StringUtils.substring(response, 0, orderOpenFailMonitorResLength))
            .tag("skuId", sku.getSkuId())
            .tag("skuName", sku.getSkuName());
        log.info("[uploadOrderOpenFailEagleMonitor][eagleParam:{}]", JSON.toJSONString(eagleParam));
        EagleMonitor.counterInc(eagleParam);
    }

    private List<MonitorTypeEnum> getMonitorType(ReceiveRightRecord receiveRightRecord) {
        List<MonitorTypeEnum> monitorTypeEnumList = Lists.newArrayList();
        //正单失败，上报监控（未退的说明是正单报错触发的数据变更，才需要告警。发送失败，已退的更新，不需要告警）
        if (RightsRecordSendStatusEnum.isFail(receiveRightRecord.getSendStatus())
            && ReceiveRightRecordRefundStatus.UN_REFUND.getStatus() == receiveRightRecord.getRefundStatus()) {
            monitorTypeEnumList.add(MonitorTypeEnum.PAID_ORDER);
        }
        //退单失败，上报监控
        if (null != receiveRightRecord.getRefundStatus()
            && ReceiveRightRecordRefundStatus.REFUND_FAIL.getStatus() == receiveRightRecord.getRefundStatus()) {
            monitorTypeEnumList.add(MonitorTypeEnum.REFUND_ORDER);
        }
        return monitorTypeEnumList;
    }

}
