package com.iqiyi.vip.acl.repository;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Map;
import java.util.UUID;

import com.iqiyi.vip.component.HttpComponent;
import com.iqiyi.vip.domain.viptag.repository.VipTagRepository;
import com.iqiyi.vip.dto.http.HttpResDTO;
import com.iqiyi.vip.dto.viptag.VipTagSaveCustomTagReq;
import com.iqiyi.vip.dto.viptag.VipTagSaveCustomTagRes;
import com.iqiyi.vip.enums.StatusEnum;
import com.iqiyi.vip.utils.Md5Utils;

/**
 * 标签服务
 *
 * <AUTHOR>
 * @date 2023/9/12 14:08
 */
@Slf4j
@Repository
public class VipTagRepositoryImpl implements VipTagRepository {

    @Value("${viptag.api.baseUrl:http://ai.vip.qiyi.domain/vip-tag}")
    private String vipTagBaseUrl;

    @Value("${viptag.api.eureka.baseUrl:http://vip-tag-plat-dev}")
    private String vipTagEurekaBaseUrl;

    @Value("${viptag.api.source:vip-partner-notification}")
    private String source;

    @Value("${viptag.api.secretKey:yPSD8tq454qUH8uIgappYZDJkkD09TD}")
    private String secretKey;

    @Value("${vipTag.eureka.switch:1}")
    private Integer vipTagEurekaSwitch;

    @Resource
    private HttpComponent httpComponent;

    private boolean eurekaSwitch() {
        return vipTagEurekaSwitch.equals(StatusEnum.VALID.getStatus());
    }

    private String getPreFixUrl() {
        return eurekaSwitch() ? vipTagEurekaBaseUrl : vipTagBaseUrl;
    }

    @Override
    public HttpResDTO saveCustomTag(VipTagSaveCustomTagReq req) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("source", source);
        UUID uuid = UUID.randomUUID();
        String messageId = "rights" + System.currentTimeMillis() + uuid.toString().substring(5);
        params.put("messageId", messageId);
        params.put("tagName", req.getTagName());
        params.put("tagValue", req.getTagValue());
        params.put("uid", req.getUid());
        params.put("busiTime", System.currentTimeMillis());
        params.put("sign", Md5Utils.sign(params, secretKey));

        HttpResDTO<VipTagSaveCustomTagRes> responseDTO = httpComponent.soonPost(eurekaSwitch(),
            getPreFixUrl() + "/v1/saveCustomTag"
            , params, null, new TypeReference<VipTagSaveCustomTagRes>() {
            });
        log.info("[saveCustomTag][req:{}][res:{}]", params, responseDTO);
        return responseDTO;
    }
}
