package com.iqiyi.vip.acl.serviceimpl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.iqiyi.vip.domain.coupon.entity.Coupon;
import com.iqiyi.vip.domain.coupon.repository.CouponRepository;
import com.iqiyi.vip.domain.coupon.service.CouponService;
import com.iqiyi.vip.domain.order.entity.CheckOrderCanTerminateDetail;
import com.iqiyi.vip.domain.rights.entity.ReceiveRightRecord;
import com.iqiyi.vip.domain.rights.repository.ReceiveRecordRepository;
import com.iqiyi.vip.dto.order.CheckOrderCanTerminateDetailRes;
import com.iqiyi.vip.dto.rights.ReceiveRecordByOrderCodeQry;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.CouponStatusEnum;
import com.iqiyi.vip.utils.AssertUtils;
import com.iqiyi.vip.utils.DateUtils;

/**
 * <AUTHOR>
 * @date 2023/9/7 16:12
 */
@Slf4j
@Service("couponService")
public class CouponServiceImpl implements CouponService {

    @Resource
    private ReceiveRecordRepository receiveRecordRepository;
    @Resource
    private CouponRepository couponRepository;

    @Override
    public CheckOrderCanTerminateDetailRes checkCanTerminateRights(CheckOrderCanTerminateDetail req) {
        ReceiveRecordByOrderCodeQry qry = ReceiveRecordByOrderCodeQry.builder()
            .orderCode(req.getReq().getOrderCode())
            .promotionCode(req.getPromotionCode())
            .uid(req.getReq().getUid())
            .build();
        ReceiveRightRecord receiveRightRecord = receiveRecordRepository.queryByOrderCode(qry);
        AssertUtils.notNull(receiveRightRecord, CodeEnum.ERROR_RECEIVE_NOT_EXISTS);
        String couponCode = receiveRightRecord.getCouponCode();
        if (StringUtils.isBlank(couponCode)) {
            CheckOrderCanTerminateDetailRes checkCanRefundDetailResVO = new CheckOrderCanTerminateDetailRes();
            checkCanRefundDetailResVO.setOrderCode(receiveRightRecord.getOrderCode());
            checkCanRefundDetailResVO.setCheckCodeMsgEnum(CodeEnum.SUCCESS);
            checkCanRefundDetailResVO.setCheckMsg("正单未获取到代金券码，可以退单");
            return checkCanRefundDetailResVO;
        }
        Coupon coupon = couponRepository.queryCoupon(receiveRightRecord.getUid(), couponCode, receiveRightRecord.getSkuId());
        AssertUtils.notNull(coupon, CodeEnum.ERROR_SYSTEM);
        CodeEnum checkCodeMsgEnum = CodeEnum.SUCCESS;
        String checkMsg = "";
        if (!CouponStatusEnum.canRefundStatus(coupon.getStatus())) {
            checkCodeMsgEnum = CodeEnum.ERROR_CAN_NOT_REFUND;
            checkMsg = "代金券状态：" + CouponStatusEnum.ofDesc(coupon.getStatus()) + "不支持退单";
        }
        if (System.currentTimeMillis() > DateUtils.str2LongTime(coupon.getEnd_time(), "yyyy-MM-dd HH:mm:ss")) {
            checkCodeMsgEnum = CodeEnum.ERROR_CAN_NOT_REFUND;
            checkMsg = "代金券已过期，不能退单";
        }
        CheckOrderCanTerminateDetailRes checkCanRefundDetailResVO = new CheckOrderCanTerminateDetailRes();
        checkCanRefundDetailResVO.setOrderCode(receiveRightRecord.getOrderCode());
        checkCanRefundDetailResVO.setCheckCodeMsgEnum(checkCodeMsgEnum);
        checkCanRefundDetailResVO.setCheckMsg(checkMsg);
        return checkCanRefundDetailResVO;
    }


}
