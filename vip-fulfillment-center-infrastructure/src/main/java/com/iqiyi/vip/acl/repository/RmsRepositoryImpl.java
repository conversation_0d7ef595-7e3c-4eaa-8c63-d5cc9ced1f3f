package com.iqiyi.vip.acl.repository;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Repository;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.StringJoiner;

import com.iqiyi.vip.component.HttpComponent;
import com.iqiyi.vip.domain.rms.repository.RmsRepository;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.exception.BizRuntimeException;

/**
 * <AUTHOR>
 * @date 2023/10/12 21:20
 */
@Slf4j
@Repository
public class RmsRepositoryImpl implements RmsRepository {

    /**
     * 我的资产sku列表
     * https://static.iqiyi.com/ext/rms/resources/165726177856441.json
     */
    @Value("${myAsset.sku.rmsUrl:https://static.iqiyi.com/ext/rms/resources/165726177856441.json}")
    private String myAssetSkuRmsUrl;

    @Resource(name = "restTemplate")
    private RestTemplate restTemplate;
    @Resource
    private HttpComponent httpComponent;

    @Cacheable(value = "FULFILL_ASSET_RMS_SKU_IDS", key = "'FULASSETRMSSKUIDS'", cacheManager = "redisCacheManager")
    @Override
    public String queryAssetRmsSkuIdList() {
        StringJoiner stringJoiner = new StringJoiner(",");
        try {
            ResponseEntity<String> responseEntity = httpComponent.getForEntity(myAssetSkuRmsUrl, MapUtils.EMPTY_MAP, restTemplate);
            log.info("[get rms sku][myAssetSkuRmsUrl:{}]", myAssetSkuRmsUrl);
            if (Objects.nonNull(responseEntity) && HttpStatus.OK.equals(responseEntity.getStatusCode())) {
                String body = responseEntity.getBody();
                if (StringUtils.isNoneBlank(body)) {
                    JSONObject jsonObject = JSON.parseObject(body);
                    JSONArray unionMemberRecommendArr = jsonObject.getJSONArray("union_icon_list");
                    if (Objects.nonNull(unionMemberRecommendArr)) {
                        for (Object unionMemberRecommend : unionMemberRecommendArr) {
                            JSONObject data = (JSONObject) unionMemberRecommend;
                            String skuId = data.getString("skuId");
                            skuId = skuId.replaceAll("\\s*", "").replaceAll("\n", "");
                            if (StringUtils.isNoneBlank(skuId)) {
                                List<String> skuIdListStr = Splitter.on(",").splitToList(skuId);
                                for (String skuIdStr : skuIdListStr) {
                                    stringJoiner.add(skuIdStr);
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("[update rms skuId Exception][get][url:{}]", myAssetSkuRmsUrl, e);
            throw new BizRuntimeException(CodeEnum.ERROR_SYSTEM);
        }
        return stringJoiner.toString();
    }

    @CachePut(value = "FULFILL_ASSET_RMS_SKU_IDS", key = "'FULASSETRMSSKUIDS'", cacheManager = "redisCacheManager")
    @Override
    public String syncAssetRmsSkuIds() {
        return queryAssetRmsSkuIdList();
    }
}
