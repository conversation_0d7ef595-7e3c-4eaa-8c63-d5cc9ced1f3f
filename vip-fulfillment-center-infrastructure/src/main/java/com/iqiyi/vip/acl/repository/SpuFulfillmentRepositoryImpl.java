package com.iqiyi.vip.acl.repository;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

import com.iqiyi.vip.domain.spufulfillment.entity.SpuFulfillmentConfig;
import com.iqiyi.vip.domain.spufulfillment.repository.SpuFulfillmentRepository;
import com.iqiyi.vip.mapper.SpuFulfillmentConfigMapper;
import com.iqiyi.vip.struct.SpuFulfillmentStructMapper;

/**
 * <AUTHOR>
 * @date 2023/9/28 11:22
 */
@Slf4j
@Repository
public class SpuFulfillmentRepositoryImpl implements SpuFulfillmentRepository {

    @Resource
    private SpuFulfillmentConfigMapper spuFulfillmentConfigMapper;

    @Override
    @Cacheable(value = "queryBySpuId", cacheManager = "caffeineCacheManager")
    public SpuFulfillmentConfig queryBySpuId(String spuId) {
        return SpuFulfillmentStructMapper.INSTANCE.po2do(spuFulfillmentConfigMapper.selectBySpuId(spuId));
    }

    @Override
    @Cacheable(value = "queryByGiftType", cacheManager = "caffeineCacheManager")
    public SpuFulfillmentConfig queryByGiftType(Integer giftType) {
        return SpuFulfillmentStructMapper.INSTANCE.po2do(spuFulfillmentConfigMapper.selectByGiftType(giftType));
    }
}
