package com.iqiyi.vip.acl.serviceimpl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.iqiyi.vip.domain.outgateway.repository.OutGatewayRepository;
import com.iqiyi.vip.domain.outgateway.service.OutGatewayService;

/**
 * <AUTHOR>
 * @date 2023/12/20 15:43
 */
@Slf4j
@Service("outGatewayService")
public class OutGatewayServiceImpl implements OutGatewayService {

    @Resource
    private OutGatewayRepository outGatewayRepository;

    @Override
    @Cacheable(value = "getOriginalUrl", cacheManager = "caffeineCacheManager")
    public String getOriginalUrlFromCache(String gatewayUrl) {
        return outGatewayRepository.getOriginalUrl(gatewayUrl);
    }
}
