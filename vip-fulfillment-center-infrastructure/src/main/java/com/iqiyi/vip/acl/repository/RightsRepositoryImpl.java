package com.iqiyi.vip.acl.repository;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import com.iqiyi.vip.component.HttpComponent;
import com.iqiyi.vip.domain.rights.repository.RightsRepository;
import com.iqiyi.vip.domain.utils.BeanCovertUtil;
import com.iqiyi.vip.dto.base.BaseResponse;
import com.iqiyi.vip.dto.http.HttpResDTO;
import com.iqiyi.vip.dto.rights.AskPartnerCanBuyByUrlIdDetailRes;
import com.iqiyi.vip.dto.rights.AskPartnerCanBuyByUrlIdReq;
import com.iqiyi.vip.dto.rights.OpenPartnerRightsReq;
import com.iqiyi.vip.dto.rights.PartnerRightsResponse;
import com.iqiyi.vip.dto.rights.TerminateRightsReq;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.RedisKeyEnum;
import com.iqiyi.vip.enums.RetryEnum;
import com.iqiyi.vip.enums.SendStatusEnum;
import com.iqiyi.vip.exception.BizRuntimeException;
import com.iqiyi.vip.utils.Md5Utils;

/**
 * 权益发放模块
 *
 * <AUTHOR>
 * @date 2023/9/11 16:48
 */
@Slf4j
@Repository
public class RightsRepositoryImpl implements RightsRepository {

    @Value("${partner.right.api.secretkey:a1817bcab1f30a30a17777b843e437d9_test}")
    private String partnerRightApiSecretKey;

    @Value("${partner.right.api.sys:cobrand}")
    private String partnerRightApiSys;

    @Value("${partner.right.api.eureka.host:http://vip-partner-rights-api-test}")
    private String partnerRightApiEurekaHost;

    @Resource
    private HttpComponent httpComponent;

    @Resource
    private RedissonClient redissonClient;

    @Override
    public BaseResponse<PartnerRightsResponse> openRights(OpenPartnerRightsReq req) {
        String lockName = RedisKeyEnum.OPEN_PARTNER_RIGHTS.getRedisKey(req.getUid(), req.getOrderCode());
        RLock lock = null;
        try {
            lock = redissonClient.getLock(lockName);
            if (!lock.tryLock()) {
                throw new BizRuntimeException(CodeEnum.ERR_CONCURRENT);
            }
            Map<String, Object> params = BeanCovertUtil.transBean2Map(req);
            putSign(params);
            HttpResDTO<PartnerRightsResponse> responseDTO = httpComponent.post(true,
                partnerRightApiEurekaHost + "/userRights/openRightsByUrlId"
                , params, null, new TypeReference<PartnerRightsResponse>() {
                });
            return convertBaseResponse(params, responseDTO);
        } finally {
            if ((lock != null) && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public BaseResponse<PartnerRightsResponse> terminateRights(TerminateRightsReq req) {
        Map<String, Object> params = BeanCovertUtil.transBean2Map(req);
        putSign(params);
        HttpResDTO<PartnerRightsResponse> responseDTO = httpComponent.post(true,
            partnerRightApiEurekaHost + "/userRights/terminateRightsBySkuId"
            , params, null, new TypeReference<PartnerRightsResponse>() {
            });
        return convertBaseResponse(params, responseDTO);
    }

    private BaseResponse<PartnerRightsResponse> convertBaseResponse(Map<String, Object> req, HttpResDTO<PartnerRightsResponse> httpResDTO) {
        if (null == httpResDTO || null == httpResDTO.getData()) {
            log.error("[http fail][req:{}]", req);
            return BaseResponse.create(CodeEnum.HTTP_ERROR, RetryEnum.YES);
        }
        PartnerRightsResponse partnerRightsResponse = httpResDTO.getData();
        if (null == partnerRightsResponse || null == partnerRightsResponse.getSendStatus()) {
            log.error("[sendStatus null][req:{}]", req);
            return BaseResponse.create(CodeEnum.HTTP_ERROR, RetryEnum.YES);
        }

        BaseResponse<PartnerRightsResponse> response = new BaseResponse(httpResDTO.getCode(), httpResDTO.getMsg());
        response.setData(partnerRightsResponse);
        Boolean retry = SendStatusEnum.needRetry(partnerRightsResponse.getSendStatus());
        response.setRetry(BooleanUtils.toInteger(retry));
        return response;
    }

    @Override
    public List<AskPartnerCanBuyByUrlIdDetailRes> batchAskPartnerCanBuyByUrlId(AskPartnerCanBuyByUrlIdReq req) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpResDTO<List<AskPartnerCanBuyByUrlIdDetailRes>> responseDTO = httpComponent.fastPost(true,
            partnerRightApiEurekaHost + "/userRights/batchAskPartnerCanBuyByUrlId"
            , JSON.toJSONString(req), headers, new TypeReference<List<AskPartnerCanBuyByUrlIdDetailRes>>() {
            });
        if (null == responseDTO || !CodeEnum.SUCCESS.getCode().equals(responseDTO.getCode())) {
            log.error("[call fail][req:{},response:{}]", req, responseDTO);
            throw new BizRuntimeException(CodeEnum.HTTP_ERROR);
        }
        return responseDTO.getData();
    }

    private Map<String, Object> putSign(Map<String, Object> param) {
        param.put("timestamp", System.currentTimeMillis());
        param.put("sys", partnerRightApiSys);
        param.put("sign", Md5Utils.signNotNull(param, partnerRightApiSecretKey));
        return param;
    }
}
