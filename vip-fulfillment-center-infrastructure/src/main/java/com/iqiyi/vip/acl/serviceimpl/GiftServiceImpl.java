package com.iqiyi.vip.acl.serviceimpl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.iqiyi.vip.domain.gift.entity.Gift;
import com.iqiyi.vip.domain.gift.repository.GiftRepository;
import com.iqiyi.vip.domain.gift.service.GiftService;
import com.iqiyi.vip.domain.sku.entity.Sku;
import com.iqiyi.vip.domain.spufulfillment.repository.SpuFulfillmentRepository;

/**
 * <AUTHOR>
 * @date 2023/9/7 10:33
 */
@Slf4j
@Service("giftService")
public class GiftServiceImpl implements GiftService {

    @Resource
    private GiftRepository giftRepository;
    @Resource
    private SpuFulfillmentRepository spuFulfillmentRepository;

    @Override
    public Gift queryBySkuId(Sku sku) {
        return queryByCode(sku.obtainGiftCode(spuFulfillmentRepository));
    }

    @Override
    public Gift queryByCode(String giftCode) {
        if (StringUtils.isBlank(giftCode)) {
            return null;
        }
        return giftRepository.queryByCodeFromCache(giftCode);
    }
}
