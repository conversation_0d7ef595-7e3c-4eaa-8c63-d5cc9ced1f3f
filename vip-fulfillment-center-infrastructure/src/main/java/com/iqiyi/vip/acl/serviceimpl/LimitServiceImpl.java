package com.iqiyi.vip.acl.serviceimpl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.iqiyi.vip.domain.goods.entity.Goods;
import com.iqiyi.vip.domain.goods.repository.GoodsRepository;
import com.iqiyi.vip.domain.jd.service.JdService;
import com.iqiyi.vip.domain.limit.entity.GiftLimit;
import com.iqiyi.vip.domain.limit.entity.PurchaseLimit;
import com.iqiyi.vip.domain.limit.factory.PurchaseLimitFactory;
import com.iqiyi.vip.domain.limit.repository.GiftLimitRepository;
import com.iqiyi.vip.domain.limit.service.LimitService;
import com.iqiyi.vip.domain.rights.entity.FulfillOrderAggregate;
import com.iqiyi.vip.domain.rights.entity.ReceiveRightRecord;
import com.iqiyi.vip.domain.rights.repository.RightsRecordRepository;
import com.iqiyi.vip.domain.sku.entity.Sku;
import com.iqiyi.vip.domain.sku.entity.SkuBeforeBuyCheckAggregate;
import com.iqiyi.vip.domain.sku.factory.SkuFactory;
import com.iqiyi.vip.dto.rights.ReceiveRightRecordQryCon;
import com.iqiyi.vip.dto.sku.SkuBeforeBuyCheckDetailRes;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.YesOrNoEnum;
import com.iqiyi.vip.exception.BizRuntimeException;

/**
 * 限购
 *
 * <AUTHOR>
 * @date 2023/9/1 16:51
 */
@Slf4j
@Service("limitService")
public class LimitServiceImpl implements LimitService {

    @Resource
    private RightsRecordRepository rightsRecordRepository;
    @Resource
    private GiftLimitRepository giftLimitRepository;
    @Resource
    private GoodsRepository goodsRepository;
    @Resource
    private JdService jdService;

    @Override
    public void checkPurchaseLimit(FulfillOrderAggregate fulfillOrderAggregate) {
        List<SkuBeforeBuyCheckAggregate> skuBeforeBuyCheckAggregateList = SkuFactory.buildByFulfillOrder(fulfillOrderAggregate);
        checkPurchaseLimit(fulfillOrderAggregate.getUid(), skuBeforeBuyCheckAggregateList);
        SkuBeforeBuyCheckDetailRes checkResult = skuBeforeBuyCheckAggregateList.get(0).getCheckResult();
        if (null != checkResult && YesOrNoEnum.NO.getValue().equals(checkResult.getCanBuy())) {
            log.info("Reach the purchase limit. req:{}, check result:{}", fulfillOrderAggregate, checkResult);
            throw new BizRuntimeException(CodeEnum.ERROR_TIMES_LIMIT);
        }
    }

    @Override
    public void checkPurchaseLimit(Long uid, List<SkuBeforeBuyCheckAggregate> skuBeforeBuyCheckAggregateList) {
        try {
            if (CollectionUtils.isEmpty(skuBeforeBuyCheckAggregateList) || null == uid) {
                return;
            }
            boolean queryRightRecordByUidFromDb = false;
            Map<String, List<ReceiveRightRecord>> userPromotionCode2UserRecordList = null;
            Map<String, Boolean> giftCode2CheckSuc = Maps.newHashMap();
            for (SkuBeforeBuyCheckAggregate skuBeforeBuyCheckAggregate : skuBeforeBuyCheckAggregateList) {
                String giftCode = skuBeforeBuyCheckAggregate.getGift().getCode();
                if (StringUtils.isBlank(giftCode)) {
                    continue;
                }
                GiftLimit giftLimit = giftLimitRepository.queryByGiftCode(giftCode);
                if (null != giftLimit) {
                    if (!queryRightRecordByUidFromDb) {
                        userPromotionCode2UserRecordList = rightsRecordRepository.promotionCode2RightsMap(uid);
                        queryRightRecordByUidFromDb = true;
                    }
                    if (MapUtils.isEmpty(userPromotionCode2UserRecordList)) {
                        break;
                    }
                    //1.验gift限购
                    Boolean giftLimitCheckSuc = giftCode2CheckSuc.get(giftCode);
                    if (null == giftLimitCheckSuc) {
                        giftLimitCheckSuc = checkGiftLimit(giftCode, giftLimit, userPromotionCode2UserRecordList);
                        giftCode2CheckSuc.put(giftCode, giftLimitCheckSuc);
                    }
                    if (null != giftLimitCheckSuc && !giftLimitCheckSuc) {
                        skuBeforeBuyCheckAggregate.setCheckResult(new SkuBeforeBuyCheckDetailRes(skuBeforeBuyCheckAggregate.getSkuId(), skuBeforeBuyCheckAggregate.getSkuAmount(), CodeEnum.ERROR_TIMES_LIMIT));
                        skuBeforeBuyCheckAggregate.setNeedNextCheck(false);
                        log.info("Purchase limit encountered for user. uid:{}, skuId:{}, giftLimit:{}", uid, skuBeforeBuyCheckAggregate.getSkuId(), giftLimit);
                        continue;
                    }
                }

                //2.验证sku
                if (jdService.isJdGiftCode(giftCode)) {
                    continue;
                }
                Sku sku = skuBeforeBuyCheckAggregate.getSku();
                Map<String, Boolean> skuId2CheckSuc = Maps.newHashMap();
                Boolean skuLimitCheckSuc = skuId2CheckSuc.get(sku.getSkuId());
                PurchaseLimit purchaseLimit = null;
                if (null == skuLimitCheckSuc) {
                    purchaseLimit = PurchaseLimitFactory.buildPurchaseLimit(sku);
                    if (null == purchaseLimit.getLimitMaxCount()) {
                        //不限购
                        skuLimitCheckSuc = true;
                    } else {
                        List<ReceiveRightRecord> userSkuRights;
                        if (queryRightRecordByUidFromDb) {
                            userSkuRights = MapUtils.isEmpty(userPromotionCode2UserRecordList) ? Collections.EMPTY_LIST
                                : userPromotionCode2UserRecordList.get(sku.getRightRecordPromotionCode());
                        } else {
                            userSkuRights = rightsRecordRepository.queryByConFromMySql(ReceiveRightRecordQryCon.builder()
                                .uid(uid)
                                .promotionCode(sku.getRightRecordPromotionCode())
                                .build());
                        }
                        skuLimitCheckSuc = purchaseLimit.checkPurchaseLimit(userSkuRights);
                    }
                    skuId2CheckSuc.put(sku.getSkuId(), skuLimitCheckSuc);
                }
                if (null != skuLimitCheckSuc && !skuLimitCheckSuc) {
                    skuBeforeBuyCheckAggregate.setCheckResult(new SkuBeforeBuyCheckDetailRes(skuBeforeBuyCheckAggregate.getSkuId(), skuBeforeBuyCheckAggregate.getSkuAmount(), CodeEnum.ERROR_TIMES_LIMIT));
                    skuBeforeBuyCheckAggregate.setNeedNextCheck(false);
                    log.info("Purchase limit encountered for user. uid:{}, skuId:{}, purchaseLimit:{}", uid, skuBeforeBuyCheckAggregate.getSkuId(), purchaseLimit);
                }
            }
        } catch (Exception e) {
            log.error("[Exception][skuBeforeBuyCheckAggregateList:{}]", skuBeforeBuyCheckAggregateList, e);
        }
    }

    private Boolean checkGiftLimit(String giftCode, GiftLimit giftLimit, Map<String, List<ReceiveRightRecord>> promotionCode2UserRecordList) {
        if (MapUtils.isEmpty(promotionCode2UserRecordList)) {
            return true;
        }
        if (null == giftLimit) {
            return true;
        }
        //查找ReceiveRightRecord的promotionCode字段值
        List<Goods> goodsList = goodsRepository.queryByGiftCode(giftCode);
        if (CollectionUtils.isEmpty(goodsList)) {
            return true;
        }
        List<String> recordPromotionCodeList = goodsList.stream()
            .map(Goods::getPromotionCode)
            .filter(promotionCode -> StringUtils.isNotBlank(promotionCode))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(recordPromotionCodeList)) {
            return true;
        }
        //找到giftCode对应的所有用户记录
        List<ReceiveRightRecord> userGiftRecordList = Lists.newArrayList();
        for (String giftPromotionCode : recordPromotionCodeList) {
            List<ReceiveRightRecord> list = promotionCode2UserRecordList.get(giftPromotionCode);
            if (CollectionUtils.isNotEmpty(list)) {
                userGiftRecordList.addAll(list);
            }
        }
        return giftLimit.checkPurchaseLimit(userGiftRecordList);
    }
}
