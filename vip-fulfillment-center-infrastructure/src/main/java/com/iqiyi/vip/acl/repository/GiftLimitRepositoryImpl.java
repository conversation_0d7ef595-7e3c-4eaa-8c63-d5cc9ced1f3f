package com.iqiyi.vip.acl.repository;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

import com.iqiyi.vip.domain.limit.entity.GiftLimit;
import com.iqiyi.vip.domain.limit.repository.GiftLimitRepository;
import com.iqiyi.vip.mapper.GiftLimitConfigMapper;
import com.iqiyi.vip.struct.GiftLimitConfigStructMapper;

/**
 * <AUTHOR>
 * @date 2023/9/7 18:44
 */
@Slf4j
@Repository
public class GiftLimitRepositoryImpl implements GiftLimitRepository {

    @Resource
    private GiftLimitConfigMapper giftLimitConfigMapper;

    @Override
    @Cacheable(value = "queryByGiftCode", cacheManager = "caffeineCacheManager")
    public GiftLimit queryByGiftCode(String giftCode) {
        return GiftLimitConfigStructMapper.INSTANCE.po2do(giftLimitConfigMapper.selectValidByCode(giftCode));
    }
}
