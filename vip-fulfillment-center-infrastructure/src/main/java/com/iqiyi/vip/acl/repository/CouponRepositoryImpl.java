package com.iqiyi.vip.acl.repository;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Map;

import com.iqiyi.vip.component.HttpComponent;
import com.iqiyi.vip.domain.coupon.entity.Coupon;
import com.iqiyi.vip.domain.coupon.repository.CouponRepository;
import com.iqiyi.vip.dto.http.HttpResDTO;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.utils.AssertUtils;
import com.iqiyi.vip.utils.Md5Utils;

/**
 * <AUTHOR>
 * @date 2023/9/28 15:05
 */
@Slf4j
@Repository
public class CouponRepositoryImpl implements CouponRepository {

    @Value("${coupon.domain.url:http://inter-test.coupons.qiyi.domain}")
    private String couponDomainUrl;

    @Value("${coupon.sign.key:**********}")
    private String couponSignKey;

    @Value("${coupon.serviceProvider:QIYUESP}")
    private String serviceProvider;

    @Resource
    private HttpComponent httpComponent;

    @Override
    public Coupon queryCoupon(Long uid, String coupon, String skuId) {
        AssertUtils.notNull(uid, CodeEnum.ERROR_PARAM);
        AssertUtils.notNull(coupon, CodeEnum.ERROR_PARAM);
        try {
            Map<String, Object> reqParams = Maps.newHashMap();
            reqParams.put("user_id", String.valueOf(uid));
            reqParams.put("coupon", coupon);
            putCommonParam(reqParams, skuId);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            HttpResDTO<Coupon> responseDTO = httpComponent.soonPost(false,
                couponDomainUrl + "/services/coupon/query.action"
                , reqParams, headers, new TypeReference<Coupon>() {
                });
            AssertUtils.notNull(responseDTO, CodeEnum.ERROR_PARAM);
            log.info("[query end][uid:{},coupon:{}]", uid, coupon);
            return responseDTO.getData();
        } catch (Exception e) {
            log.error("[Exception][uid:{},coupon:{}]", uid, coupon, e);
            return null;
        }
    }

    private void putCommonParam(Map<String, Object> reqParams, String skuId) {
        reqParams.put("service_provider", serviceProvider);
        reqParams.put("version", "1.0.0");
        reqParams.put("sign", Md5Utils.sign(reqParams, couponSignKey));
    }

}
