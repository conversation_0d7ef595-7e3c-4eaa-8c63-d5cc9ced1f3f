package com.iqiyi.vip.acl.serviceimpl;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import com.iqiyi.vip.domain.fulfillment.repository.FulfillmentTaskRepository;
import com.iqiyi.vip.domain.fulfillment.service.FulfillmentEventService;
import com.iqiyi.vip.domain.order.service.OrderService;
import com.iqiyi.vip.domain.rights.factory.ReceiveRightRecordFactory;
import com.iqiyi.vip.domain.rights.factory.RightsFactory;
import com.iqiyi.vip.domain.sku.entity.Sku;
import com.iqiyi.vip.domain.sku.repository.SkuRepository;
import com.iqiyi.vip.domain.tool.service.ToolService;
import com.iqiyi.vip.domain.vmc.repository.VmcRepository;
import com.iqiyi.vip.dto.base.BaseResponse;
import com.iqiyi.vip.dto.http.HttpResDTO;
import com.iqiyi.vip.dto.order.PaidOrderInfo;
import com.iqiyi.vip.dto.rights.BatchDeliverOrderReq;
import com.iqiyi.vip.dto.rights.BatchFulfillOrderReq;
import com.iqiyi.vip.dto.rights.BatchNotifyVmcReq;
import com.iqiyi.vip.dto.rights.BatchRefundOrderReq;
import com.iqiyi.vip.dto.rights.PartnerRightsResponse;
import com.iqiyi.vip.dto.rights.RecordByOrderCodeSkuIdQry;
import com.iqiyi.vip.dto.sku.SkuByAttrQry;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.YesOrNoEnum;
import com.iqiyi.vip.exception.BizRuntimeException;
import com.iqiyi.vip.utils.AssertUtils;

import static java.util.concurrent.TimeUnit.SECONDS;

/**
 * <AUTHOR>
 * @date 2024/11/1 18:23
 */
@Slf4j
@Service("toolService")
public class ToolServiceImpl implements ToolService {

    private static final ThreadPoolExecutor batchThreadPoolExecutor = new ThreadPoolExecutor(64, 128, 60, SECONDS, new LinkedBlockingQueue<Runnable>(1000));

    @Resource
    private OrderService orderService;
    @Resource
    private VmcRepository vmcRepository;
    @Resource
    private FulfillmentEventService fulfillmentEventService;
    @Resource
    private SkuRepository skuRepository;
    @Resource
    private FulfillmentTaskRepository fulfillmentTaskRepository;

    @Override
    public List<Sku> batchQuerySkuByAttributeOneTime(SkuByAttrQry req) {
        HttpResDTO<Sku> skuRes = skuRepository.queryByAttribute(req);
        List<Sku> skuList = skuRes.getDataList();
        if (skuRes.getPageInfo().getTotalPage() > 1) {
            for (int i = 2; i <= skuRes.getPageInfo().getTotalPage(); i++) {
                req.setPageNo(i);
                //暂停2秒钟，避免流量过大，商品中心返回被限流了
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    log.error("batchQuerySkuByAttributeOneTime sleep error", e);
                }
                List<Sku> querySkuList = skuRepository.queryByAttribute(req).getDataList();
                skuList.addAll(querySkuList);
            }
        }
        return skuList;
    }

    @Override
    public void batchSaveFulfillConfigBySkuAttrOneTime(SkuByAttrQry req) {
        List<Sku> skuList = batchQuerySkuByAttributeOneTime(req);
        AssertUtils.notEmpty(skuList, CodeEnum.ERROR_SKU_NULL);
        for (Sku sku : skuList) {
            //暂停一下，避免流量过大，商品中心返回被限流了
            try {
                Thread.sleep(50);
            } catch (InterruptedException e) {
                log.error("batchQuerySkuByAttributeOneTime sleep error", e);
            }
            fulfillmentTaskRepository.saveFulfillmentConfigFailRetry(sku.getSkuId(), null);
        }
    }

    @Override
    public void batchSaveFulfillConfigBySkuAttr(SkuByAttrQry req) {
        HttpResDTO<Sku> skuRes = skuRepository.queryByAttribute(req);
        List<Sku> skuList = skuRes.getDataList();
        AssertUtils.notEmpty(skuList, CodeEnum.ERROR_SKU_NULL);
        for (Sku sku : skuList) {
            CompletableFuture.runAsync(() -> fulfillmentTaskRepository.saveFulfillmentConfigFailRetry(sku.getSkuId(), null), batchThreadPoolExecutor);
        }
    }

    @Override
    public void batchSaveFulfillConfig(String skuIds) {
        AssertUtils.notBlank(skuIds, CodeEnum.ERROR_PARAM);
        List<String> skuIdList = Splitter.on(",").omitEmptyStrings().splitToList(skuIds);
        for (String skuId : skuIdList) {
            CompletableFuture.runAsync(() -> fulfillmentTaskRepository.saveFulfillmentConfigFailRetry(skuId, null), batchThreadPoolExecutor);
        }
    }

    /**
     * 批量通知VMC-不会存储数据
     */
    @Override
    public void batchNotifyVmc(BatchNotifyVmcReq req) {
        AssertUtils.notBlank(req.getOrderCodes(), CodeEnum.ERROR_PARAM);
        List<String> orderCodeList = Splitter.on(",").omitEmptyStrings().splitToList(req.getOrderCodes());
        for (String orderCode : orderCodeList) {
            CompletableFuture.runAsync(() -> notifyVmc(orderCode), batchThreadPoolExecutor);
        }
    }

    public void notifyVmc(String orderCode) {
        PaidOrderInfo orderInfo = RightsFactory.buildPaidOrderInfo(orderCode, orderService);
        if (null == orderInfo) {
            throw new BizRuntimeException(CodeEnum.ERROR_RECEIVE_NOT_EXISTS);
        }
        BaseResponse<PartnerRightsResponse> httpResDTO = vmcRepository.openRights(RightsFactory.buildOpenVmcRightsReq(RightsFactory.buildOpenPartnerRightsOrderReq(orderInfo)));
        log.info("[orderCode:{},httpResDTO:{}]", orderCode, httpResDTO);
    }

    /**
     * 批量重试履约
     */
    @Override
    public List<BaseResponse<PartnerRightsResponse>> batchFulfillOrder(BatchFulfillOrderReq req) {
        AssertUtils.notBlank(req.getOrderCodes(), CodeEnum.ERROR_PARAM);
        List<String> orderCodeList = Splitter.on(",").splitToList(req.getOrderCodes());
        List<CompletableFuture<BaseResponse<PartnerRightsResponse>>> futureList = Lists.newArrayList();
        for (String orderCode : orderCodeList) {
            CompletableFuture<BaseResponse<PartnerRightsResponse>> future = CompletableFuture.supplyAsync(() -> fulfillmentEventService.fulfillOrder(orderCode), batchThreadPoolExecutor)
                .exceptionally(e -> {
                    if (e instanceof BizRuntimeException) {
                        BizRuntimeException bizException = (BizRuntimeException) e;
                        return BaseResponse.create(bizException.getCodeEnum());
                    } else {
                        return BaseResponse.create(CodeEnum.ERROR_SYSTEM);
                    }
                });
            futureList.add(future);
        }
        if (!YesOrNoEnum.YES.getValue().equals(req.getSyncDeal())) {
            return Lists.newArrayList(BaseResponse.create(CodeEnum.ERROR_ASYNC_DEAL));
        }

        List<BaseResponse<PartnerRightsResponse>> results = futureList.stream()
            .map(CompletableFuture::join)
            .collect(Collectors.toList());
        log.info("results:{}", results);
        return results;
    }

    /**
     * 批量重试解约
     */
    @Override
    public List<BaseResponse<PartnerRightsResponse>> batchRefundOrder(BatchRefundOrderReq req) {
        AssertUtils.notBlank(req.getRefundOrderCode(), CodeEnum.ERROR_PARAM);
        List<String> orderCodeList = Splitter.on(",").splitToList(req.getRefundOrderCode());
        List<CompletableFuture<BaseResponse<PartnerRightsResponse>>> futureList = Lists.newArrayList();
        for (String orderCode : orderCodeList) {
            //异步处理
            CompletableFuture<BaseResponse<PartnerRightsResponse>> future = CompletableFuture.supplyAsync(() -> fulfillmentEventService.refundOrder(orderCode), batchThreadPoolExecutor);
            futureList.add(future);
        }
        if (!YesOrNoEnum.YES.getValue().equals(req.getSyncDeal())) {
            return Lists.newArrayList(BaseResponse.create(CodeEnum.ERROR_ASYNC_DEAL));
        }

        List<BaseResponse<PartnerRightsResponse>> results = futureList.stream()
            .map(CompletableFuture::join)
            .collect(Collectors.toList());
        log.info("results:{}", results);
        return results;
    }

    /**
     * 批量调用交易deliver接口
     */
    @Override
    public void batchDeliverOrder(BatchDeliverOrderReq req) {
        List<RecordByOrderCodeSkuIdQry> qryList = CollectionUtils.isEmpty(req.getQryList()) ? Lists.newArrayList() : req.getQryList();
        //订单号不为空，直接按照订单号查询
        if (StringUtils.isNotBlank(req.getOrderCodes())) {
            List<String> orderCodeList = Splitter.on(",").omitEmptyStrings().splitToList(req.getOrderCodes());
            for (String orderCode : orderCodeList) {
                PaidOrderInfo orderInfo = RightsFactory.buildPaidOrderInfo(orderCode, orderService);
                if (null == orderInfo) {
                    log.error("[order null][orderCode:{}]", orderCode);
                    continue;
                }
                qryList.add(ReceiveRightRecordFactory.buildQuery(orderInfo));
            }
        }
        AssertUtils.notEmpty(qryList, CodeEnum.ERROR_PARAM);
        for (RecordByOrderCodeSkuIdQry qry : qryList) {
            log.info("[start][qry:{}]", qry);
            CompletableFuture.supplyAsync(() -> orderService.paidOrderCallBack(qry), batchThreadPoolExecutor);
        }
    }
}
