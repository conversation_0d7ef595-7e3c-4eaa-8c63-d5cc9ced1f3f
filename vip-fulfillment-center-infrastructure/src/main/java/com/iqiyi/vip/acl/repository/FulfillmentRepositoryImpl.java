package com.iqiyi.vip.acl.repository;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import com.iqiyi.vip.domain.fulfillment.entity.FulfillmentConfig;
import com.iqiyi.vip.domain.fulfillment.repository.FulfillmentRepository;
import com.iqiyi.vip.mapper.FulfillmentConfigMapper;
import com.iqiyi.vip.struct.FulfillmentConfigStructMapper;

/**
 * <AUTHOR>
 * @date 2023/8/1 17:47
 */
@Slf4j
@Repository
public class FulfillmentRepositoryImpl implements FulfillmentRepository {

    @Resource
    private FulfillmentConfigMapper fulfillmentConfigMapper;

    @Override
    public void save(FulfillmentConfig fulfillmentConfig) {
        com.iqiyi.vip.po.FulfillmentConfig newFulfillmentConfigPo = FulfillmentConfigStructMapper.INSTANCE.do2po(fulfillmentConfig);
        com.iqiyi.vip.po.FulfillmentConfig existFulfillmentConfigPo = fulfillmentConfigMapper.selectBySkuIdAndActCode(newFulfillmentConfigPo.getSkuId(), newFulfillmentConfigPo
            .getActCode());
        if (null == existFulfillmentConfigPo) {
            fulfillmentConfigMapper.insertSelective(newFulfillmentConfigPo);
        } else {
            BeanUtils.copyProperties(newFulfillmentConfigPo, existFulfillmentConfigPo, "id", "createTime","deleteFlag");
            fulfillmentConfigMapper.updateByPrimaryKey(existFulfillmentConfigPo);
        }
        log.info("[end][fulfillmentConfig:{}]", fulfillmentConfig);
    }

    @Override
    public FulfillmentConfig query(String skuId, String actCode) {
        return FulfillmentConfigStructMapper.INSTANCE.po2do(fulfillmentConfigMapper.selectBySkuIdAndActCode(skuId, actCode));
    }

    @Override
    public List<FulfillmentConfig> queryBySkuId(String skuId) {
        return FulfillmentConfigStructMapper.INSTANCE.poList2doList(fulfillmentConfigMapper.selectBySkuId(skuId));
    }

    @Override
    public int updateInvalidBySkuId(String skuId) {
        com.iqiyi.vip.po.FulfillmentConfig record = new com.iqiyi.vip.po.FulfillmentConfig();
        record.setSkuId(skuId);
        record.setDeleteFlag(System.currentTimeMillis());
        int size = fulfillmentConfigMapper.updateInvalidBySkuId(record);
        log.info("[updateInvalidBySkuId][skuId:{},size:{}]", skuId, size);
        return size;
    }
}
