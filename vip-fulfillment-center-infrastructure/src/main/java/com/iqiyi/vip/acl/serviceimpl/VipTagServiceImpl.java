package com.iqiyi.vip.acl.serviceimpl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.StringJoiner;
import java.util.stream.Collectors;

import com.iqiyi.vip.domain.goods.service.GoodsService;
import com.iqiyi.vip.domain.rights.entity.ReceiveRightRecord;
import com.iqiyi.vip.domain.rights.repository.RightsRecordRepository;
import com.iqiyi.vip.domain.viptag.repository.VipTagRepository;
import com.iqiyi.vip.domain.viptag.service.VipTagService;
import com.iqiyi.vip.dto.http.HttpResDTO;
import com.iqiyi.vip.dto.rights.ReceiveRightRecordQryCon;
import com.iqiyi.vip.dto.viptag.VipTagSaveCustomTagReq;
import com.iqiyi.vip.dto.viptag.VipTagSaveCustomTagRes;
import com.iqiyi.vip.dto.viptag.VipTagSaveReq;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.ReceiveRightRecordRefundStatus;
import com.iqiyi.vip.enums.ReceiveStatusEnum;
import com.iqiyi.vip.enums.SendTypeEnum;
import com.iqiyi.vip.enums.TagNameEnum;
import com.iqiyi.vip.utils.AssertUtils;


/**
 * 标签逻辑
 *
 * <AUTHOR>
 * @date 2023/9/12 14:25
 */
@Slf4j
@Service("vipTagService")
public class VipTagServiceImpl implements VipTagService {

    @Resource
    private RightsRecordRepository rightsRecordRepository;
    @Resource
    private VipTagRepository vipTagRepository;
    @Resource
    private GoodsService goodsService;

    @Override
    public boolean saveCustomTag(VipTagSaveReq request) {
        try {
            Long uid = request.getUid();
            Boolean receiveFlag = request.getReceiveFlag();
            AssertUtils.notNull(uid, CodeEnum.ERROR_PARAM);
            List<ReceiveRightRecord> userRights = rightsRecordRepository.queryByConFromMySql(ReceiveRightRecordQryCon.builder().uid(uid).build());
            if (CollectionUtils.isEmpty(userRights)) {
                return true;
            }
            fillSkuId(userRights);
            HttpResDTO<VipTagSaveCustomTagRes> responseDTO;
            //只有领取的时候才更新领取标签
            if (receiveFlag) {
                String receivedSkuList = userRights.stream()
                    .filter(e -> null != e.getReceiveStatus() && ReceiveStatusEnum.RECEIVED.getStatus() == e.getReceiveStatus())
                    .map(record -> getReceivedTagValue(record))
                    .filter(StringUtils::isNotBlank).distinct()
                    .collect(Collectors.joining(","));
                VipTagSaveCustomTagReq req = VipTagSaveCustomTagReq.builder().uid(uid)
                    .tagName(TagNameEnum.RECEIVED.getTagName())
                    .tagValue(receivedSkuList).build();
                //更新已领取权益的标签
                responseDTO = vipTagRepository.saveCustomTag(req);
            } else {
                //更新待领取权益的标签
                String pendingReceiveSkuList = userRights.stream()
                    .filter(record -> null != record.getReceiveType() && SendTypeEnum.DIRECT_SEND.getType() != record.getReceiveType())
                    .filter(record -> isPendingReceive(record))
                    .filter(record -> StringUtils.isNotBlank(record.getSkuId()))
                    .collect(Collectors.groupingBy(ReceiveRightRecord::getSkuId))
                    .values()
                    .stream()
                    .flatMap(e -> Arrays.asList(e.stream()
                        .sorted(Comparator.comparing(ReceiveRightRecord::getReceiveDeadlineTime).reversed())
                        .findFirst()
                        .get()).stream())
                    .collect(Collectors.toList())
                    .stream()
                    .map(record -> getPendingReceiveTagValue(record))
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.joining(","));
                VipTagSaveCustomTagReq req = VipTagSaveCustomTagReq.builder().uid(uid)
                    .tagName(TagNameEnum.PENDING_RECEIVE.getTagName())
                    .tagValue(pendingReceiveSkuList).build();
                responseDTO = vipTagRepository.saveCustomTag(req);
            }
            return null != responseDTO && CodeEnum.SUCCESS.getCode().equals(responseDTO.getCode());
        } catch (Exception e) {
            log.error("[更新标签系统失败]req:{}", request, e);
            return false;
        }
    }

    /**
     * 是否是待领状态
     */
    private boolean isPendingReceive(ReceiveRightRecord record) {
        return Objects.nonNull(record)
            && null != record.getReceiveStatus() && ReceiveStatusEnum.PENDING_RECEIVE.getStatus() == record.getReceiveStatus()
            && !ReceiveRightRecordRefundStatus.isAlreadyRefund(record.getRefundStatus())
            && null != record.getReceiveDeadlineTime()
            && record.getReceiveDeadlineTime().after(new Date());
    }

    /**
     * 待领标签值
     */
    private String getPendingReceiveTagValue(ReceiveRightRecord record) {
        if (Objects.isNull(record)) {
            return StringUtils.EMPTY;
        }
        //将record 转化成sku#时间戳
        StringJoiner joiner = new StringJoiner("#");
        joiner.add(record.getSkuId())
            .add(null == record.getReceiveDeadlineTime() ? StringUtils.EMPTY : String.valueOf(record.getReceiveDeadlineTime().getTime()));
        return joiner.toString();
    }

    /**
     * 已领标签值
     */
    private String getReceivedTagValue(ReceiveRightRecord record) {
        if (Objects.isNull(record) || StringUtils.isBlank(record.getSkuId())) {
            return StringUtils.EMPTY;
        }
        return record.getSkuId();
    }

    private void fillSkuId(List<ReceiveRightRecord> userRights) {
        for (ReceiveRightRecord record : userRights) {
            if (StringUtils.isBlank(record.getSkuId())) {
                record.setSkuId(goodsService.querySkuIdByPromotionCode(record.getPromotionCode()));
            }
        }
    }
}
