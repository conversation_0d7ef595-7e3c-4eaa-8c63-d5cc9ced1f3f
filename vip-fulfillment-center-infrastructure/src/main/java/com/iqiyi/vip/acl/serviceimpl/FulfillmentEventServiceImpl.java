package com.iqiyi.vip.acl.serviceimpl;

import com.iqiyi.solar.config.client.spring.annotation.ConfigJsonValue;
import com.iqiyi.vip.domain.constraint.entity.Constraint;
import com.iqiyi.vip.domain.fulfillment.entity.FulfillmentConfig;
import com.iqiyi.vip.domain.fulfillment.factory.FulfillmentFactory;
import com.iqiyi.vip.domain.fulfillment.repository.FulfillmentRepository;
import com.iqiyi.vip.domain.fulfillment.service.FulfillmentEventService;
import com.iqiyi.vip.domain.gift.service.GiftService;
import com.iqiyi.vip.domain.graypublish.service.GrayPublishService;
import com.iqiyi.vip.domain.jd.service.JdService;
import com.iqiyi.vip.domain.limit.service.LimitService;
import com.iqiyi.vip.domain.order.repository.OrderRepository;
import com.iqiyi.vip.domain.order.service.OrderService;
import com.iqiyi.vip.domain.rights.entity.FulfillOrderAggregate;
import com.iqiyi.vip.domain.rights.entity.ReceiveRightRecord;
import com.iqiyi.vip.domain.rights.entity.TerminateOrderAggregate;
import com.iqiyi.vip.domain.rights.factory.ReceiveRightRecordFactory;
import com.iqiyi.vip.domain.rights.factory.RightsFactory;
import com.iqiyi.vip.domain.rights.repository.ReceiveRecordRepository;
import com.iqiyi.vip.domain.rights.service.RightsService;
import com.iqiyi.vip.domain.ruleengine.entity.RuleCheckReqDto;
import com.iqiyi.vip.domain.ruleengine.entity.RuleEngineRespDto;
import com.iqiyi.vip.domain.ruleengine.repository.RuleEngineRepository;
import com.iqiyi.vip.domain.sku.entity.Sku;
import com.iqiyi.vip.domain.sku.repository.SkuRepository;
import com.iqiyi.vip.domain.spufulfillment.repository.SpuFulfillmentRepository;
import com.iqiyi.vip.domain.task.*;
import com.iqiyi.vip.dto.act.ActMsg;
import com.iqiyi.vip.dto.base.BaseResponse;
import com.iqiyi.vip.dto.order.PaidOrderInfo;
import com.iqiyi.vip.dto.order.RefundOrderInfo;
import com.iqiyi.vip.dto.rights.PartnerRightsResponse;
import com.iqiyi.vip.dto.sku.SkuQuery;
import com.iqiyi.vip.dto.viptag.VipTagSaveReq;
import com.iqiyi.vip.enums.*;
import com.iqiyi.vip.exception.BizRuntimeException;
import com.iqiyi.vip.repository.ClusterAsyncTaskManager;
import com.iqiyi.vip.utils.AssertUtils;
import com.iqiyi.vip.utils.OrderUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/8/1 18:24
 */
@Slf4j
@Service("fulfillmentEventService")
public class FulfillmentEventServiceImpl implements FulfillmentEventService {

    @Resource
    private FulfillmentRepository fulfillmentRepository;
    @Resource
    private SkuRepository skuRepository;
    @Resource
    private ReceiveRecordRepository receiveRecordRepository;
    @Resource
    private GiftService giftService;
    @Resource
    private LimitService limitService;
    @Resource
    private RightsService rightsService;
    @Resource
    private ClusterAsyncTaskManager clusterAsyncTaskManager;
    @Resource
    private JdService jdService;
    @Resource
    private OrderService orderService;
    @Resource
    private SpuFulfillmentRepository spuFulfillmentRepository;
    @Resource
    private GrayPublishService grayPublishService;
    @Resource
    private RuleEngineRepository ruleEngineRepository;
    @Resource
    private OrderRepository orderRepository;

    @Value("${fulfillVipSku:0}")
    private Integer fulfillVipSku;
    @Value("${skipSyncDataCatalogIds:vod,package}")
    private String skipSyncDataCatalogIds;
    @Value("${skipSyncDataSpuIds:spu_cloud_public,spu_cloud_seat}")
    private String skipSyncDataSpuIds;
    @Value("${skipVipPressureOrder:true}")
    private Boolean skipVipPressureOrder;
    @ConfigJsonValue("${ios.pay.type:[98,99,100,303,304,465,466]}")
    private Set<Integer> iosPayType;

    @Override
    public boolean save(String skuId, ActMsg actMsg) {
        try {
            FulfillmentConfig fulfillmentConfig = null;
            if (null != actMsg) {
                //1.加价购类录入履约条件
                fulfillmentConfig = FulfillmentFactory.buildFulfillmentConfig(actMsg);
                skuId = fulfillmentConfig.getSkuId();
            }
            if (StringUtils.isBlank(skuId)) {
                log.error("[skuId null][skuId:{}]", skuId);
                return false;
            }
            SkuQuery skuQuery = new SkuQuery();
            skuQuery.setSkuId(skuId);
            skuQuery.setOnlyQueryValid(false);
            Sku sku = skuRepository.query(skuQuery);
            if (null == sku) {
                log.error("[sku info null][skuId:{}]", skuId);
                return false;
            }
            //不是加价购活动过来的 且 在白名单中，则无需录入fulfillmentConfig配置表
            if (actMsg == null && isInSkipSyncDataSet(sku.getCatalogId(), sku.getSpuId())) {
                log.info("Not need sync sku data. sku:{}, catalogId:{}, spuId:{}, name:{}", sku.getSkuId(), sku.getCatalogId(), sku.getSpuId(), sku.getSkuName());
                return true;
            }

            DealModuleEnum dealModule = DealModuleEnum.WORKER;
            //2.基于商品信息构建履约条件
            if (null == actMsg) {
                //会员类使用vip-worker进行履约
                if (SpuCategoryEnum.VIP.getCategoryId().equals(sku.getCatalogId())) {
                    if (!YesOrNoEnum.YES.getValue().equals(fulfillVipSku)) {
                        log.info("[not fulfill vip sku][skuId:{},sku:{}]", skuId, sku);
                        return true;
                    }
                    log.info("[vip sku start][skuInfo:{}]", sku);
                    dealModule = DealModuleEnum.VIP_WORKER;
                }
                fulfillmentConfig = FulfillmentFactory.buildFulfillmentConfig(sku);
            }

            //3.检查商品是否有对应的giftCode
            String giftCode = sku.obtainGiftCode(spuFulfillmentRepository);
            if (StringUtils.isBlank(giftCode)) {
                //查找不到giftCode自动下线
                List<FulfillmentConfig> existFulfillmentConfigList = fulfillmentRepository.queryBySkuId(skuId);
                if (CollectionUtils.isNotEmpty(existFulfillmentConfigList)) {
                    log.error("[gift null fulfill invalid][sku:{},updateFulfillmentConfigList:{}]", sku, existFulfillmentConfigList);
                    fulfillmentRepository.updateInvalidBySkuId(skuId);
                }
                log.info("[giftCode null ignore][skuId:{}]", skuId);
                return true;
            }

            //4.保存履约条件
            fulfillmentConfig.setCatalogId(sku.getCatalogId());
            fulfillmentConfig.setDealModule(dealModule.getModule());
            fulfillmentRepository.save(fulfillmentConfig);
            log.info("[save fulfillmentConfig success][fulfillmentConfig:{}]", fulfillmentConfig);
            return true;
        } catch (Exception e) {
            log.error("[Exception][skuId:{}]", skuId, e);
            return false;
        }
    }

    @Override
    public void fulfillOrderFailRetry(PaidOrderInfo orderInfo, DealModuleEnum dealModule) {
        BaseResponse<PartnerRightsResponse> response = fulfillOrder(orderInfo, dealModule);
        log.info("[end][orderInfo:{},response:{}]", orderInfo, response);
        if (null == response || YesOrNoEnum.YES.getValue().equals(response.getRetry())) {
            //重试
            incrRetryCount(orderInfo);
            log.info("[retry][insert task][orderInfo:{},response:{}]", orderInfo, response);
            if (DealModuleEnum.VIP_WORKER.equals(dealModule)) {
                //vip-worker履约失败重试task单独拆分，避免与外部合作重试履约任务共用影响消费
                clusterAsyncTaskManager.insertTask(new VipFulfillOrderTask(orderInfo));
            } else {
                clusterAsyncTaskManager.insertTask(new FulfillOrderTask(orderInfo));
            }
        }
    }

    @Override
    public BaseResponse<PartnerRightsResponse> fulfillOrder(String orderCode) {
        AssertUtils.notBlank(orderCode, CodeEnum.ERROR_PARAM);
        PaidOrderInfo orderInfo = RightsFactory.buildPaidOrderInfo(orderCode, orderService);
        if (null == orderInfo) {
            throw new BizRuntimeException(CodeEnum.ERROR_RECEIVE_NOT_EXISTS);
        }
        return fulfillOrder(orderInfo, null);
    }

    @Override
    public BaseResponse<PartnerRightsResponse> fulfillOrder(PaidOrderInfo orderInfo, DealModuleEnum dealModule) {
        try {
            if (!orderInfo.needDealOrder(dealModule, skipVipPressureOrder)) {
                throw new BizRuntimeException(CodeEnum.ERROR_FULFILL_NOT_MATCH);
            }
            if (!TradeOrderStatusEnum.isPaidOrder(orderInfo.getStatus())) {
                throw new BizRuntimeException(CodeEnum.NOT_PAID_ORDER);
            }
            if (null == orderInfo.getSkuAmount()) {
                orderInfo.setSkuAmount(1);
            }

            //查询商品
            SkuQuery skuQuery = SkuQuery.builder().skuId(orderInfo.getSkuId()).onlyQueryValid(false).build();
            Sku sku = skuRepository.queryFromCache(skuQuery);
            AssertUtils.notNull(sku, CodeEnum.ERROR_SKU_NULL);

            //匹配履约条件
            String actCode = OrderUtils.getCombineActCode(orderInfo.getRefer());
            log.info("[start fulfillment][actCode:{},orderCode:{}]", actCode, orderInfo.getOrderCode());
            FulfillmentConfig fulfillmentConfig = null;
            if (needValidateFulfillmentConfig(actCode, sku.getCatalogId(), sku.getSpuId())) {
                fulfillmentConfig = fulfillmentRepository.query(orderInfo.getSkuId(), actCode);
                if (null == fulfillmentConfig) {
                    log.info("[not match][orderCode:{}]", orderInfo.getOrderCode());
                    throw new BizRuntimeException(CodeEnum.ERROR_FULFILL_NOT_MATCH);
                }
                if (null != dealModule && null != fulfillmentConfig.getDealModule()
                    && !dealModule.getModule().equals(fulfillmentConfig.getDealModule())) {
                    log.info("[dealModule not match][orderCode:{}]", orderInfo.getOrderCode());
                    throw new BizRuntimeException(CodeEnum.ERROR_FULFILL_NOT_MATCH);
                }
            }

            //构建请求上下文实体类
            FulfillOrderAggregate fulfillOrderAggregate = RightsFactory.buildFulfillOrderAggregate(orderInfo, sku, giftService, fulfillmentConfig, spuFulfillmentRepository, dealModule);

            log.info("[start][fulfillmentAggregate:{}]", fulfillOrderAggregate);
            //判断是否需要履约
            if (!fulfillOrderAggregate.needFulFill()) {
                log.error("[no needFulFill][orderCode:{},fulfillOrderAggregate:{}]", orderInfo.getOrderCode(), fulfillOrderAggregate);
                throw new BizRuntimeException(CodeEnum.ERROR_FULFILL_NOT_MATCH);
            }

            //白名单订单验证
            if (!grayPublishService.isFulfillWhiteOrder(fulfillOrderAggregate)) {
                log.info("[not white sku][orderCode:{},skuId:{}]", orderInfo.getOrderCode(), orderInfo.getSkuId());
                throw new BizRuntimeException(CodeEnum.ERROR_FULFILL_NOT_MATCH);
            }

            //幂等
            ReceiveRightRecord receiveRightRecord = receiveRecordRepository.queryByOrderCode(ReceiveRightRecordFactory.buildQuery(fulfillOrderAggregate));
            if (null != receiveRightRecord && receiveRightRecord.getSendStatus() != null
                && RightsRecordSendStatusEnum.SEND_SUC.getStatus() == receiveRightRecord.getSendStatus()) {
                log.info("[repeat req][req:{}][record:{}]", fulfillOrderAggregate, receiveRightRecord);
                BaseResponse baseResponse = new BaseResponse(CodeEnum.SUC_CODE_REPEAT);
                baseResponse.setData(RightsFactory.buildOpenPartnerRightsRes(receiveRightRecord));
                return baseResponse;
            }

            boolean checkResult = checkSkuLimit(orderInfo, sku);
            if(checkResult){
                return new BaseResponse(CodeEnum.SUCCESS);
            }

            //手动领取型，有记录即可
            if (null != receiveRightRecord && null != receiveRightRecord.getReceiveType()
                && SendTypeEnum.MANUAL_RECEIVE.getType() == receiveRightRecord.getReceiveType()) {
                log.info("[manual repeat req][req:{}]", fulfillOrderAggregate);
                BaseResponse baseResponse = new BaseResponse(CodeEnum.SUC_CODE_REPEAT);
                baseResponse.setData(RightsFactory.buildOpenPartnerRightsRes(receiveRightRecord));
                return baseResponse;
            }

            //生成权益记录
            if (null == receiveRightRecord) {
                //验证限购
                limitService.checkPurchaseLimit(fulfillOrderAggregate);
                ReceiveRightRecordFactory.create(fulfillOrderAggregate, receiveRecordRepository, rightsService, jdService);
            }

            Constraint constraint = fulfillOrderAggregate.getConstraint();
            //按照发放时机，开权益
            if (SendTypeEnum.DIRECT_SEND.equals(constraint.getSendTypeEnum())) {
                //1.直接发放型
                return rightsService.openRights(RightsFactory.buildOpenPartnerRightsReq(fulfillOrderAggregate));
            } else if (SendTypeEnum.MANUAL_RECEIVE.equals(constraint.getSendTypeEnum())) {
                //2.手动领取型
                return rightsService.manualOpenRights(fulfillOrderAggregate);
            } else if (SendTypeEnum.DIRECT_FAIL_THEN_MANUAL_RECEIVE.equals(constraint.getSendTypeEnum())) {
                //3.直接发放转手动领取型
                return rightsService.openRightsFailThenManual(fulfillOrderAggregate);
            } else {
                log.error("[unKnown sendType][orderCode:{},fulfillmentConfig:{}]", orderInfo.getOrderCode(), fulfillmentConfig);
                throw new BizRuntimeException(CodeEnum.ERROR_SEND_TYPE);
            }
        } catch (BizRuntimeException e) {
            log.info("orderInfo:{}", orderInfo, e);
            return RightsFactory.createOpenRightsRes(e.getCodeEnum());
        } catch (DuplicateKeyException duplicateKeyException) {
            log.error("[duplicate key, orderInfo:{}]", orderInfo, duplicateKeyException);
            return RightsFactory.createOpenRightsRes(CodeEnum.ERR_CONCURRENT);
        } catch (Exception e) {
            log.error("[dealMsg Exception][orderInfo:{}]", orderInfo, e);
            return RightsFactory.createOpenRightsRes(CodeEnum.ERROR_SYSTEM);
        }
    }

    @Override
    public BaseResponse<PartnerRightsResponse> refundOrder(String orderCode) {
        AssertUtils.notBlank(orderCode, CodeEnum.ERROR_PARAM);
        RefundOrderInfo refundOrderInfo = RightsFactory.buildRefundOrderInfo(orderCode, orderService);
        return refundOrder(refundOrderInfo);
    }

    @Override
    public void refundOrderFailRetry(RefundOrderInfo refundOrderInfo) {
        BaseResponse<PartnerRightsResponse> response = refundOrder(refundOrderInfo);
        log.info("[end][orderInfo:{},response:{}]", refundOrderInfo, response);
        if (null == response || YesOrNoEnum.YES.getValue().equals(response.getRetry())) {
            log.info("[retry][insert task][orderInfo:{},response:{}]", refundOrderInfo, response);
            clusterAsyncTaskManager.insertTask(new RefundOrderTask(refundOrderInfo));
        }
    }

    @Override
    public BaseResponse<PartnerRightsResponse> refundOrder(RefundOrderInfo refundOrderInfo) {
        ReceiveRightRecord paidRightRecord = null;
        try {
            if (!refundOrderInfo.needDealOrder()) {
                throw new BizRuntimeException(CodeEnum.ERROR_FULFILL_NOT_MATCH);
            }
            if (!TradeOrderStatusEnum.isRefundStatus(refundOrderInfo.getStatus())) {
                throw new BizRuntimeException(CodeEnum.NOT_REFUND_ORDER);
            }
            FulfillmentConfig fulfillmentConfig = fulfillmentRepository.query(refundOrderInfo.getSkuId(), OrderUtils.getCombineActCode(refundOrderInfo.getRefer()));
            TerminateOrderAggregate terminateOrderAggregate = RightsFactory.buildTerminateOrderAggregate(refundOrderInfo, skuRepository, fulfillmentConfig);
            paidRightRecord = receiveRecordRepository.queryByOrderCode(ReceiveRightRecordFactory.buildQuery(terminateOrderAggregate));
            AssertUtils.notNull(paidRightRecord, CodeEnum.NO_PAID_ORDER);
            if (ReceiveRightRecordRefundStatus.isRefundSuc(paidRightRecord.getRefundStatus())) {
                return RightsFactory.createTerminateRightsRes(CodeEnum.SUC_CODE_REPEAT, paidRightRecord);
            }
            log.info("[start][paidRightRecord:{}]", paidRightRecord);
            paidRightRecord.setIqRefundTime(refundOrderInfo.getRefundTime());
            paidRightRecord.setRefundCode(refundOrderInfo.getRefundOrderCode());
            paidRightRecord.setRefundTime(new Date());

            //配置是无需回收
            if (RefundPartnerType.NOT_REFUND.getType() == terminateOrderAggregate.getRefundConstraint().getRefundPartnerType()) {
                paidRightRecord.setRefundStatus(ReceiveRightRecordRefundStatus.REFUND_OFFLINE_SUC.getStatus());
                receiveRecordRepository.updateByOrderCodeSelective(paidRightRecord);
                return RightsFactory.createTerminateRightsRes(CodeEnum.SUCCESS, paidRightRecord);
            }
            //超过回收期限，不再回收
            if (refundOrderInfo.getRefundTime().after(terminateOrderAggregate.getRefundConstraint().getRefundPartnerDeadline())) {
                paidRightRecord.setRefundStatus(ReceiveRightRecordRefundStatus.REFUND_OFFLINE_SUC.getStatus());
                log.info("[refundTime after refundDeadline][paidRightRecord:{}]", paidRightRecord);
                receiveRecordRepository.updateByOrderCodeSelective(paidRightRecord);
                return RightsFactory.createTerminateRightsRes(CodeEnum.SUCCESS, paidRightRecord);
            }
            // 未领取的手动领取型订单，未开通权益，所以也无需回收权益
            if (null != paidRightRecord.getReceiveType()
                && SendTypeEnum.MANUAL_RECEIVE.getType() == paidRightRecord.getReceiveType()
                && ReceiveStatusEnum.PENDING_RECEIVE.getStatus() == paidRightRecord.getReceiveStatus()) {
                paidRightRecord.setRefundStatus(ReceiveRightRecordRefundStatus.REFUND_SUC.getStatus());
                receiveRecordRepository.updateByOrderCodeSelective(paidRightRecord);
                return RightsFactory.createTerminateRightsRes(CodeEnum.SUCCESS, paidRightRecord);
            }
            receiveRecordRepository.updateByOrderCodeSelective(paidRightRecord);
            //终止权益
            return rightsService.terminateRights(terminateOrderAggregate, paidRightRecord);
        } catch (BizRuntimeException e) {
            log.info("orderInfo:{}", refundOrderInfo, e);
            return RightsFactory.createTerminateRightsRes(e.getCodeEnum(), paidRightRecord);
        } catch (Exception e) {
            log.error("[dealMsg Exception][orderInfo:{}]", refundOrderInfo, e);
            return RightsFactory.createTerminateRightsRes(CodeEnum.ERROR_SYSTEM, paidRightRecord);
        } finally {
            clusterAsyncTaskManager.insertTask(new VipTagTask(VipTagSaveReq.builder().uid(refundOrderInfo.getUid()).receiveFlag(false).build()));
        }
    }

    private void incrRetryCount(PaidOrderInfo orderInfo) {
        log.info("Incr or init retry count. orderCode:{}, currentCount:{}", orderInfo.getOrderCode(), orderInfo.getCurrentRetryCount());
        if (orderInfo.getCurrentRetryCount() == null) {
            orderInfo.setCurrentRetryCount(1);
        } else {
            orderInfo.setCurrentRetryCount(orderInfo.getCurrentRetryCount()+1);
        }
    }

    /**
     * 是否需要校验fulfillmentConfig
     *
     * @param actCode
     * @param catalogId
     * @return
     */
    private boolean needValidateFulfillmentConfig(String actCode, String catalogId, String spuId) {
        //加价购，需要校验fulfillmentConfig
        if (StringUtils.isNotBlank(actCode)) {
            return true;
        }
        //不在白名单中，需要校验fulfillmentConfig
        return !isInSkipSyncDataSet(catalogId, spuId);
    }

    /**
     * 判断是否在集合中，集合为：
     * 无需同步sku信息到fulfillmentConfig表的商品类目
     *
     * @param catalogId
     * @param spuId
     * @return
     */
    private boolean isInSkipSyncDataSet(String catalogId, String spuId) {
        if (StringUtils.isBlank(catalogId) && StringUtils.isBlank(spuId)) {
            return false;
        }
        //获取无需同步sku信息到fulfillmentConfig表的商品类目
        Set<String> catalogIdSet = StringUtils.isBlank(skipSyncDataCatalogIds)
            ? new HashSet<>()
            : Arrays.stream(skipSyncDataCatalogIds.split(",")).map(String::trim).collect(Collectors.toSet());
        //获取无需同步sku信息到fulfillmentConfig表的商品品类
        Set<String> spuIdSet = StringUtils.isBlank(skipSyncDataSpuIds)
                ? new HashSet<>()
                : Arrays.stream(skipSyncDataSpuIds.split(",")).map(String::trim).collect(Collectors.toSet());
        return catalogIdSet.contains(catalogId) || spuIdSet.contains(spuId);
    }

    private boolean checkSkuLimit(PaidOrderInfo orderInfo,Sku sku) {
        Boolean ruleResult = true;
        try {
            if (iosPayType.contains(orderInfo.getPayType())) {
                return false;
            }
            if (StringUtils.isBlank(sku.getSpecAttributes().getBuyLimitRuleCode()) && StringUtils.isBlank(OrderUtils.getActCenterRuleCode(orderInfo.getRefer()))) {
                return false;
            }
            List<String> limitCodeList = new ArrayList<>();
            List<String> skuAmountList = new ArrayList<>();
            String skuAmount = Objects.isNull(orderInfo.getSkuAmount()) ? "1" : String.valueOf(orderInfo.getSkuAmount());
            String buyLimitRuleCode = sku.getSpecAttributes().getBuyLimitRuleCode();
            if (StringUtils.isNotBlank(buyLimitRuleCode)) {
                limitCodeList.add(buyLimitRuleCode);
                skuAmountList.add(skuAmount);
            }
            String actCenterRuleCode = OrderUtils.getActCenterRuleCode(orderInfo.getRefer());
            if(StringUtils.isNotBlank(actCenterRuleCode)){
                limitCodeList.add(actCenterRuleCode);
                skuAmountList.add(skuAmount);
            }
            String limitCode = String.join(",", limitCodeList);
            String addAmount = String.join(",", skuAmountList);

            String deviceId = OrderUtils.getFromFrVersion(orderInfo.getFrVersion(), "d");

            RuleCheckReqDto ruleCheckReqDto = RuleCheckReqDto.builder()
                    .uid(String.valueOf(orderInfo.getUid()))
                    .deviceId(deviceId)
                    .limitCode(limitCode)
                    .account(orderInfo.getAccountId())
                    .addAmount(addAmount)
                    .build();
            List<RuleEngineRespDto> ruleResp = ruleEngineRepository.queryRuleEngine(ruleCheckReqDto);
            ruleResult = ruleResp.stream()
                    .allMatch(dto -> Boolean.FALSE.equals(dto.getTrust()) || Boolean.TRUE.equals(dto.getResult()));
        }
        catch (Exception e){
            log.error("[checkSkuLimit Exception][orderCode:{}]", orderInfo.getOrderCode(), e);
        }
        if(Boolean.FALSE.equals(ruleResult)){
            log.info("[rule engine check failed][orderCode:{}]", orderInfo.getOrderCode());
            String orderCode = StringUtils.isNotBlank(orderInfo.getParentOrderCode()) ? orderInfo.getParentOrderCode() : orderInfo.getOrderCode();
            boolean result = orderRepository.cancel(orderCode);
            if(!result){
                log.error("[cancel order cancel failed][orderCode:{}]", orderInfo.getOrderCode());
                clusterAsyncTaskManager.insertTask(new CancelOrderTask(orderCode));
            }
            return true;
        }
        return false;
    }

}
