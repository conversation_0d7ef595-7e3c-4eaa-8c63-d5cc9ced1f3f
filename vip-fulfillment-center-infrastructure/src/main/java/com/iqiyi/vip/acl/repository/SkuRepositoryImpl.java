package com.iqiyi.vip.acl.repository;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import com.iqiyi.vip.component.HttpComponent;
import com.iqiyi.vip.domain.sku.entity.Sku;
import com.iqiyi.vip.domain.sku.repository.SkuRepository;
import com.iqiyi.vip.domain.utils.BeanCovertUtil;
import com.iqiyi.vip.dto.http.HttpResDTO;
import com.iqiyi.vip.dto.sku.SkuBatchQuery;
import com.iqiyi.vip.dto.sku.SkuByAttrQry;
import com.iqiyi.vip.dto.sku.SkuQuery;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.StatusEnum;
import com.iqiyi.vip.exception.BizRuntimeException;
import com.iqiyi.vip.utils.AssertUtils;
import com.iqiyi.vip.utils.JacksonUtil;
import com.iqiyi.vip.utils.Md5Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Repository;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/8/1 14:21
 */
@Slf4j
@Repository
public class SkuRepositoryImpl implements SkuRepository, ApplicationContextAware {
    private ApplicationContext applicationContext;

    @Value("${sku.eureka.switch:1}")
    private Integer eurekaSwitch;
    @Value("${sku.qsm.admin.switch:true}")
    private Boolean qsmAdminSwitch;

    @Value("${sku.caller:vip-fulfillment-center}")
    private String skuCaller;

    @Value("${sku.sign.key:7d315dcef5aa45788ebe90841c37860b}")
    private String skuSignKey;

    @Value("${sku.url.prefix:http://vip-commodity-center-test/vip-commodity}")
    private String skuUrlPrefix;

    @Value("${sku.http.url.prefix:http://vcc.vip.qiyi.domain/vip-commodity}")
    private String skuHttpUrlPrefix;

    @Value("${sku.admin.qsm.url.prefix:http://vip-commodity-center-admin.qsm.qiyi.middle/vip-commodity-admin}")
    private String skuAdminQsmUrlPrefix;

    @Resource
    private HttpComponent httpComponent;
    @Resource(name = "lbRestTemplate")
    private RestTemplate lbRestTemplate;
    @Resource(name = "fastRestTemplate")
    private RestTemplate fastRestTemplate;

    public static final Integer RETRY_MAX_TIMES = 2;

    @Override
    @CacheRefresh(refresh = 120, stopRefreshAfterLastAccess = 1800)
    @Cached(name = "querySkuFromLocalCache", cacheType = CacheType.LOCAL, cacheNullValue = true)
    public Sku queryFromCache(SkuQuery skuQuery) {
        return query(skuQuery);
    }

    @Override
    public Sku query(SkuQuery skuQuery) {
        SkuBatchQuery skuBatchQuery = new SkuBatchQuery();
        skuBatchQuery.setSkuIds(skuQuery.getSkuId());
        skuBatchQuery.setOnlyQueryValid(skuBatchQuery.isOnlyQueryValid());
        Map<String, Sku> skuIdToDetailMap = batchQuery(skuBatchQuery);
        return MapUtils.isEmpty(skuIdToDetailMap) ? null : skuIdToDetailMap.get(skuQuery.getSkuId());
    }

    @Override
    public Map<String, Sku> batchQueryFromCache(SkuBatchQuery query) {
        Map<String, Sku> skuId2SkuMap = Maps.newHashMap();
        try {
            if (StringUtils.isBlank(query.getSkuIds())) {
                return skuId2SkuMap;
            }
            Set<String> skuIdSet = new HashSet<>(Arrays.asList(query.getSkuIds().split(",")));
            if (CollectionUtils.isEmpty(skuIdSet)) {
                return skuId2SkuMap;
            }
            skuId2SkuMap = skuIdSet.stream()
                    .map(skuId -> new AbstractMap.SimpleEntry<>(skuId,
                            getSelf().queryFromCache(SkuQuery.builder()
                                    .skuId(skuId)
                                    .onlyQueryValid(query.isOnlyQueryValid())
                                    .build())))
                    .filter(entry -> entry.getValue() != null)
                    .collect(Collectors.toMap(Map.Entry::getKey, AbstractMap.SimpleEntry::getValue));
            if (MapUtils.isEmpty(skuId2SkuMap)) {
                return Maps.newHashMap();
            }
        } catch (Exception e) {
            log.error("[Batch Query from cache failed. param:{}]", query, e);
        }
        return skuId2SkuMap;
    }

    /**
     * 查询商品详情 http://wiki.qiyi.domain/pages/viewpage.action?pageId=1322357420
     */
    @Override
    public Map<String, Sku> batchQuery(SkuBatchQuery skuQuery) {
        AssertUtils.notBlank(skuQuery.getSkuIds(), CodeEnum.ERROR_PARAM);
        try {
            Map<String, Object> reqParams = Maps.newHashMap();
            reqParams.put("skuIds", skuQuery.getSkuIds());
            reqParams.put("validFlag", skuQuery.isOnlyQueryValid());
            putCommonParam(reqParams);
            HttpResDTO<Map> response = httpComponent.soonPostJson(eurekaSwitch,
                getBaseUrl() + "/basicAndSku/sku/batchQuery", reqParams, new TypeReference<Map>() {
                });
            if (null == response) {
                for (int i = 1; i <= RETRY_MAX_TIMES; i++) {
                    response = httpComponent.soonPostJson(eurekaSwitch,
                        getBaseUrl() + "/basicAndSku/sku/batchQuery", reqParams, new TypeReference<Map>() {
                        });
                    if (null != response) {
                        break;
                    }
                }
            }
            if (null != response && CodeEnum.SUCCESS.getCode().equals(response.getCode())) {
                Map<String, Sku> resultMap = Maps.newHashMap();
                Map<String, LinkedHashMap> res = response.getData();
                for (Map.Entry<String, LinkedHashMap> entry : res.entrySet()) {
                    if (null != entry.getValue()) {
                        resultMap.put(entry.getKey(), JacksonUtil.convertValue(entry.getValue(), Sku.class));
                    } else {
                        resultMap.put(entry.getKey(), null);
                    }
                }
                return resultMap;
            }
            log.error("[query fail][skuQuery:{},response:{}]", skuQuery, response);
        } catch (Exception e) {
            log.error("[Exception][skuQuery:{}]", skuQuery, e);
        }
        throw new BizRuntimeException(CodeEnum.HTTP_ERROR);
    }

    private void putCommonParam(Map<String, Object> reqParams) {
        reqParams.put("caller", skuCaller);
        reqParams.put("sign", Md5Utils.sign(reqParams, skuSignKey));
    }

    private String getBaseUrl() {
        if (eurekaSwitch.equals(StatusEnum.VALID.getStatus())) {
            return skuUrlPrefix;
        }
        return skuHttpUrlPrefix;
    }

    /**
     * 根据属性查询SKU(分页) https://iq.feishu.cn/wiki/KfPCwadlRi1B5nkzOEGcQ1YlnEa?open_in_browser=true
     */
    @Override
    public HttpResDTO<Sku> queryByAttribute(SkuByAttrQry skuByAttrQry) {
        try {
            Map<String, Object> reqParams = BeanCovertUtil.transBean2Map(skuByAttrQry);
            Map<String, Object> signParams = Maps.newHashMap();
            signParams.put("pageNo", skuByAttrQry.getPageNo());
            signParams.put("pageSize", skuByAttrQry.getPageSize());
            signParams.put("caller", skuCaller);
            reqParams.put("sign", Md5Utils.sign(signParams, skuSignKey));
            reqParams.put("caller", skuCaller);
            ResponseEntity<String> responseEntity = httpComponent.postJsonForResponseEntity(
                getAdminBaseUrl() + "/sku/queryByAttribute", reqParams, getAdminRestTemplate());
            if (responseEntity.getStatusCode() == HttpStatus.OK) {
                HttpResDTO result = JacksonUtil.json2obj(responseEntity.getBody(), HttpResDTO.class);
                List<Sku> skuList = JacksonUtil.convertValue(result.getDataList(), new TypeReference<List<Sku>>() {
                });
                result.setDataList(skuList);
                return result;
            }
            return HttpResDTO.create(CodeEnum.HTTP_ERROR);
        } catch (Exception e) {
            log.error("[Exception][req:{}]", skuByAttrQry, e);
            throw new BizRuntimeException(CodeEnum.HTTP_ERROR);
        }
    }

    private String getAdminBaseUrl() {
        if (qsmAdminSwitch) {
            return skuAdminQsmUrlPrefix;
        }
        return skuUrlPrefix;
    }

    private RestTemplate getAdminRestTemplate() {
        if (qsmAdminSwitch) {
            return fastRestTemplate;
        }
        return lbRestTemplate;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    private SkuRepository getSelf() {
        return applicationContext.getBean(SkuRepository.class);
    }

}
