package com.iqiyi.vip.acl.repository;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import com.iqiyi.vip.component.HttpComponent;
import com.iqiyi.vip.domain.inventory.repository.InventoryRepository;
import com.iqiyi.vip.domain.utils.BeanCovertUtil;
import com.iqiyi.vip.dto.http.HttpResDTO;
import com.iqiyi.vip.dto.stock.BatchQueryStockReq;
import com.iqiyi.vip.dto.stock.DeductStockReq;
import com.iqiyi.vip.dto.stock.QueryStockReq;
import com.iqiyi.vip.dto.stock.RevertStockReq;
import com.iqiyi.vip.dto.stock.StockQueryRes;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.YesOrNoEnum;
import com.iqiyi.vip.exception.BizRuntimeException;
import com.iqiyi.vip.utils.Md5Utils;

/**
 * 与库存中心交互 http://atlas.qiyi.domain/system/interface/list?systemId=64e41e0cc9e77c001140d35b&branch=master
 *
 * <AUTHOR>
 * @date 2023/9/8 16:49
 */
@Slf4j
@Repository
public class InventoryRepositoryImpl implements InventoryRepository {

    @Value("${stock.api.sys:fulfillment-center}")
    private String stockApiSys;

    @Value("${stock.api.signKey:123456}")
    private String stockApiSignKey;

    @Value("${stock.api.eurekaHost:http://VIP-INVENTORY-CENTER-API}")
    private String stockApiEurekaHost;

    @Value("${stock.call:0}")
    private Integer stockCall;

    @Resource
    private HttpComponent httpComponent;

    public static final Integer RETRY_TIMES = 3;

    /**
     * 扣减库存
     */
    @Override
    public boolean deductStock(DeductStockReq req) {
        try {
            if (!YesOrNoEnum.YES.getValue().equals(stockCall)) {
                log.info("[notCallStock][req:{}]", req);
                return true;
            }
            Map<String, Object> params = BeanCovertUtil.transBean2Map(req);
            putSign(params);
            HttpResDTO response = httpComponent.soonPost(true, stockApiEurekaHost + "/stock/deduct"
                , params, null, new TypeReference<Map>() {
                });
            if (null == response) {
                for (int i = 0; i < RETRY_TIMES; i++) {
                    response = httpComponent.soonPost(true, stockApiEurekaHost + "/stock/deduct"
                        , params, null, new TypeReference<Map>() {
                        });
                    if (null != response) {
                        break;
                    }
                }
            }
            if (null == response) {
                return false;
            }
            return CodeEnum.SUCCESS.getCode().equals(response.getCode())
                || CodeEnum.SUC_CODE_REPEAT.getCode().equals(response.getCode());
        } catch (Exception e) {
            log.error("[Exception][req:{}]", req, e);
            return false;
        }
    }

    /**
     * 回滚库存
     */
    @Override
    public boolean revertStock(RevertStockReq req) {
        try {
            if (!YesOrNoEnum.YES.getValue().equals(stockCall)) {
                log.info("[notCallStock][req:{}]", req);
                return true;
            }
            Map<String, Object> params = BeanCovertUtil.transBean2Map(req);
            putSign(params);
            HttpResDTO response = httpComponent.soonPost(true, stockApiEurekaHost + "/stock/revert"
                , params, null, new TypeReference<Map>() {
                });
            if (null == response) {
                for (int i = 0; i < RETRY_TIMES; i++) {
                    response = httpComponent.soonPost(true, stockApiEurekaHost + "/stock/revert"
                        , params, null, new TypeReference<Map>() {
                        });
                    if (null != response) {
                        break;
                    }
                }
            }
            if (null == response) {
                return false;
            }
            return CodeEnum.SUCCESS.getCode().equals(response.getCode())
                || CodeEnum.SUC_CODE_REPEAT.getCode().equals(response.getCode());
        } catch (Exception e) {
            log.error("[Exception][req:{}]", req, e);
            return false;
        }
    }

    @Override
    public List<StockQueryRes> queryStockBatch(List<QueryStockReq> req) {
        if (!YesOrNoEnum.YES.getValue().equals(stockCall)) {
            log.info("[notCallStock][req:{}]", req);
            return Collections.EMPTY_LIST;
        }
        if (CollectionUtils.isEmpty(req)) {
            return Collections.EMPTY_LIST;
        }
        BatchQueryStockReq batchQueryStockReq = new BatchQueryStockReq();
        batchQueryStockReq.setItems(req);
        batchQueryStockReq.setOrderCreateTime(System.currentTimeMillis());
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpResDTO<List<StockQueryRes>> response = httpComponent.soonPost(true, stockApiEurekaHost + "/stock/batchQuery"
            , JSON.toJSONString(batchQueryStockReq), headers, new TypeReference<List<StockQueryRes>>() {
            });
        if (null != response && CodeEnum.SUCCESS.getCode().equals(response.getCode())) {
            log.info("[call success][req:{},response:{}]", req, response);
            return response.getData();
        }
        throw new BizRuntimeException(CodeEnum.HTTP_ERROR);
    }

    private Map<String, Object> putSign(Map<String, Object> param) {
        param.put("timestamp", System.currentTimeMillis());
        param.put("sys", stockApiSys);
        param.put("sign", Md5Utils.sign(param, stockApiSignKey));
        return param;
    }
}
