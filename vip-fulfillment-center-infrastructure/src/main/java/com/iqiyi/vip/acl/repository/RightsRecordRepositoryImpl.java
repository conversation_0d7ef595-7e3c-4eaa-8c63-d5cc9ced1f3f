package com.iqiyi.vip.acl.repository;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.iqiyi.vip.domain.rights.entity.ReceiveRightRecord;
import com.iqiyi.vip.domain.rights.repository.RightsRecordRepository;
import com.iqiyi.vip.dto.rights.ReceiveRecordByOrderCodeQry;
import com.iqiyi.vip.dto.rights.ReceiveRightRecordQryCon;
import com.iqiyi.vip.dto.rights.RecordByOrderCodeSkuIdQry;
import com.iqiyi.vip.enums.ReceiveRightRecordRefundStatus;
import com.iqiyi.vip.enums.ReceiveStatusEnum;
import com.iqiyi.vip.enums.SendTypeEnum;
import com.iqiyi.vip.enums.YesOrNoEnum;
import com.iqiyi.vip.mapper.RightsRecordMapper;
import com.iqiyi.vip.struct.RightsRecordStructMapper;
import com.iqiyi.vip.tidbmapper.TiDBReceiveRightRecordMapper;
import com.iqiyi.vip.utils.MergeUtils;

/**
 * 分库后的权益记录
 *
 * <AUTHOR>
 * @date 2024/7/31 10:17
 */
@Slf4j
@Repository
public class RightsRecordRepositoryImpl implements RightsRecordRepository {

    @Resource
    private RightsRecordMapper rightsRecordMapper;
    @Resource
    private TiDBReceiveRightRecordMapper tiDBReceiveRightRecordMapper;

    public static final int MAX_RESPONSE_SAVE_LENGTH = 1000;
    public static final int MAX_REMARK_LENGTH = 2048;

    @Override
    public int insertRecord(ReceiveRightRecord receiveRightRecord) {
        return rightsRecordMapper.insertSelective(RightsRecordStructMapper.INSTANCE.do2po(receiveRightRecord));
    }

    @Override
    public Integer updateByOrderCodeSelective(ReceiveRightRecord receiveRightRecord) {
        com.iqiyi.vip.po.RightsRecord updateInfo = RightsRecordStructMapper.INSTANCE.do2po(receiveRightRecord);
        subStrResponse(updateInfo);
        return rightsRecordMapper.updateByOrderCodeAndPromotionCodeSelective(updateInfo);
    }

    /**
     * 因为存储长度有限，只保存部分响应结果
     */
    private void subStrResponse(com.iqiyi.vip.po.RightsRecord updateInfo) {
        String sendRes = updateInfo.getSendRes();
        if (StringUtils.isNotBlank(sendRes) && sendRes.length() > MAX_RESPONSE_SAVE_LENGTH) {
            updateInfo.setSendRes(sendRes.substring(0, MAX_RESPONSE_SAVE_LENGTH));
        }
        String refundRes = updateInfo.getRefundRes();
        if (StringUtils.isNotBlank(refundRes) && refundRes.length() > MAX_RESPONSE_SAVE_LENGTH) {
            updateInfo.setRefundRes(refundRes.substring(0, MAX_RESPONSE_SAVE_LENGTH));
        }
        String remark = updateInfo.getRemark();
        if (StringUtils.isNotBlank(remark) && remark.length() > MAX_REMARK_LENGTH) {
            updateInfo.setRemark(remark.substring(0, MAX_REMARK_LENGTH));
        }
    }

    @Override
    public Integer updateCallBackStatus(ReceiveRightRecord receiveRightRecord) {
        com.iqiyi.vip.po.RightsRecord updateInfo = RightsRecordStructMapper.INSTANCE.do2po(receiveRightRecord);
        return rightsRecordMapper.updateCallBackStatus(updateInfo);
    }

    @Override
    public List<ReceiveRightRecord> queryByCon(ReceiveRightRecordQryCon con) {
        return RightsRecordStructMapper.INSTANCE.po2doList(queryByConAndMerge(con));
    }

    @Override
    public List<ReceiveRightRecord> queryByConFromMySql(ReceiveRightRecordQryCon con) {
        List<com.iqiyi.vip.po.RightsRecord> currentList = rightsRecordMapper.selectByCon(con);
        return CollectionUtils.isEmpty(currentList) ? Collections.EMPTY_LIST : RightsRecordStructMapper.INSTANCE.po2doList(currentList);
    }

    public List<com.iqiyi.vip.po.RightsRecord> queryByConAndMerge(ReceiveRightRecordQryCon con) {
        // MySQL查询
        List<com.iqiyi.vip.po.RightsRecord> currentList = rightsRecordMapper.selectByCon(con);
        // TiDB查询
        List<com.iqiyi.vip.po.RightsRecord> historyList = tiDBReceiveRightRecordMapper.selectByCon(con);
        // 合并列表
        List<com.iqiyi.vip.po.RightsRecord> resultList = MergeUtils.mergeRightRecordList(currentList, historyList);
        return resultList;
    }

    @Override
    public ReceiveRightRecord queryByOrderCode(ReceiveRecordByOrderCodeQry qry) {
        com.iqiyi.vip.po.RightsRecord dbRecord = rightsRecordMapper.selectByUidAndOrderCodeAndPromotionCode(qry.getUid(), qry.getOrderCode(), qry.getPromotionCode());
        return RightsRecordStructMapper.INSTANCE.po2do(dbRecord);
    }

    @Override
    public ReceiveRightRecord queryByOrderCode(RecordByOrderCodeSkuIdQry qry) {
        com.iqiyi.vip.po.RightsRecord dbRecord = rightsRecordMapper.selectByUidAndOrderCodeAndSkuId(qry.getUid(), qry.getOrderCode(), qry.getSkuId());
        return RightsRecordStructMapper.INSTANCE.po2do(dbRecord);
    }

    @Override
    public Map<String, List<ReceiveRightRecord>> promotionCode2RightsMap(Long uid) {
        List<ReceiveRightRecord> userRightsList = queryByConFromMySql(ReceiveRightRecordQryCon.builder().uid(uid).build());
        return CollectionUtils.isEmpty(userRightsList) ? MapUtils.EMPTY_MAP
            : userRightsList.stream()
                .filter(r -> StringUtils.isNotBlank(r.getPromotionCode()))
                .collect(Collectors.groupingBy(ReceiveRightRecord::getPromotionCode));
    }

    @Override
    public List<ReceiveRightRecord> queryCanReceiveOrderList(ReceiveRecordByOrderCodeQry receiveRecordByorderCodeQry) {
        ReceiveRightRecordQryCon con = ReceiveRightRecordQryCon.builder()
            .uid(receiveRecordByorderCodeQry.getUid())
            .orderCode(receiveRecordByorderCodeQry.getOrderCode())
            .promotionCode(receiveRecordByorderCodeQry.getPromotionCode())
            .receiveStatus(ReceiveStatusEnum.PENDING_RECEIVE.getStatus())
            .refundStatus(ReceiveRightRecordRefundStatus.UN_REFUND.getStatus())
            .queryValidReceiveDeadlineTime(YesOrNoEnum.YES.getValue())
            .date(new Date())
            .receiveType(SendTypeEnum.MANUAL_RECEIVE.getType()).build();
        return queryByConFromMySql(con);
    }
}
