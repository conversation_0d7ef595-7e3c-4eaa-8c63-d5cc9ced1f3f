package com.iqiyi.vip.acl.repository;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.iqiyi.vip.component.HttpComponent;
import com.iqiyi.vip.domain.benefit.entity.BenefitBeforeBuySkuCheckDetailResVO;
import com.iqiyi.vip.domain.benefit.repository.BenefitRepository;
import com.iqiyi.vip.domain.sku.entity.SkuBeforeBuyCheckAggregate;
import com.iqiyi.vip.dto.http.HttpResDTO;
import com.iqiyi.vip.dto.sku.SkuBeforeBuyCheckDetailRes;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.StatusEnum;
import com.iqiyi.vip.enums.YesOrNoEnum;

/**
 * 福利系统
 *
 * <AUTHOR>
 * @date 2024/4/24 14:38
 */
@Slf4j
@Repository
public class BenefitRepositoryImpl implements BenefitRepository {

    @Value("${benefit.eureka.host:http://vip-benefit-api-test/internal/benefit}")
    private String benefitEurekaHost;

    @Value("${benefit.call.switch:1}")
    private Integer benefitCallSwitch;

    @Value("${benefitCodeToFulfillCodeList:Q00603_Q00206,Q00330_Q00401,Q00601_Q00219}")
    private String benefitCodeToFulfillCodeListStr;

    @Value("${benefit.check.url:/obtain/checkOnlyBenefit}")
    private String benefitCheckUrl;

    @Resource
    private HttpComponent httpComponent;

    public void batchAskPartnerCanBuy(Long uid, List<SkuBeforeBuyCheckAggregate> skuBeforeBuyCheckAggregateList) {
        try {
            if (!benefitCallSwitch.equals(StatusEnum.VALID.getStatus())) {
                return;
            }
            if (CollectionUtils.isEmpty(skuBeforeBuyCheckAggregateList) || null == uid ) {
                return;
            }
            List<String> needCallBenefitCheckSkuIds = Lists.newArrayList();
            for (SkuBeforeBuyCheckAggregate skuBeforeBuyCheckAggregate : skuBeforeBuyCheckAggregateList) {
                if (null != skuBeforeBuyCheckAggregate.getSku() && null != skuBeforeBuyCheckAggregate.getSku().getSpecAttributes()
                    && "1".equals(skuBeforeBuyCheckAggregate.getSku().getSpecAttributes().getRelOrCreate())) {
                    needCallBenefitCheckSkuIds.add(skuBeforeBuyCheckAggregate.getSkuId());
                }
            }
            if (CollectionUtils.isEmpty(needCallBenefitCheckSkuIds)) {
                return;
            }
            List<BenefitBeforeBuySkuCheckDetailResVO> beforeBuySkuCheckList = beforeBuySkuCheck(uid, Joiner.on(",").join(needCallBenefitCheckSkuIds));
            if (CollectionUtils.isEmpty(beforeBuySkuCheckList)) {
                log.warn("[beforeBuySkuCheckList empty][uid:{},needCallBenefitCheckSkuIds:{}]", uid, needCallBenefitCheckSkuIds);
                return;
            }
            Map<String, BenefitBeforeBuySkuCheckDetailResVO> skuId2Res = beforeBuySkuCheckList.stream()
                .collect(Collectors.toMap(BenefitBeforeBuySkuCheckDetailResVO::getSkuId, Function.identity(), (key1, key2) -> key2));

            Map<String, String> benefitCodeToFulfillCodeMap = Splitter.on(",").splitToList(benefitCodeToFulfillCodeListStr).stream()
                .collect(Collectors.toMap(s -> s.split("_")[0], s -> s.split("_")[1]));

            for (SkuBeforeBuyCheckAggregate skuBeforeBuyCheckAggregate : skuBeforeBuyCheckAggregateList) {
                BenefitBeforeBuySkuCheckDetailResVO benefitCheckRes = skuId2Res.get(skuBeforeBuyCheckAggregate.getSkuId());
                if (null != benefitCheckRes && YesOrNoEnum.NO.getValue().equals(benefitCheckRes.getCanBuy())) {
                    SkuBeforeBuyCheckDetailRes checkDetailRes = new SkuBeforeBuyCheckDetailRes();
                    checkDetailRes.setSkuId(benefitCheckRes.getSkuId());
                    checkDetailRes.setSkuAmount(skuBeforeBuyCheckAggregate.getSkuAmount());

                    //将福利code转成履约系统code
                    CodeEnum fulfillCodeEnum = getErrorCodeEnumByBenefitCode(benefitCheckRes.getCheckCode(), benefitCodeToFulfillCodeMap);
                    checkDetailRes.setCheckCode(fulfillCodeEnum.getCode());
                    checkDetailRes.setCheckMsg(fulfillCodeEnum.getMessage());
                    checkDetailRes.setCanBuy(benefitCheckRes.getCanBuy());

                    skuBeforeBuyCheckAggregate.setCheckResult(checkDetailRes);
                    skuBeforeBuyCheckAggregate.setNeedNextCheck(false);
                }
            }
        } catch (Exception e) {
            log.error("[Exception][uid:{},skuBeforeBuyCheckAggregateList:{}]", uid, skuBeforeBuyCheckAggregateList, e);
        }
    }

    /**
     * 把福利返回的错误码映射成履约中心的错误码给前端提示(仅针对不能买的情况做错误码映射)
     */
    private CodeEnum getErrorCodeEnumByBenefitCode(String benefitCode, Map<String, String> benefitCodeToFulfillCodeMap) {
        if (StringUtils.isBlank(benefitCode)) {
            return CodeEnum.ERROR_BENEFIT_CODE;
        }
        if (MapUtils.isEmpty(benefitCodeToFulfillCodeMap)) {
            return CodeEnum.ERROR_BENEFIT_CODE;
        }
        CodeEnum fulfillCodeEnum = CodeEnum.of(benefitCodeToFulfillCodeMap.get(benefitCode));
        return null == fulfillCodeEnum ? CodeEnum.ERROR_BENEFIT_CODE : fulfillCodeEnum;
    }

    public List<BenefitBeforeBuySkuCheckDetailResVO> beforeBuySkuCheck(Long uid, String skuIds) {
        try {
            Map<String, Object> reqParams = Maps.newHashMap();
            reqParams.put("uid", uid);
            reqParams.put("skuIds", skuIds);
            HttpResDTO<List<BenefitBeforeBuySkuCheckDetailResVO>> response = httpComponent.soonPost(true,
                benefitEurekaHost + benefitCheckUrl, reqParams, null, new TypeReference<List<BenefitBeforeBuySkuCheckDetailResVO>>() {
                });
            if (null == response || !response.suc()) {
                log.error("[httpClientResponseDTO null][uid:{},skuIds:{}][httpClientResponseDTO:{}]", uid, skuIds, response);
                return Collections.EMPTY_LIST;
            }
            return response.getData();
        } catch (Exception e) {
            log.error("[Exception][uid:{},skuIds:{}]", uid, skuIds, e);
            return Collections.EMPTY_LIST;
        }
    }
}
