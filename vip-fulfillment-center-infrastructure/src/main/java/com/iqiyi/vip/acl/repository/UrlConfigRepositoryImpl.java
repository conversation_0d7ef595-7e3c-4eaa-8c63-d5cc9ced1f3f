package com.iqiyi.vip.acl.repository;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

import com.iqiyi.vip.domain.urlconfig.entity.UrlConfig;
import com.iqiyi.vip.domain.urlconfig.repository.UrlConfigRepository;
import com.iqiyi.vip.mapper.UrlConfigMapper;
import com.iqiyi.vip.struct.UrlConfigStructMapper;

/**
 * <AUTHOR>
 * @date 2023/9/28 14:04
 */
@Slf4j
@Repository
public class UrlConfigRepositoryImpl implements UrlConfigRepository {

    @Resource
    private UrlConfigMapper urlConfigMapper;

    @Override
    @Cacheable(value = "urlConfigQueryById", cacheManager = "caffeineCacheManager")
    public UrlConfig queryByIdFromCache(Long id) {
        return UrlConfigStructMapper.INSTANCE.po2do(urlConfigMapper.selectByPrimaryKey(id));
    }
}
