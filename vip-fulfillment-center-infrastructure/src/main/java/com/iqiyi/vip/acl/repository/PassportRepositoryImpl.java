package com.iqiyi.vip.acl.repository;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Map;

import com.iqiyi.vip.component.HttpComponent;
import com.iqiyi.vip.domain.passport.entity.PassportUserInfo;
import com.iqiyi.vip.domain.passport.repository.PassportRepository;
import com.iqiyi.vip.dto.http.HttpResDTO;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.exception.BizRuntimeException;
import com.iqiyi.vip.utils.AssertUtils;
import com.iqiyi.vip.utils.Md5Utils;

/**
 * <AUTHOR>
 * @date 2023/11/29 14:27
 */
@Slf4j
@Repository
public class PassportRepositoryImpl implements PassportRepository {

    @Value("${passport.query.plaintext.byuid.url:http://passport.qiyi.domain/apis/plaintext/byUid.action}")
    private String queryPlaintextByUidUrl;

    @Resource
    private HttpComponent httpComponent;

    /**
     * 查询未脱敏用户信息
     */
    @Override
    public PassportUserInfo queryUserInfo(Long uid) {
        AssertUtils.notNull(uid, CodeEnum.ERROR_NOT_LOG);
        Map<String, Object> params = Maps.newHashMap();
        params.put("source", "boss");
        params.put("uid", String.valueOf(uid));
        params.put("fields", "userinfo,private");
        params.put("timestamp", String.valueOf(System.currentTimeMillis()));
        params.put("sign", Md5Utils.verticalVirguleSign(params, "bSsIlop6JD8tW7BdPLQ2foAL"));
        HttpResDTO<PassportUserInfo> passportHttpRes = httpComponent.soonPost(false, queryPlaintextByUidUrl, params, null, new TypeReference<PassportUserInfo>() {
        });
        log.info("[queryUserByUid][uid:{},reqMap:{}][response:{}]", uid, params, passportHttpRes);
        if (null == passportHttpRes || !passportHttpRes.suc()) {
            throw new BizRuntimeException(CodeEnum.HTTP_ERROR);
        }
        return passportHttpRes.getData();
    }
}
