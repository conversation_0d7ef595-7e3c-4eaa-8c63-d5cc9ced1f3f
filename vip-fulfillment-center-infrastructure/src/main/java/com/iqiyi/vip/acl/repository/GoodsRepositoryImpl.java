package com.iqiyi.vip.acl.repository;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

import com.iqiyi.vip.domain.goods.entity.Goods;
import com.iqiyi.vip.domain.goods.repository.GoodsRepository;
import com.iqiyi.vip.mapper.GoodsMapper;
import com.iqiyi.vip.struct.GoodsStructMapper;

/**
 * <AUTHOR>
 * @date 2023/9/7 15:35
 */
@Slf4j
@Repository
public class GoodsRepositoryImpl implements GoodsRepository {

    @Resource
    private GoodsMapper goodsMapper;

    @Override
    public Goods queryBySkuId(String skuId) {
        return GoodsStructMapper.INSTANCE.po2do(goodsMapper.selectBySkuId(skuId));
    }

    @Override
    @Cacheable(value = "queryByGiftCode", cacheManager = "caffeineCacheManager")
    public List<Goods> queryByGiftCode(String giftCode) {
        return GoodsStructMapper.INSTANCE.poList2doList(goodsMapper.selectByGiftCode(giftCode));
    }

    @Override
    @Cacheable(value = "goodsByPromotionCode", cacheManager = "caffeineCacheManager")
    public Goods queryByPromotionCodeFromCache(String promotionCode) {
        return GoodsStructMapper.INSTANCE.po2do(goodsMapper.selectByPromotionCode(promotionCode));
    }
}
