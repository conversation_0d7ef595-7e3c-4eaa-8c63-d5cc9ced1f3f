/**
 *
 */
package com.iqiyi.vip.acl.serviceimpl;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.iqiyi.vip.domain.fulfillment.service.FulfillmentMqService;
import com.iqiyi.vip.domain.rights.entity.OpenOrderRights;
import com.iqiyi.vip.domain.rights.entity.ReceiveRightRecord;
import com.iqiyi.vip.domain.rights.factory.FulfillmentMsgFactory;
import com.iqiyi.vip.dto.order.RefundOrderInfo;
import com.iqiyi.vip.dto.rights.FulfillmentMsg;
import com.iqiyi.vip.dto.rights.PartnerRightsResponse;

/**
 * 履约消息发放服务实现
 */
@Slf4j
@Service("fulfillmentMqService")
@ConditionalOnProperty(prefix = "rocketmq.producer", name = "enabled", havingValue = "true")
public class FulfillmentMqServiceImpl implements FulfillmentMqService {

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @Value("${rocketmq.producers[1].topic}")
    private String topic;

    @Override
    public void sendOpenRightsMsg(ReceiveRightRecord receiveRightRecord, OpenOrderRights openRights, PartnerRightsResponse partnerRightsResponse) {
        FulfillmentMsg msg = FulfillmentMsgFactory.buildGrantRightsMsg(receiveRightRecord, openRights, partnerRightsResponse);
        log.info("[msg:{}]", msg);
        rocketMQTemplate.syncSend(topic, MessageBuilder.withPayload(msg).build());
    }

    @Override
    public void sendRecycleRightsMsg(ReceiveRightRecord receiveRightRecord, RefundOrderInfo refundOrderInfo, PartnerRightsResponse partnerRightsResponse) {
        FulfillmentMsg msg = FulfillmentMsgFactory.buildRecycleRightsMsg(receiveRightRecord, refundOrderInfo, partnerRightsResponse);
        log.info("[msg:{}]", msg);
        rocketMQTemplate.syncSend(topic, MessageBuilder.withPayload(msg).build());
    }

}
