package com.iqiyi.vip.acl.repository;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Map;
import java.util.UUID;

import com.iqiyi.vip.component.HttpComponent;
import com.iqiyi.vip.domain.rights.factory.RightRecordRemarkFactory;
import com.iqiyi.vip.domain.utils.BeanCovertUtil;
import com.iqiyi.vip.domain.vmc.repository.VmcRepository;
import com.iqiyi.vip.dto.base.BaseResponse;
import com.iqiyi.vip.dto.http.HttpResDTO;
import com.iqiyi.vip.dto.rights.OpenVmcRightsReq;
import com.iqiyi.vip.dto.rights.OpenVmcRightsRes;
import com.iqiyi.vip.dto.rights.PartnerRightsResponse;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.RetryEnum;
import com.iqiyi.vip.enums.SendStatusEnum;
import com.iqiyi.vip.enums.StatusEnum;
import com.iqiyi.vip.enums.VmcCodeEnum;
import com.iqiyi.vip.utils.Md5Utils;

/**
 * VMC接口
 *
 * <AUTHOR>
 * @date 2024/10/28 14:43
 */
@Slf4j
@Repository
public class VmcRepositoryImpl implements VmcRepository {

    @Value("${vmc.openRight.signKey:123456}")
    private String vmcOpenRightSignKey;

    @Value("${vmc.eureka.switch:1}")
    private Integer vmcEurekaSwitch;

    @Value("${vmc.eureka.url.prefix:http://VIP-MANAGER-CENTER-TEST/}")
    private String vmcEurekaUrlPrefix;

    @Value("${vmc.http.url.prefix:http://serv.vip.qiyi.domain/vmc/}")
    private String vmcHttpUrlPrefix;

    @Resource
    private HttpComponent httpComponent;

    /**
     * VMC权益开通接口 https://iq.feishu.cn/wiki/G0fJwptdHiYXTtkYA8Jc4ugtngf?open_in_browser=true
     */
    @Override
    public BaseResponse<PartnerRightsResponse> openRights(OpenVmcRightsReq req) {
        Map<String, Object> params = BeanCovertUtil.transBean2Map(req);
        params.put("version", "1.0");
        params.put("subscribeType", 0);
        putSign(params);
        HttpResDTO<OpenVmcRightsRes> httpResDTO = httpComponent.soonPostJson(vmcEurekaSwitch,
            getBaseUrl() + "/internal/openRights.action", params, new TypeReference<OpenVmcRightsRes>() {
            });

        if (null == httpResDTO || httpResDTO.getCode() == null) {
            log.error("[http fail][resp is null, req:{}]", req);
            return BaseResponse.create(CodeEnum.HTTP_ERROR, RetryEnum.YES);
        }

        Integer sendStatus = getSendStatus(httpResDTO.getCode(), httpResDTO.getMessage());
        CodeEnum codeEnum = getCodeEnum(httpResDTO.getCode(), httpResDTO.getMessage());

        //构建BaseResponse
        BaseResponse<PartnerRightsResponse> response = new BaseResponse(codeEnum.getCode(), codeEnum.getMessage());
        boolean retry = SendStatusEnum.needRetry(sendStatus);
        response.setRetry(BooleanUtils.toInteger(retry));
        //构建partnerRightsResponse
        PartnerRightsResponse partnerRightsResponse = new PartnerRightsResponse();
        partnerRightsResponse.setSendStatus(sendStatus);
        partnerRightsResponse.setAccount(String.valueOf(req.getUserId()));
        partnerRightsResponse.setAmount(req.getSkuAmount());
        partnerRightsResponse.setSendTime(System.currentTimeMillis());
        //设置返回的data详情
        OpenVmcRightsRes openVmcRightsRes = httpResDTO.getData();
        if (openVmcRightsRes != null) {
            partnerRightsResponse.setSendRes(JSON.toJSONString(openVmcRightsRes));
            partnerRightsResponse.setRightRecordRemark(RightRecordRemarkFactory.buildRemark(openVmcRightsRes));
        }
        response.setData(partnerRightsResponse);

        //需取消订单
        if (SendStatusEnum.NEED_CANCEL.getStatus() == sendStatus) {
            log.info("[http fail][Need cancel order, req:{}]", req);
            return response;
        }

        if (openVmcRightsRes == null) {
            log.error("[http fail][resp body is null,req:{}]", req);
            return BaseResponse.create(CodeEnum.HTTP_ERROR, RetryEnum.YES);
        }

        return response;
    }

    private CodeEnum getCodeEnum(String code, String msg) {
        //将VMC转换成履约系统的code码
        CodeEnum codeEnum = VmcCodeEnum.convertToCodeEnum(code);
        //用户已订阅
        if (hasSubscribed(code, msg)) {
            codeEnum = CodeEnum.SUCCESS;
        }
        return codeEnum;
    }

    private int getSendStatus(String code, String msg) {
        //如果 用户已订购，则返回取消的状态码
        if (hasSubscribed(code, msg)) {
            return SendStatusEnum.NEED_CANCEL.getStatus();
        }
        return CodeEnum.SUCCESS.getCode().equals(code) ? SendStatusEnum.SEND_SUC.getStatus()
            : SendStatusEnum.SEND_FAIL_RETRY.getStatus();
    }

    private boolean hasSubscribed(String code, String msg) {
        return VmcCodeEnum.ILLEGAL_ORDER.getCode().equals(code) && StringUtils.isNotBlank(msg) && msg.startsWith("用户已订购");
    }

    private Map<String, Object> putSign(Map<String, Object> param) {
        param.put("messageId", "fulfill_" + UUID.randomUUID().toString().replaceAll("-", ""));
        param.put("sign", Md5Utils.signNotNull(param, vmcOpenRightSignKey));
        return param;
    }

    private String getBaseUrl() {
        return vmcEurekaSwitch.equals(StatusEnum.VALID.getStatus()) ? vmcEurekaUrlPrefix : vmcHttpUrlPrefix;
    }

}
