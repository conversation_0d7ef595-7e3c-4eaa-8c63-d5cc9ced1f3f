package com.iqiyi.vip.mapper;

import org.apache.ibatis.annotations.Param;

import java.util.List;

import com.iqiyi.vip.po.FulfillmentConfig;

public interface FulfillmentConfigMapper {
    int deleteByPrimaryKey(Long id);

    int insert(FulfillmentConfig record);

    int insertSelective(FulfillmentConfig record);

    FulfillmentConfig selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(FulfillmentConfig record);

    int updateByPrimaryKey(FulfillmentConfig record);

    FulfillmentConfig selectBySkuIdAndActCode(@Param("skuId") String skuId, @Param("actCode") String actCode);

    List<FulfillmentConfig> selectBySkuId(@Param("skuId") String skuId);

    int updateInvalidBySkuId(FulfillmentConfig record);
}