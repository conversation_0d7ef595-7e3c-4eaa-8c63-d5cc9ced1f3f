package com.iqiyi.vip.mapper;

import com.iqiyi.vip.po.SpuFulfillmentConfig;

public interface SpuFulfillmentConfigMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SpuFulfillmentConfig record);

    int insertSelective(SpuFulfillmentConfig record);

    SpuFulfillmentConfig selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SpuFulfillmentConfig record);

    int updateByPrimaryKey(SpuFulfillmentConfig record);

    SpuFulfillmentConfig selectBySpuId(String spuId);

    SpuFulfillmentConfig selectByGiftType(Integer giftType);

}