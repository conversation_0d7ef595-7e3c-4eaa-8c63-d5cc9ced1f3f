package com.iqiyi.vip.mapper;

import org.apache.ibatis.annotations.Param;

import java.util.List;

import com.iqiyi.vip.dto.rights.ReceiveRightRecordQryCon;
import com.iqiyi.vip.po.RightsRecord;

public interface RightsRecordMapper {

    int deleteByPrimaryKey(Long id);

    int insert(RightsRecord record);

    int insertSelective(RightsRecord record);

    RightsRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(RightsRecord record);

    int updateByPrimaryKey(RightsRecord record);

    RightsRecord selectByUidAndOrderCodeAndPromotionCode(@Param("uid") Long uid, @Param("orderCode") String orderCode, @Param("promotionCode") String promotionCode);

    RightsRecord selectByUidAndOrderCodeAndSkuId(@Param("uid") Long uid, @Param("orderCode") String orderCode, @Param("skuId") String skuId);

    int updateByOrderCodeAndPromotionCodeSelective(RightsRecord record);

    int updateCallBackStatus(RightsRecord record);

    List<RightsRecord> selectByCon(ReceiveRightRecordQryCon con);

}