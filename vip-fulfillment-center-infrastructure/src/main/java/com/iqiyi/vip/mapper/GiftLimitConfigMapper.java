package com.iqiyi.vip.mapper;

import com.iqiyi.vip.po.GiftLimitConfig;

public interface GiftLimitConfigMapper {
    int deleteByPrimaryKey(Long id);

    int insert(GiftLimitConfig record);

    int insertSelective(GiftLimitConfig record);

    GiftLimitConfig selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(GiftLimitConfig record);

    int updateByPrimaryKey(GiftLimitConfig record);

    GiftLimitConfig selectValidByCode(String giftCode);
}