package com.iqiyi.vip.component;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Collections;
import java.util.Map;

import com.iqiyi.kit.http.client.util.HttpClients;
import com.iqiyi.vip.dto.http.HttpResDTO;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.StatusEnum;

/**
 * <AUTHOR>
 * @date 2023/8/1 15:07
 */
@Slf4j
@Component
public class HttpComponent {

    @Resource(name = "restTemplate")
    private RestTemplate restTemplate;

    @Resource(name = "fastRestTemplate")
    private RestTemplate fastRestTemplate;

    @Resource(name = "lbRestTemplate")
    private RestTemplate lbRestTemplate;

    @Resource(name = "fastLbRestTemplate")
    private RestTemplate fastLbRestTemplate;

    @Resource(name = "soonRestTemplate")
    private RestTemplate soonRestTemplate;

    @Resource(name = "soonLbRestTemplate")
    private RestTemplate soonLbRestTemplate;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    static {
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
    }

    public <T> HttpResDTO<T> postJson(Integer eurekaSwitch, String url, Map<String, Object> requestMap, TypeReference<?> resTypeReference) {
        try {
            ResponseEntity<String> responseEntity;
            if (eurekaSwitch.equals(StatusEnum.VALID.getStatus())) {
                responseEntity = postJsonForResponseEntity(url, requestMap, lbRestTemplate);
            } else {
                responseEntity = postJsonForResponseEntity(url, requestMap, restTemplate);
            }
            return dealRes(responseEntity, resTypeReference);
        } catch (Exception e) {
            log.error("[Exception][url:{},requestMap:{}]", url, requestMap, e);
            return null;
        }
    }

    public <T> HttpResDTO<T> fastPostJson(Integer eurekaSwitch, String url, Map<String, Object> requestMap, TypeReference<?> resTypeReference) {
        try {
            ResponseEntity<String> responseEntity;
            if (eurekaSwitch.equals(StatusEnum.VALID.getStatus())) {
                responseEntity = postJsonForResponseEntity(url, requestMap, fastLbRestTemplate);
            } else {
                responseEntity = postJsonForResponseEntity(url, requestMap, fastRestTemplate);
            }
            return dealRes(responseEntity, resTypeReference);
        } catch (Exception e) {
            log.error("[Exception][url:{},requestMap:{}]", url, requestMap, e);
            return null;
        }
    }

    public <T> HttpResDTO<T> soonPostJson(Integer eurekaSwitch, String url, Map<String, Object> requestMap, TypeReference<?> resTypeReference) {
        try {
            ResponseEntity<String> responseEntity;
            if (eurekaSwitch.equals(StatusEnum.VALID.getStatus())) {
                responseEntity = postJsonForResponseEntity(url, requestMap, soonLbRestTemplate);
            } else {
                responseEntity = postJsonForResponseEntity(url, requestMap, soonRestTemplate);
            }
            return dealRes(responseEntity, resTypeReference);
        } catch (Exception e) {
            log.error("[Exception][url:{},requestMap:{}]", url, requestMap, e);
            return null;
        }
    }

    public <T> HttpResDTO<T> postJson(String url, Map<String, Object> requestMap, RestTemplate restTemplate, TypeReference<?> resTypeReference) {
        try {
            ResponseEntity<String> responseEntity = postJsonForResponseEntity(url, requestMap, restTemplate);
            return dealRes(responseEntity, resTypeReference);
        } catch (Exception e) {
            log.error("[Exception][url:{},requestMap:{}]", url, requestMap, e);
            return null;
        }
    }

    public ResponseEntity<String> postJsonForResponseEntity(String url, Map<String, Object> reqMap, RestTemplate restTemplate) {
        long start = System.currentTimeMillis();
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("application/json; charset=UTF-8"));
            String reqStr = JSON.toJSONString(reqMap);
            HttpEntity entity = new HttpEntity(reqStr, headers);
            log.info("[HTTP-post][url:{}][reqStr:{}]", url, reqStr);
            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);
            log.info("[HTTP-post][url:{}][reqStr:{}][responseCode:{}][responseBody:{}][ct:{}ms]", url, reqStr,
                response.getStatusCode(), response.getBody(), System.currentTimeMillis() - start);
            return response;
        } catch (HttpStatusCodeException e) {
            log.error("[HTTP-post][Exception][url:{}][reqMap:{}][ct:{}ms]", url, reqMap, System.currentTimeMillis() - start, e);
            return new ResponseEntity(e.getResponseBodyAsString(), e.getStatusCode());
        }
    }

    public <T> HttpResDTO<T> post(boolean eurekaSwitch, String url, Map<String, Object> requestMap, HttpHeaders headers, TypeReference<?> resTypeReference) {
        try {
            ResponseEntity<String> responseEntity;
            if (eurekaSwitch) {
                responseEntity = postForEntity(url, requestMap, headers, lbRestTemplate);
            } else {
                responseEntity = postForEntity(url, requestMap, headers, restTemplate);
            }
            return dealRes(responseEntity, resTypeReference);
        } catch (Exception e) {
            log.error("[Exception][url:{},requestMap:{}]", url, requestMap, e);
            return null;
        }
    }

    public <T> HttpResDTO<T> fastPost(boolean eurekaSwitch, String url, Map<String, Object> requestMap, HttpHeaders headers, TypeReference<?> resTypeReference) {
        try {
            ResponseEntity<String> responseEntity;
            if (eurekaSwitch) {
                responseEntity = postForEntity(url, requestMap, headers, fastLbRestTemplate);
            } else {
                responseEntity = postForEntity(url, requestMap, headers, fastRestTemplate);
            }
            return dealRes(responseEntity, resTypeReference);
        } catch (Exception e) {
            log.error("[Exception][url:{},requestMap:{}]", url, requestMap, e);
            return null;
        }
    }

    public <T> HttpResDTO<T> soonPost(boolean eurekaSwitch, String url, Map<String, Object> requestMap, HttpHeaders headers, TypeReference<?> resTypeReference) {
        try {
            ResponseEntity<String> responseEntity;
            if (eurekaSwitch) {
                responseEntity = postForEntity(url, requestMap, headers, soonLbRestTemplate);
            } else {
                responseEntity = postForEntity(url, requestMap, headers, soonRestTemplate);
            }
            return dealRes(responseEntity, resTypeReference);
        } catch (Exception e) {
            log.error("[Exception][url:{},requestMap:{}]", url, requestMap, e);
            return null;
        }
    }

    public <T> HttpResDTO<T> soonGetForEntity(boolean eurekaSwitch, String url, Map<String, Object> requestMap, TypeReference<?> resTypeReference) {
        try {
            ResponseEntity<String> responseEntity;
            if (eurekaSwitch) {
                responseEntity = getForEntity(url, requestMap, soonLbRestTemplate);
            } else {
                responseEntity = getForEntity(url, requestMap, soonRestTemplate);
            }
            return dealRes(responseEntity, resTypeReference);
        } catch (Exception e) {
            log.error("[Exception][url:{},requestMap:{}]", url, requestMap, e);
            return null;
        }
    }

    public ResponseEntity<String> postForEntity(String url, Map<String, Object> reqMap, HttpHeaders headers, RestTemplate restTemplate) {
        long startTime = System.currentTimeMillis();
        try {
            log.info("[HTTP-post][start][url:{}][request:{}]", url, reqMap);
            MultiValueMap<String, Object> multiValueMap = new LinkedMultiValueMap<>();
            for (Map.Entry<String, Object> entry : reqMap.entrySet()) {
                multiValueMap.put(entry.getKey(), Collections.singletonList(entry.getValue()));
            }
            HttpEntity entity = new HttpEntity(multiValueMap, headers);
            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);
            log.info("[HTTP-post][end][url:{}][request:{}][responseCode:{}][responseBody:{}][ct:{}]", url, reqMap,
                response.getStatusCode(), response.getBody(), System.currentTimeMillis() - startTime);
            return response;
        } catch (HttpStatusCodeException e) {
            log.error("[HTTP-post][Exception][url:{}][reqMap:{}][ct:{}ms]", url, reqMap, System.currentTimeMillis() - startTime, e);
            return new ResponseEntity(e.getResponseBodyAsString(), e.getStatusCode());
        }
    }


    public <T> HttpResDTO<T> post(boolean eurekaSwitch, String url, String reqStr, HttpHeaders headers, TypeReference<?> resTypeReference) {
        try {
            ResponseEntity<String> responseEntity;
            if (eurekaSwitch) {
                responseEntity = postForEntity(url, reqStr, headers, lbRestTemplate);
            } else {
                responseEntity = postForEntity(url, reqStr, headers, restTemplate);
            }
            return dealRes(responseEntity, resTypeReference);
        } catch (Exception e) {
            log.error("[Exception][url:{},requestMap:{}]", url, reqStr, e);
            return null;
        }
    }

    public <T> HttpResDTO<T> fastPost(boolean eurekaSwitch, String url, String reqStr, HttpHeaders headers, TypeReference<?> resTypeReference) {
        try {
            ResponseEntity<String> responseEntity;
            if (eurekaSwitch) {
                responseEntity = postForEntity(url, reqStr, headers, fastLbRestTemplate);
            } else {
                responseEntity = postForEntity(url, reqStr, headers, fastRestTemplate);
            }
            return dealRes(responseEntity, resTypeReference);
        } catch (Exception e) {
            log.error("[Exception][url:{},requestMap:{}]", url, reqStr, e);
            return null;
        }
    }

    public <T> HttpResDTO<T> soonPost(boolean eurekaSwitch, String url, String reqStr, HttpHeaders headers, TypeReference<?> resTypeReference) {
        try {
            ResponseEntity<String> responseEntity;
            if (eurekaSwitch) {
                responseEntity = postForEntity(url, reqStr, headers, soonLbRestTemplate);
            } else {
                responseEntity = postForEntity(url, reqStr, headers, soonRestTemplate);
            }
            return dealRes(responseEntity, resTypeReference);
        } catch (Exception e) {
            log.error("[Exception][url:{},requestMap:{}]", url, reqStr, e);
            return null;
        }
    }

    /**
     * raw
     */
    public ResponseEntity<String> postForEntity(String url, String reqStr, HttpHeaders headers, RestTemplate restTemplate) {
        long startTime = System.currentTimeMillis();
        try {
            log.info("[HTTP-post][start][url:{}][request:{}]", url, reqStr);
            HttpEntity<String> entity = new HttpEntity<>(reqStr, headers);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
            log.info("[HTTP-post][end][url:{}][request:{}][responseCode:{}][responseBody:{}][ct:{}]", url, reqStr,
                response.getStatusCode(), response.getBody(), System.currentTimeMillis() - startTime);
            return response;
        } catch (HttpStatusCodeException e) {
            log.error("[HTTP-post][Exception][url:{}][reqMap:{}][ct:{}ms]", url, reqStr, System.currentTimeMillis() - startTime, e);
            return new ResponseEntity(e.getResponseBodyAsString(), e.getStatusCode());
        }
    }

    public ResponseEntity<String> getForEntity(String url, Map<String, Object> reqMap, RestTemplate restTemplate) {
        long startTime = System.currentTimeMillis();
        try {
            log.info("[HTTP-getForEntity][start][url:{}][request:{}]", url, reqMap);
            String request = HttpClients.buildQueryUrl(url, reqMap);
            ResponseEntity<String> response = restTemplate.getForEntity(request, String.class);
            log.info("[HTTP-getForEntity][end][url:{}][request:{}][responseCode:{}][responseBody:{}][ct:{}]", url, reqMap,
                response.getStatusCode(), response.getBody(), System.currentTimeMillis() - startTime);
            return response;
        } catch (HttpStatusCodeException e) {
            log.error("[HTTP-getForEntity][Exception][url:{}][reqMap:{}][ct:{}ms]", url, reqMap, System.currentTimeMillis() - startTime, e);
            return new ResponseEntity(e.getResponseBodyAsString(), e.getStatusCode());
        }
    }

    private <T> HttpResDTO<T> dealRes(ResponseEntity<String> responseEntity, TypeReference<?> dataTypeReference) throws IOException {
        if (responseEntity.getStatusCode() == HttpStatus.OK) {
            HttpResDTO result = objectMapper.readValue(responseEntity.getBody(), HttpResDTO.class);
            if (null != dataTypeReference) {
                T data = (T) objectMapper.convertValue(result.getData(), dataTypeReference);
                result.setData(data);
            }
            return result;
        }
        return HttpResDTO.create(CodeEnum.HTTP_ERROR);
    }

}
