package com.iqiyi.vip.component;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.MessageDigestAlgorithms;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.LinkedHashMap;
import java.util.Map;

import com.iqiyi.vip.utils.EncodeUtils;

import static com.iqiyi.vip.log.Constants.*;

/**
 * 交易签名
 *
 * <AUTHOR>
 * @date 2024/10/29 15:31
 */
@Slf4j
public class SignatureInterceptor implements ClientHttpRequestInterceptor {

    private final String source;
    private final String signatureKey;

    private final String signAlgorithm;

    public SignatureInterceptor(String source, String signatureKey, String signAlgorithm) {
        this.source = source;
        this.signatureKey = signatureKey;
        this.signAlgorithm = signAlgorithm;
    }

    public SignatureInterceptor(String source, String signatureKey) {
        this.source = source;
        this.signatureKey = signatureKey;
        this.signAlgorithm = MessageDigestAlgorithms.MD5;
    }

    @Override
    public ClientHttpResponse intercept(final HttpRequest request, final byte[] body,
        final ClientHttpRequestExecution execution) throws IOException {

        // 获取请求参数
        Map<String, String[]> requestParams = new LinkedHashMap<>();
        UriComponentsBuilder.fromUri(request.getURI())
            .build()
            .getQueryParams()
            .forEach((key, value) -> requestParams.put(key, value.toArray(new String[0])));

        // 获取请求体
        String requestBody = new String(body, StandardCharsets.UTF_8);

        // 生成签名
        String signature;
        try {
            signature = generateSignature(requestParams, requestBody, signatureKey, signAlgorithm);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
        // 添加签名到请求头
        request.getHeaders().add(SIGNATURE_HEADER, signature);
        request.getHeaders().add(SOURCE_HEADER, source);

        return execution.execute(request, body);
    }

    private String generateSignature(Map<String, String[]> requestParams, String requestBody, String signatureKey,
        String algorithm)
        throws NoSuchAlgorithmException {

        // 将请求参数和请求体按照一定规则排序和拼接
        // 可根据具体需求调整排序和拼接逻辑
        StringBuilder sb = new StringBuilder();
        requestParams.keySet().stream()
            .sorted()
            .forEach(requestKey -> sb.append(requestKey).append("=").append(
                EncodeUtils.urlDecode(String.join(",", requestParams.get(requestKey)))).append("&"));
        sb.append(requestBody);
        sb.append(signatureKey);

        log.info("actual sign string:{}", sb);

        // 使用指定的签名算法计算签名
        MessageDigest md = MessageDigest.getInstance(algorithm);
        md.update(sb.toString().getBytes());
        byte[] digest = md.digest();

        // 将签名转换为十六进制
        return byte2Hex(digest);
    }

    private static String byte2Hex(byte[] digest) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : digest) {
            hexString.append(String.format("%02x", b));
        }
        return hexString.toString();
    }
}
