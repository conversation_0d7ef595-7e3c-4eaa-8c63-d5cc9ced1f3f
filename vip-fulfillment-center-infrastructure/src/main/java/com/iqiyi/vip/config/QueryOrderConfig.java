package com.iqiyi.vip.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

import com.qiyi.vip.trade.dataservice.client.DataServiceClient;

/**
 * 会员交易查单config
 *
 * <AUTHOR> on 2019/1/22
 */
@Configuration
public class QueryOrderConfig {

    @Value("${query.order.signKey}")
    private String signKey;

    @Value("${query.order.channel:partner_notify}")
    private String channel;


    @Value("${query.order.url:http://VIPTRADE-DATASERVICE-ONLINE}")
    private String url;
    @Value("${query.order.qsmUrl:http://viptrade-dataservice.qsm.qiyi.middle}")
    private String qsmUrl;
    @Resource(name = "lbRestTemplate")
    private RestTemplate lbRestTemplate;
    @Resource(name = "fastRestTemplate")
    private RestTemplate fastRestTemplate;

    @Value("${dataService.qsm.switch:true}")
    private Boolean dataServiceQsmSwitch;

    @Bean("dataServiceClient")
    @Primary
    public DataServiceClient getRestTemplate() {
        DataServiceClient dataServiceClient = new DataServiceClient();
        dataServiceClient.setChannel(channel);
        dataServiceClient.setSignKey(signKey);
        dataServiceClient.setServerUrl(getDataServiceHost());
        dataServiceClient.setNeedCheckRequest(true);
        dataServiceClient.setRestTemplate(getDataServiceRestTemplate());
        return dataServiceClient;
    }

    private String getDataServiceHost() {
        if (dataServiceQsmSwitch) {
            return qsmUrl;
        }
        return url;
    }

    private RestTemplate getDataServiceRestTemplate() {
        if (dataServiceQsmSwitch) {
            return fastRestTemplate;
        }
        return lbRestTemplate;
    }

}
