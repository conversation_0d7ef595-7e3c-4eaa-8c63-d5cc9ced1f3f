package com.iqiyi.vip.config;

import org.apache.commons.codec.digest.MessageDigestAlgorithms;
import org.apache.http.conn.HttpClientConnectionManager;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import com.iqiyi.vip.component.SignatureInterceptor;

/**
 * <AUTHOR>
 * @date 2018/2/11
 */
@Configuration
public class RestTemplateConfig {

    @Value("${restTemplate.connectTimeoutInMillis.long:1000}")
    private Integer longConnectTimeoutInMillis;
    @Value("${restTemplate.readTimeoutInMillis.long:4000}")
    private Integer longReadTimeoutInMillis;
    @Value("${restTemplate.connectionRequestTimeout.long:3000}")
    private Integer longConnectionRequestTimeout;

    @Value("${restTemplate.connectTimeoutInMillis.fast:1000}")
    private Integer fastConnectTimeoutInMillis;
    @Value("${restTemplate.readTimeoutInMillis.fast:1000}")
    private Integer fastReadTimeoutInMillis;
    @Value("${restTemplate.connectionRequestTimeout.fast:1000}")
    private Integer fastConnectionRequestTimeout;

    @Value("${restTemplate.connectTimeoutInMillis.soon:500}")
    private Integer soonConnectTimeoutInMillis;
    @Value("${restTemplate.readTimeoutInMillis.soon:500}")
    private Integer soonReadTimeoutInMillis;
    @Value("${restTemplate.connectionRequestTimeout.soon:500}")
    private Integer soonConnectionRequestTimeout;

    @Value("${restTemplate.connectTimeoutInMillis.orderSys:500}")
    private Integer orderSysConnectTimeoutInMillis;
    @Value("${restTemplate.readTimeoutInMillis.orderSys:500}")
    private Integer orderSysReadTimeoutInMillis;
    @Value("${restTemplate.connectionRequestTimeout.orderSys:500}")
    private Integer orderSysConnectionRequestTimeout;

    @Value("${restTemplate.connectTimeoutInMillis.refund:500}")
    private Integer refundConnectTimeoutInMillis;
    @Value("${restTemplate.readTimeoutInMillis.refund:1000}")
    private Integer refundReadTimeoutInMillis;
    @Value("${restTemplate.connectionRequestTimeout.refund:1000}")
    private Integer refundConnectionRequestTimeout;

    @Value("${orderSys.sign.key:123456}")
    private String orderSysSignKey;
    @Value("${refund.sign.key:NiFUGtBgeWZA7pZD}")
    private String refundSignKey;

    @Bean
    public RestTemplate restTemplate(ClientHttpRequestFactory clientHttpRequestFactory) {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setRequestFactory(clientHttpRequestFactory);
        restTemplate.setMessageConverters(transConverters(restTemplate.getMessageConverters()));
        return restTemplate;
    }

    @Bean
    public RestTemplate fastRestTemplate(ClientHttpRequestFactory fastClientHttpRequestFactory) {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setRequestFactory(fastClientHttpRequestFactory);
        restTemplate.setMessageConverters(transConverters(restTemplate.getMessageConverters()));
        return restTemplate;
    }

    @Bean
    public RestTemplate soonRestTemplate(ClientHttpRequestFactory soonClientHttpRequestFactory) {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setRequestFactory(soonClientHttpRequestFactory);
        restTemplate.setMessageConverters(transConverters(restTemplate.getMessageConverters()));
        return restTemplate;
    }

    @Bean
    @LoadBalanced
    public RestTemplate lbRestTemplate(ClientHttpRequestFactory clientHttpRequestFactory) {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setRequestFactory(clientHttpRequestFactory);
        restTemplate.setMessageConverters(transConverters(restTemplate.getMessageConverters()));
        return restTemplate;
    }

    @Bean
    @LoadBalanced
    public RestTemplate fastLbRestTemplate(ClientHttpRequestFactory fastClientHttpRequestFactory) {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setRequestFactory(fastClientHttpRequestFactory);
        restTemplate.setMessageConverters(transConverters(restTemplate.getMessageConverters()));
        return restTemplate;
    }

    @Bean
    @LoadBalanced
    public RestTemplate soonLbRestTemplate(ClientHttpRequestFactory soonClientHttpRequestFactory) {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setRequestFactory(soonClientHttpRequestFactory);
        restTemplate.setMessageConverters(transConverters(restTemplate.getMessageConverters()));
        return restTemplate;
    }

    @Bean
    @LoadBalanced
    public RestTemplate orderSysLbRestTemplate(ClientHttpRequestFactory orderSysClientHttpRequestFactory) {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setRequestFactory(orderSysClientHttpRequestFactory);
        restTemplate.setMessageConverters(transConverters(restTemplate.getMessageConverters()));
        List<ClientHttpRequestInterceptor> interceptors = new LinkedList<>();
        interceptors.add(new SignatureInterceptor("vip-fulfillment-center", orderSysSignKey, MessageDigestAlgorithms.SHA_256));
        restTemplate.setInterceptors(interceptors);
        return restTemplate;
    }

    @Bean
    public RestTemplate orderSysRestTemplate(ClientHttpRequestFactory orderSysClientHttpRequestFactory) {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setRequestFactory(orderSysClientHttpRequestFactory);
        restTemplate.setMessageConverters(transConverters(restTemplate.getMessageConverters()));
        List<ClientHttpRequestInterceptor> interceptors = new LinkedList<>();
        interceptors.add(new SignatureInterceptor("vip-fulfillment-center", orderSysSignKey, MessageDigestAlgorithms.SHA_256));
        restTemplate.setInterceptors(interceptors);
        return restTemplate;
    }

    @Bean("refundLbRestTemplate")
    @LoadBalanced
    public RestTemplate RefundLbRestTemplate(ClientHttpRequestFactory refundClientHttpRequestFactory) {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setRequestFactory(refundClientHttpRequestFactory);
        restTemplate.setMessageConverters(transConverters(restTemplate.getMessageConverters()));
        List<ClientHttpRequestInterceptor> interceptors = new LinkedList<>();
        restTemplate.setInterceptors(interceptors);
        return restTemplate;
    }

    @Bean("refundRestTemplate")
    public RestTemplate RefundRestTemplate(ClientHttpRequestFactory refundClientHttpRequestFactory) {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setRequestFactory(refundClientHttpRequestFactory);
        restTemplate.setMessageConverters(transConverters(restTemplate.getMessageConverters()));
        List<ClientHttpRequestInterceptor> interceptors = new LinkedList<>();
        restTemplate.setInterceptors(interceptors);
        return restTemplate;
    }

    /*此写法与鹰眼有循环依赖问题
    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder restTemplateBuilder, ClientHttpRequestFactory clientHttpRequestFactory) {
        return restTemplateBuilder.requestFactory(() -> clientHttpRequestFactory).build();
    }*/

    @Bean
    public ClientHttpRequestFactory clientHttpRequestFactory(HttpClientBuilder httpClientBuilder) {
        HttpComponentsClientHttpRequestFactory clientHttpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        clientHttpRequestFactory.setHttpClient(httpClientBuilder.build());
        // 连接超时时间/毫秒
        clientHttpRequestFactory.setConnectTimeout(longConnectTimeoutInMillis);
        // 读写超时时间/毫秒
        clientHttpRequestFactory.setReadTimeout(longReadTimeoutInMillis);
        // 请求超时时间/毫秒
        clientHttpRequestFactory.setConnectionRequestTimeout(longConnectionRequestTimeout);
        return clientHttpRequestFactory;
    }

    @Bean
    public ClientHttpRequestFactory fastClientHttpRequestFactory(HttpClientBuilder httpClientBuilder) {
        HttpComponentsClientHttpRequestFactory clientHttpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        clientHttpRequestFactory.setHttpClient(httpClientBuilder.build());
        // 连接超时时间/毫秒
        clientHttpRequestFactory.setConnectTimeout(fastConnectTimeoutInMillis);
        // 读写超时时间/毫秒
        clientHttpRequestFactory.setReadTimeout(fastReadTimeoutInMillis);
        // 请求超时时间/毫秒
        clientHttpRequestFactory.setConnectionRequestTimeout(fastConnectionRequestTimeout);
        return clientHttpRequestFactory;
    }

    @Bean
    public ClientHttpRequestFactory soonClientHttpRequestFactory(HttpClientBuilder httpClientBuilder) {
        HttpComponentsClientHttpRequestFactory clientHttpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        clientHttpRequestFactory.setHttpClient(httpClientBuilder.build());
        // 连接超时时间/毫秒
        clientHttpRequestFactory.setConnectTimeout(soonConnectTimeoutInMillis);
        // 读写超时时间/毫秒
        clientHttpRequestFactory.setReadTimeout(soonReadTimeoutInMillis);
        // 请求超时时间/毫秒
        clientHttpRequestFactory.setConnectionRequestTimeout(soonConnectionRequestTimeout);
        return clientHttpRequestFactory;
    }

    @Bean
    public ClientHttpRequestFactory orderSysClientHttpRequestFactory(HttpClientBuilder httpClientBuilder) {
        HttpComponentsClientHttpRequestFactory clientHttpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        clientHttpRequestFactory.setHttpClient(httpClientBuilder.build());
        // 连接超时时间/毫秒
        clientHttpRequestFactory.setConnectTimeout(orderSysConnectTimeoutInMillis);
        // 读写超时时间/毫秒
        clientHttpRequestFactory.setReadTimeout(orderSysReadTimeoutInMillis);
        // 请求超时时间/毫秒
        clientHttpRequestFactory.setConnectionRequestTimeout(orderSysConnectionRequestTimeout);
        return clientHttpRequestFactory;
    }

    @Bean
    public ClientHttpRequestFactory refundClientHttpRequestFactory(HttpClientBuilder httpClientBuilder) {
        HttpComponentsClientHttpRequestFactory clientHttpRequestFactory = new HttpComponentsClientHttpRequestFactory();
        clientHttpRequestFactory.setHttpClient(httpClientBuilder.build());
        // 连接超时时间/毫秒
        clientHttpRequestFactory.setConnectTimeout(refundConnectTimeoutInMillis);
        // 读写超时时间/毫秒
        clientHttpRequestFactory.setReadTimeout(refundReadTimeoutInMillis);
        // 请求超时时间/毫秒
        clientHttpRequestFactory.setConnectionRequestTimeout(refundConnectionRequestTimeout);

        return clientHttpRequestFactory;
    }

    @Bean
    public HttpClientBuilder httpClientBuilder(HttpClientConnectionManager poolingConnectionManager) {
        HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
        httpClientBuilder.setConnectionManager(poolingConnectionManager);
        httpClientBuilder.evictExpiredConnections()
            .evictIdleConnections(60, java.util.concurrent.TimeUnit.SECONDS);
        return httpClientBuilder;
    }

    @Bean
    public HttpClientConnectionManager poolingConnectionManager() {
        PoolingHttpClientConnectionManager poolingConnectionManager = new PoolingHttpClientConnectionManager();
        // 整个连接池最大连接数
        poolingConnectionManager.setMaxTotal(1000);
        // 设定默认单个路由的最大连接数
        poolingConnectionManager.setDefaultMaxPerRoute(100);
        // 检查有效连接的间隔
        poolingConnectionManager.setValidateAfterInactivity(3000);
        return poolingConnectionManager;
    }

    private List<HttpMessageConverter<?>> transConverters(List<HttpMessageConverter<?>> oldMessageConverters) {
        MappingJackson2HttpMessageConverter jacksonConverter = new MappingJackson2HttpMessageConverter();
        //TODO:其他jackson配置根据需要自行配置
        List<MediaType> fastMediaTypes = new ArrayList<>();
        fastMediaTypes.add(MediaType.TEXT_HTML);
        fastMediaTypes.add(MediaType.APPLICATION_JSON);
        fastMediaTypes.add(MediaType.parseMediaType(MediaType.TEXT_PLAIN_VALUE + ";UTF-8"));
        jacksonConverter.setSupportedMediaTypes(fastMediaTypes);

        List<HttpMessageConverter<?>> messageConverters = new ArrayList<>();
        for (HttpMessageConverter converter : oldMessageConverters) {
            if (converter instanceof StringHttpMessageConverter) {
                messageConverters.add(new StringHttpMessageConverter(Charset.forName("UTF-8")));
            } else if (converter instanceof MappingJackson2HttpMessageConverter) {
                messageConverters.add(jacksonConverter);
            } else {
                messageConverters.add(converter);
            }
        }

        return messageConverters;
    }

}
