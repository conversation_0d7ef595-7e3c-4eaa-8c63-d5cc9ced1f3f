package com.iqiyi.vip.config;

import com.github.benmanes.caffeine.cache.CacheLoader;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.checkerframework.checker.nullness.qual.Nullable;

/**
 * <AUTHOR>
 * @date 2023/11/29 20:49
 */
@Slf4j
public class DefaultCacheLoader implements CacheLoader {

    @Nullable
    @Override
    public Object load(@NonNull Object o) throws Exception {
        log.info("[DefaultCacheLoader.load] o:{}", o);
        return null;
    }
}
