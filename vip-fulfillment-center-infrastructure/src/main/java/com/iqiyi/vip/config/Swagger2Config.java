package com.iqiyi.vip.config;

import io.swagger.annotations.ApiOperation;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * @Author: chengweigang
 * @Date: 2022/6/15 16:23
 *
 * swagger初始化
 */
@Profile({"dev", "test"})
@Configuration
@EnableSwagger2
public class Swagger2Config {

    @Bean
    public Docket createRestApi() {
        return new Docket(DocumentationType.OAS_30)
            .apiInfo(apiInfo())
            .select()
            //只扫描有ApiOperation注解的方法
            .apis(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class))
            .paths(PathSelectors.any())
            .build();
    }

    /**
     * 基本信息的配置，信息会在api文档上显示
     */
    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
            .title("接口文档")
            .description("接口文档")
            .termsOfServiceUrl("http://localhost:8080/status")
            .version("1.0")
            .build();
    }

}
