///**
// * Copyright 2020 iQiyi.com Inc. All Rights Reserved.
// */
//package com.iqiyi.vip.config;
//
//import org.apache.ibatis.plugin.Interceptor;
//import org.apache.ibatis.session.SqlSessionFactory;
//import org.mybatis.spring.SqlSessionFactoryBean;
//import org.mybatis.spring.SqlSessionTemplate;
//import org.mybatis.spring.annotation.MapperScan;
//import org.springframework.beans.factory.ObjectProvider;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.boot.jdbc.DataSourceBuilder;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Primary;
//import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
//import org.springframework.jdbc.datasource.DataSourceTransactionManager;
//import org.springframework.util.ObjectUtils;
//
//import javax.sql.DataSource;
//
///**
// * TiDB数据源配置
// *
// * <AUTHOR>
// * @version 1.0 2020年12月28日
// */
//@Configuration
//@MapperScan(basePackages = "com.iqiyi.vip.tidbmapper", sqlSessionFactoryRef = "tidbSqlSessionFactory")
//public class TiDBDataSourceConfig {
//
//    @Bean
//    @ConfigurationProperties("spring.datasource.tidb")
//    public DataSource tidbDataSource() {
//        return DataSourceBuilder.create().build();
//    }
//
//    @Bean
//    @Primary
//    public SqlSessionFactory tidbSqlSessionFactory(@Qualifier("tidbDataSource") DataSource dataSource, ObjectProvider<Interceptor[]> interceptorsProvider)
//        throws Exception {
//        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
//        bean.setDataSource(dataSource);
//        Interceptor[] interceptors = interceptorsProvider.getIfAvailable();
//        if (!ObjectUtils.isEmpty(interceptors)) {
//            bean.setPlugins(interceptors);
//        }
//        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mapper/tidb/*.xml"));
//        return bean.getObject();
//    }
//
//    @Bean
//    @Primary
//    public DataSourceTransactionManager tidbTransactionManager(@Qualifier("tidbDataSource") DataSource dataSource) {
//        return new DataSourceTransactionManager(dataSource);
//    }
//
//    @Bean
//    @Primary
//    public SqlSessionTemplate tidbSqlSessionTemplate(@Qualifier("tidbSqlSessionFactory") SqlSessionFactory sqlSessionFactory) throws Exception {
//        return new SqlSessionTemplate(sqlSessionFactory);
//    }
//}
