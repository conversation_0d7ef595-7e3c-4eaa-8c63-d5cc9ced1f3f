//package com.iqiyi.vip.config;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.jdbc.core.JdbcTemplate;
//
//import javax.sql.DataSource;
//
//import java.util.Collections;
//
//import com.iqiyi.vip.TaskConsumerStartListener;
//import com.iqiyi.vip.TaskProperties;
//import com.iqiyi.vip.monitor.CompositeMonitorReporter;
//import com.iqiyi.vip.monitor.DefaultTaskErrorHandler;
//import com.iqiyi.vip.monitor.ZkMonitorReporter;
//import com.iqiyi.vip.repository.ClusterAsyncTaskDao;
//import com.iqiyi.vip.repository.ClusterAsyncTaskManager;
//
///**
// * 分布式任务配置类
// *
// * <AUTHOR>
// */
//@Configuration
//public class AsyncTaskConfig {
//
//    @Value("${async.task.table.name:async_task}")
//    private String asyncTaskTable;
//
//    @Bean("jdbcTemplate")
//    public JdbcTemplate jdbcTemplate(@Autowired DataSource dataSource) {
//        return new JdbcTemplate(dataSource);
//    }
//
//    @Bean("clusterAsyncTaskDao")
//    public ClusterAsyncTaskDao clusterAsyncTaskDao(@Qualifier("jdbcTemplate") JdbcTemplate jdbcTemplate) {
//        ClusterAsyncTaskDao asyncTaskDao = new ClusterAsyncTaskDao(asyncTaskTable);
//        asyncTaskDao.setJdbcTemplate(jdbcTemplate);
//        return asyncTaskDao;
//    }
//
//    @Bean("taskErrorHandler")
//    public DefaultTaskErrorHandler taskErrorHandler() {
//
//        return new DefaultTaskErrorHandler();
//    }
//
//    @Bean("clusterAsyncTaskManager")
//    public ClusterAsyncTaskManager asyncTaskManager(@Qualifier("clusterAsyncTaskDao") ClusterAsyncTaskDao asyncTaskDao
//        , @Qualifier("taskErrorHandler") DefaultTaskErrorHandler taskErrorHandler) {
//        return new ClusterAsyncTaskManager(asyncTaskDao, taskErrorHandler);
//    }
//
//    @Bean
//    public TaskProperties taskProperties(){
//        return new TaskProperties();
//    }
//
//    @Bean
//    public ZkMonitorReporter zkMonitorReporter(){
//        return new ZkMonitorReporter();
//    }
//
//    @Bean("compositeMonitorReport")
//    public CompositeMonitorReporter compositeMonitorReporter(){
//        return new CompositeMonitorReporter(Collections.EMPTY_LIST);
//    }
//
//    @Bean
//    public TaskConsumerStartListener applicationStartListener(){
//        return new TaskConsumerStartListener();
//    }
//}
