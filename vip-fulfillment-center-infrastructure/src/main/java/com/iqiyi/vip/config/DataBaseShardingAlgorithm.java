package com.iqiyi.vip.config;

import org.apache.shardingsphere.api.sharding.standard.PreciseShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingValue;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2024/7/29 15:31
 */
@Component
public class DataBaseShardingAlgorithm implements PreciseShardingAlgorithm<Long> {

    @Override
    public String doSharding(Collection<String> collection, PreciseShardingValue<Long> shardingValue) {
        Long dataSourceSuffix = shardingValue.getValue() % 16;
        return String.format("%s%d", "fulfill-ds", dataSourceSuffix);
    }
}
