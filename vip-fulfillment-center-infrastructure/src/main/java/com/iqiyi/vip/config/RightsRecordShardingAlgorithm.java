package com.iqiyi.vip.config;

import org.apache.shardingsphere.api.sharding.standard.PreciseShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingValue;
import org.springframework.stereotype.Component;

import java.util.Collection;

/**
 * rights_record分表逻辑
 *
 * <AUTHOR>
 * @date 2024/7/29 15:31
 */
@Component
public class RightsRecordShardingAlgorithm implements PreciseShardingAlgorithm<Long> {

    @Override
    public String doSharding(Collection<String> collection, PreciseShardingValue<Long> shardingValue) {
        Long tableSuffix = shardingValue.getValue() / 100 % 100;
        return String.format("%s_%02d", shardingValue.getLogicTableName(), tableSuffix);
    }
}
