package com.iqiyi.vip.config;

import com.alibaba.fastjson.support.spring.GenericFastJsonRedisSerializer;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/17 21:11
 */
@Configuration
public class CacheConfig extends CachingConfigurerSupport {

    // 缓存的最大条数
    @Value("${caffeine.cache.maximum:1000}")
    private int caffeineMaximumSize;

    // 初始的缓存空间大小
    @Value("${caffeine.cache.initial:100}")
    private int caffeineInitialCapacity;
    // 内存过期时间 单位分钟
    @Value("${caffeine.cache.expire:5}")
    private int caffeineExpire;
    //redis过期时间 单位秒
    @Value("${redis.cache.expire:300}")
    private int redisDefaultExpireSeconds;

    /**
     * 配置缓存管理器
     *
     * @return 缓存管理器
     */
    @Bean("caffeineCacheManager")
    public CacheManager caffeineCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
            // 设置最后一次写入经过固定时间过期
            .expireAfterWrite(caffeineExpire, TimeUnit.MINUTES)
            // 初始的缓存空间大小
            .initialCapacity(caffeineInitialCapacity)
            // 缓存的最大条数
            .maximumSize(caffeineMaximumSize));
        return cacheManager;
    }


    /**
     * 往容器中添加RedisCacheManager容器，并设置序列化方式
     */
    @Bean("redisCacheManager")
    @Primary
    public CacheManager redisCacheManager(RedisTemplate redisTemplate) {
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashValueSerializer(new GenericFastJsonRedisSerializer());
        redisTemplate.setValueSerializer(new GenericFastJsonRedisSerializer());
        //1.创建RedisCacheWriter
        /**
         * 非锁方式：nonLockingRedisCacheWriter(RedisConnectionFactory connectionFactory);
         * 有锁方式：lockingRedisCacheWriter(RedisConnectionFactory connectionFactory);
         */
        RedisCacheWriter redisCacheWriter = RedisCacheWriter.nonLockingRedisCacheWriter(Objects.requireNonNull(redisTemplate.getConnectionFactory()));
        RedisCacheConfiguration redisCacheConfiguration = RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofSeconds(redisDefaultExpireSeconds));
        //5.创建RedisCacheManager(RedisCacheWriter redisCacheWriter, RedisCacheConfiguration redisCacheConfiguration)对象并返回
        RedisCacheManager rcm = new RedisCacheManager(redisCacheWriter, redisCacheConfiguration);
        return rcm;
    }

    @Bean(name = "jacksonRedisTemplate")
    public RedisTemplate<?, ?> jacksonRedisTemplate(RedisConnectionFactory factory) {
        RedisTemplate<?, ?> redisTemplate = new RedisTemplate();
        redisTemplate.setConnectionFactory(factory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        redisTemplate.setValueSerializer(new GenericJackson2JsonRedisSerializer(objectMapper));
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashValueSerializer(new StringRedisSerializer());
        return redisTemplate;
    }

    @Bean(name = "jacksonRedisCacheManager")
    public CacheManager jacksonRedisCacheManager(RedisTemplate jacksonRedisTemplate) {
        RedisCacheWriter redisCacheWriter = RedisCacheWriter.nonLockingRedisCacheWriter(Objects.requireNonNull(jacksonRedisTemplate.getConnectionFactory()));
        RedisCacheConfiguration redisCacheConfiguration = RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofSeconds(redisDefaultExpireSeconds));
        RedisCacheManager rcm = new RedisCacheManager(redisCacheWriter, redisCacheConfiguration);
        return rcm;
    }

    @Override
    @Bean
    public KeyGenerator keyGenerator() {
        return (target, method, params) -> {
            StringBuilder sb = new StringBuilder();
            sb.append(target.getClass().getName());
            sb.append(method.getName());
            for (Object obj : params) {
                sb.append(obj.toString());
            }
            return sb.toString();
        };
    }

}
