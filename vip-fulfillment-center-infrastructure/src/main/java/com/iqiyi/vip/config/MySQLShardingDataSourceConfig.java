///**
// * Copyright 2020 iQiyi.com Inc. All Rights Reserved.
// */
//package com.iqiyi.vip.config;
//
//import org.apache.ibatis.plugin.Interceptor;
//import org.apache.ibatis.session.SqlSessionFactory;
//import org.apache.shardingsphere.api.config.sharding.ShardingRuleConfiguration;
//import org.apache.shardingsphere.api.config.sharding.TableRuleConfiguration;
//import org.apache.shardingsphere.api.config.sharding.strategy.StandardShardingStrategyConfiguration;
//import org.apache.shardingsphere.shardingjdbc.api.ShardingDataSourceFactory;
//import org.mybatis.spring.SqlSessionFactoryBean;
//import org.mybatis.spring.SqlSessionTemplate;
//import org.mybatis.spring.annotation.MapperScan;
//import org.springframework.beans.factory.ObjectProvider;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.boot.jdbc.DataSourceBuilder;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Primary;
//import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
//import org.springframework.jdbc.datasource.DataSourceTransactionManager;
//import org.springframework.util.ObjectUtils;
//
//import javax.annotation.Resource;
//import javax.sql.DataSource;
//import java.sql.SQLException;
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.Properties;
//
///**
// * MySQL数据源配置
// *
// * <AUTHOR>
// * @version 1.0 2020年12月28日
// */
//@Configuration
//@MapperScan(basePackages = "com.iqiyi.vip.mapper", sqlSessionFactoryRef = "shardingSqlSessionFactory")
//public class MySQLShardingDataSourceConfig {
//
//    // 分表算法
//    @Resource
//    private ShardingAlgorithm shardingAlgorithm;
//
//    @Bean(name = "mysqlDataSource")
//    @Primary
//    @ConfigurationProperties(prefix = "spring.datasource.mysql")
//    public DataSource mysqlDataSource() {
//        return DataSourceBuilder.create().build();
//    }
//
//    // 创建数据源，需要把分库的库都传递进去
//    @Bean("dataSource")
//    public DataSource dataSource(@Qualifier("mysqlDataSource") DataSource mysqlDataSource) throws SQLException {
//        // 配置真实数据源
//        Map<String, DataSource> dataSourceMap = new HashMap<String, DataSource>();
//        dataSourceMap.put("ds", mysqlDataSource);
//        ShardingRuleConfiguration shardingRuleConfig = new ShardingRuleConfiguration();
//        shardingRuleConfig.setDefaultDataSourceName("ds");
//
//        // 如果有多个数据表需要分表，依次添加到这里
//        shardingRuleConfig.getTableRuleConfigs().add(getOrderTableRuleConfiguration());
//        Properties p = new Properties();
//        p.setProperty("sql.show", Boolean.FALSE.toString());
//
//        // 获取数据源对象
//        DataSource dataSource = ShardingDataSourceFactory.createDataSource(dataSourceMap, shardingRuleConfig, p);
//        return dataSource;
//    }
//
//    // 创建SessionFactory
//    @Bean(name = "shardingSqlSessionFactory")
//    public SqlSessionFactory shardingSqlSessionFactory(@Qualifier("dataSource") DataSource dataSource, ObjectProvider<Interceptor[]> interceptorsProvider)
//        throws Exception {
//        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
//        bean.setDataSource(dataSource);
//        Interceptor[] interceptors = interceptorsProvider.getIfAvailable();
//        if (!ObjectUtils.isEmpty(interceptors)) {
//            bean.setPlugins(interceptors);
//        }
//        PathMatchingResourcePatternResolver pathMatchingResourcePatternResolver = new PathMatchingResourcePatternResolver();
//        List<org.springframework.core.io.Resource> resources = new ArrayList<>();
//        resources.addAll(Arrays.asList(pathMatchingResourcePatternResolver.getResources("classpath:mapper/*.xml")));
//        resources.addAll(Arrays.asList(pathMatchingResourcePatternResolver.getResources("classpath:mapper/extend/*.xml")));
//        bean.setMapperLocations(resources.toArray(new org.springframework.core.io.Resource[resources.size()]));
//        return bean.getObject();
//    }
//
//    // 创建事务管理器
//    @Bean("shardingTransactionManger")
//    public DataSourceTransactionManager shardingTransactionManger(@Qualifier("dataSource") DataSource dataSource) {
//        return new DataSourceTransactionManager(dataSource);
//    }
//
//    // 创建SqlSessionTemplate
//    @Bean(name = "shardingSqlSessionTemplate")
//    public SqlSessionTemplate shardingSqlSessionTemplate(@Qualifier("shardingSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
//        return new SqlSessionTemplate(sqlSessionFactory);
//    }
//
//    // 订单表的分表规则配置
//    private TableRuleConfiguration getOrderTableRuleConfiguration() {
//        TableRuleConfiguration result = new TableRuleConfiguration("partner_rights_receive_record", "ds.partner_rights_receive_record_0$->{0..9},ds.partner_rights_receive_record_$->{10..99}");
//        result.setTableShardingStrategyConfig(new StandardShardingStrategyConfiguration("uid", shardingAlgorithm));
//        return result;
//    }
//
//}
