package com.iqiyi.vip.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.iqiyi.vip.domain.task.entity.AsyncTaskStat;
import com.iqiyi.vip.po.AsyncTaskRawPO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 异步任务数据处理工具类
 * 负责JSON解析和数据聚合，提升性能
 */
@Slf4j
public class AsyncTaskDataProcessor {
    
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    
    /**
     * 将原始数据转换为聚合统计数据
     * 在程序层进行JSON解析和聚合，避免SQL层的JSON函数开销
     * 
     * @param rawDataList 原始数据列表
     * @return 聚合后的统计数据列表
     */
    public static List<AsyncTaskStat> processAndAggregate(List<AsyncTaskRawPO> rawDataList) {
        if (rawDataList == null || rawDataList.isEmpty()) {
            return Collections.emptyList();
        }
        
        log.info("[AsyncTaskDataProcessor] Processing {} raw records", rawDataList.size());
        long startTime = System.currentTimeMillis();
        
        // 1. 解析JSON并过滤有效数据
        List<ParsedTaskData> parsedDataList = rawDataList.stream()
                .filter(Objects::nonNull) // 过滤null的rawData
                .filter(rawData -> rawData.getData() != null && StringUtils.hasText(rawData.getData())) // 在业务代码中判空data字段
                .map(AsyncTaskDataProcessor::parseTaskData)
                .filter(Objects::nonNull) // 过滤解析失败的数据
                // 不再过滤skuId为空的数据，允许skuId为null的数据上报
                .collect(Collectors.toList());
        
        log.info("[AsyncTaskDataProcessor] Parsed {} valid records from {} raw records", 
                parsedDataList.size(), rawDataList.size());
        
        // 2. 按聚合键分组并统计（按className、exeCount、skuId聚合）
        Map<AggregationKey, List<ParsedTaskData>> groupedData = parsedDataList.stream()
                .collect(Collectors.groupingBy(AsyncTaskDataProcessor::createAggregationKey));

        // 3. 转换为AsyncTaskStat对象
        List<AsyncTaskStat> result = groupedData.entrySet().stream()
                .map(entry -> createAsyncTaskStat(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());
        
        long costTime = System.currentTimeMillis() - startTime;
        log.info("[AsyncTaskDataProcessor] Aggregated {} groups from {} parsed records, cost: {}ms", 
                result.size(), parsedDataList.size(), costTime);
        
        return result;
    }
    
    /**
     * 解析单条任务数据的JSON
     */
    private static ParsedTaskData parseTaskData(AsyncTaskRawPO rawData) {
        try {
            // 防御性检查
            if (rawData == null) {
                log.warn("[AsyncTaskDataProcessor] rawData is null");
                return null;
            }

            if (!StringUtils.hasText(rawData.getData())) {
                log.warn("[AsyncTaskDataProcessor] data field is empty for id: {}", rawData.getId());
                return null;
            }

            JsonNode jsonNode = OBJECT_MAPPER.readTree(rawData.getData());
            if (jsonNode == null) {
                log.warn("[AsyncTaskDataProcessor] Failed to parse JSON for id: {}, jsonNode is null", rawData.getId());
                return null;
            }

            ParsedTaskData parsedData = new ParsedTaskData();
            parsedData.setId(rawData.getId());
            parsedData.setExeCount(rawData.getExeCount());
            parsedData.setClassName(rawData.getClassName());

            // 安全获取JSON字段，并记录空值情况
            String skuId = getJsonStringValue(jsonNode, "skuId");
            String uid = getJsonStringValue(jsonNode, "uid");
            String orderCode = getJsonStringValue(jsonNode, "orderCode");
            String sendRes = getJsonStringValue(jsonNode, "sendRes");

            parsedData.setSkuId(skuId);
            parsedData.setUid(uid);
            parsedData.setOrderCode(orderCode);
            parsedData.setSendRes(sendRes);



            return parsedData;
        } catch (Exception e) {
            log.warn("[AsyncTaskDataProcessor] Failed to parse JSON data for id: {}, className: {}, exeCount: {}, error: {}",
                    rawData != null ? rawData.getId() : "unknown",
                    rawData != null ? rawData.getClassName() : "unknown",
                    rawData != null ? rawData.getExeCount() : "unknown",
                    e.getMessage());
            return null;
        }
    }
    
    /**
     * 从JsonNode中安全获取字符串值
     */
    private static String getJsonStringValue(JsonNode jsonNode, String fieldName) {
        try {
            if (jsonNode == null || !StringUtils.hasText(fieldName)) {
                return null;
            }

            JsonNode fieldNode = jsonNode.get(fieldName);
            if (fieldNode == null || fieldNode.isNull()) {
                return null;
            }

            String value = fieldNode.asText();
            // 处理空字符串和"null"字符串
            return StringUtils.hasText(value) && !"null".equalsIgnoreCase(value) ? value : null;
        } catch (Exception e) {
            log.warn("[AsyncTaskDataProcessor] Failed to get JSON field '{}': {}", fieldName, e.getMessage());
            return null;
        }
    }
    
    /**
     * 创建聚合键（按className、exeCount、skuId聚合）
     */
    private static AggregationKey createAggregationKey(ParsedTaskData data) {
        if (data == null) {
            throw new IllegalArgumentException("ParsedTaskData cannot be null");
        }

        return new AggregationKey(
                data.getExeCount(),
                data.getClassName(),
                data.getSkuId()
        );
    }
    
    /**
     * 创建AsyncTaskStat对象
     * 按className、exeCount、skuId聚合，num为相加的数量
     */
    private static AsyncTaskStat createAsyncTaskStat(AggregationKey key, List<ParsedTaskData> dataList) {
        if (key == null) {
            throw new IllegalArgumentException("AggregationKey cannot be null");
        }
        if (CollectionUtils.isEmpty(dataList)) {
            throw new IllegalArgumentException("DataList cannot be null or empty");
        }

        AsyncTaskStat stat = new AsyncTaskStat();
        stat.setExeCount(key.getExeCount());
        stat.setClassName(key.getClassName());
        stat.setSkuId(key.getSkuId());

        // 聚合计数：相加的数量
        stat.setNum(dataList.size());

        // 取第一个记录的uid、orderCode、sendRes作为示例值
        ParsedTaskData firstData = dataList.get(0);
        if (firstData != null) {
            // 安全转换uid从String到Long
            String uidStr = firstData.getUid();
            if (StringUtils.hasText(uidStr)) {
                try {
                    stat.setUid(Long.valueOf(uidStr));
                } catch (NumberFormatException e) {
                    log.warn("[AsyncTaskDataProcessor] Failed to parse uid as Long: {}, className: {}, skuId: {}",
                            uidStr, key.getClassName(), key.getSkuId());
                    stat.setUid(null);
                }
            } else {
                stat.setUid(null);
            }

            stat.setOrderCode(firstData.getOrderCode());

            // 设置response（获取data中的sendRes字段）
            stat.setResponse(firstData.getSendRes());
        }

        return stat;
    }
    
    /**
     * 解析后的任务数据
     */
    @Data
    private static class ParsedTaskData {
        private Long id;
        private Integer exeCount;
        private String className;
        private String skuId;
        private String uid;
        private String orderCode;
        private String sendRes;
    }
    
    /**
     * 聚合键（按className、exeCount、skuId聚合）
     */
    @Data
    private static class AggregationKey {
        private final Integer exeCount;
        private final String className;
        private final String skuId;

        public AggregationKey(Integer exeCount, String className, String skuId) {
            this.exeCount = exeCount;
            this.className = className;
            this.skuId = skuId;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            AggregationKey that = (AggregationKey) o;
            return Objects.equals(exeCount, that.exeCount) &&
                   Objects.equals(className, that.className) &&
                   Objects.equals(skuId, that.skuId);
        }

        @Override
        public int hashCode() {
            return Objects.hash(exeCount, className, skuId);
        }
    }
}
