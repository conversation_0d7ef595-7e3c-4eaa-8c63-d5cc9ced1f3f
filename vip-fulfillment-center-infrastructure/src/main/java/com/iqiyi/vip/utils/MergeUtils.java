package com.iqiyi.vip.utils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.iqiyi.vip.po.RightsRecord;

/**
 * <AUTHOR>
 * @date 2023/8/31 17:28
 */
public class MergeUtils {

    public static List<RightsRecord> mergeRightRecordList(List<RightsRecord> list1, List<RightsRecord> list2) {
        Set<String> uniqueKeys = new HashSet<>();
        List<RightsRecord> resultList = new ArrayList<>();
        for (RightsRecord record : list1) {
            String uniqueKey = getRightsRecordUniqueKey(record);
            if (!uniqueKeys.contains(uniqueKey)) {
                resultList.add(record);
                uniqueKeys.add(uniqueKey);
            }
        }

        for (RightsRecord record : list2) {
            String uniqueKey = getRightsRecordUniqueKey(record);
            if (!uniqueKeys.contains(uniqueKey)) {
                resultList.add(record);
                uniqueKeys.add(uniqueKey);
            }
        }

        uniqueKeys.clear();

        return resultList;
    }

    private static String getRightsRecordUniqueKey(RightsRecord record) {
        return record.getUid() + "-" + record.getPromotionCode() + "-" + record.getOrderCode();
    }

}
