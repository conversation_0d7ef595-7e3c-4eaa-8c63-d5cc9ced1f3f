package com.iqiyi.vip.struct;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.iqiyi.vip.domain.spufulfillment.entity.SpuFulfillmentConfig;
import com.iqiyi.vip.domain.urlconfig.entity.UrlConfig;

/**
 * <AUTHOR>
 * @date 2023/9/28 14:08
 */
@Mapper
public interface UrlConfigStructMapper {

    UrlConfigStructMapper INSTANCE = Mappers.getMapper(UrlConfigStructMapper.class);

    UrlConfig po2do(com.iqiyi.vip.po.UrlConfig po);


}
