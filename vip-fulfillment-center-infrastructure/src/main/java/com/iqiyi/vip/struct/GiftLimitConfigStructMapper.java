package com.iqiyi.vip.struct;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.iqiyi.vip.domain.limit.entity.GiftLimit;
import com.iqiyi.vip.po.GiftLimitConfig;


/**
 * <AUTHOR>
 * @date 2023/9/8 14:11
 */
@Mapper
public interface GiftLimitConfigStructMapper {

    GiftLimitConfigStructMapper INSTANCE = Mappers.getMapper(GiftLimitConfigStructMapper.class);

    GiftLimit po2do(GiftLimitConfig po);

}
