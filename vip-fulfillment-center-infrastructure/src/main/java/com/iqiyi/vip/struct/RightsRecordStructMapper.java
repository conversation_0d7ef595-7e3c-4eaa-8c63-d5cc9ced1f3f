package com.iqiyi.vip.struct;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

import com.iqiyi.vip.domain.rights.entity.ReceiveRightRecord;

/**
 * 分库后的权益记录映射
 *
 * <AUTHOR>
 * @date 2023/8/31 11:24
 */
@Mapper
public interface RightsRecordStructMapper {

    RightsRecordStructMapper INSTANCE = Mappers.getMapper(RightsRecordStructMapper.class);

    com.iqiyi.vip.po.RightsRecord do2po(ReceiveRightRecord receiveRightRecordDo);

    ReceiveRightRecord po2do(com.iqiyi.vip.po.RightsRecord rightRecord);

    List<ReceiveRightRecord> po2doList(List<com.iqiyi.vip.po.RightsRecord> userPOS);

}
