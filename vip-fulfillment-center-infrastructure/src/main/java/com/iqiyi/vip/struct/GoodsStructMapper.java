package com.iqiyi.vip.struct;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

import com.iqiyi.vip.domain.goods.entity.Goods;

/**
 * <AUTHOR>
 * @date 2023/9/7 15:55
 */
@Mapper
public interface GoodsStructMapper {

    GoodsStructMapper INSTANCE = Mappers.getMapper(GoodsStructMapper.class);

    Goods po2do(com.iqiyi.vip.po.Goods po);

    List<Goods> poList2doList(List<com.iqiyi.vip.po.Goods> poList);
}
