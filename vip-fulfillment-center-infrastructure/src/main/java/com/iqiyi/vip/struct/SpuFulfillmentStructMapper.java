package com.iqiyi.vip.struct;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.iqiyi.vip.domain.spufulfillment.entity.SpuFulfillmentConfig;

/**
 * <AUTHOR>
 * @date 2023/9/28 11:24
 */
@Mapper
public interface SpuFulfillmentStructMapper {

    SpuFulfillmentStructMapper INSTANCE = Mappers.getMapper(SpuFulfillmentStructMapper.class);

    com.iqiyi.vip.po.SpuFulfillmentConfig do2po(SpuFulfillmentConfig dto);

    SpuFulfillmentConfig po2do(com.iqiyi.vip.po.SpuFulfillmentConfig po);

}
