package com.iqiyi.vip.struct;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

import com.iqiyi.vip.domain.fulfillment.entity.FulfillmentConfig;

/**
 * <AUTHOR>
 * @date 2023/7/31 16:23
 */
@Mapper
public interface FulfillmentConfigStructMapper {

    FulfillmentConfigStructMapper INSTANCE = Mappers.getMapper(FulfillmentConfigStructMapper.class);

    com.iqiyi.vip.po.FulfillmentConfig do2po(FulfillmentConfig fulfillmentConfig);

    FulfillmentConfig po2do(com.iqiyi.vip.po.FulfillmentConfig fulfillmentConfig);

    List<FulfillmentConfig> poList2doList(List<com.iqiyi.vip.po.FulfillmentConfig> fulfillmentConfig);

}
