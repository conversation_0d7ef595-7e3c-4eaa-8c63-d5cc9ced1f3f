package com.iqiyi.vip.po;

import java.util.Date;

public class FulfillmentConfig {
    private Long id;

    private String skuId;

    private String spuId;

    private String catalogId;

    private String name;

    private String actCode;

    private Integer sendType;

    private Integer receiveDeadlineType;

    private Integer receiveDeadlineRelativeDays;

    private Date receiveDeadlineAbsolute;

    private Integer refundPartnerType;

    private Date refundPartnerDeadline;

    private Integer actType;

    private Integer dealModule;

    private Integer version;

    private String operator;

    private String description;

    private Long deleteFlag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public String getSpuId() {
        return spuId;
    }

    public void setSpuId(String spuId) {
        this.spuId = spuId;
    }

    public String getCatalogId() {
        return catalogId;
    }

    public void setCatalogId(String catalogId) {
        this.catalogId = catalogId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getActCode() {
        return actCode;
    }

    public void setActCode(String actCode) {
        this.actCode = actCode;
    }

    public Integer getSendType() {
        return sendType;
    }

    public void setSendType(Integer sendType) {
        this.sendType = sendType;
    }

    public Integer getReceiveDeadlineType() {
        return receiveDeadlineType;
    }

    public void setReceiveDeadlineType(Integer receiveDeadlineType) {
        this.receiveDeadlineType = receiveDeadlineType;
    }

    public Integer getReceiveDeadlineRelativeDays() {
        return receiveDeadlineRelativeDays;
    }

    public void setReceiveDeadlineRelativeDays(Integer receiveDeadlineRelativeDays) {
        this.receiveDeadlineRelativeDays = receiveDeadlineRelativeDays;
    }

    public Date getReceiveDeadlineAbsolute() {
        return receiveDeadlineAbsolute;
    }

    public void setReceiveDeadlineAbsolute(Date receiveDeadlineAbsolute) {
        this.receiveDeadlineAbsolute = receiveDeadlineAbsolute;
    }

    public Integer getRefundPartnerType() {
        return refundPartnerType;
    }

    public void setRefundPartnerType(Integer refundPartnerType) {
        this.refundPartnerType = refundPartnerType;
    }

    public Date getRefundPartnerDeadline() {
        return refundPartnerDeadline;
    }

    public void setRefundPartnerDeadline(Date refundPartnerDeadline) {
        this.refundPartnerDeadline = refundPartnerDeadline;
    }

    public Integer getActType() {
        return actType;
    }

    public void setActType(Integer actType) {
        this.actType = actType;
    }

    public Integer getDealModule() {
        return dealModule;
    }

    public void setDealModule(Integer dealModule) {
        this.dealModule = dealModule;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Long deleteFlag) {
        this.deleteFlag = deleteFlag;
    }
}