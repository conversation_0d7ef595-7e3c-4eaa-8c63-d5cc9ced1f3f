package com.iqiyi.vip.po;

import java.util.Date;

public class Gift {
    private Long id;

    private String code;

    private String name;

    private Byte giftType;

    private Date startTime;

    private Date endTime;

    private Integer status;

    private Byte refundType;

    private Long sendNotifyUrlId;

    private Long refundNotifyUrlId;

    private String promotionCode;

    private Integer supportInventory;

    private Integer supportLimit;

    private String operator;

    private Date createTime;

    private Date updateTime;

    private Long beforeCheckUrlId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Byte getGiftType() {
        return giftType;
    }

    public void setGiftType(Byte giftType) {
        this.giftType = giftType;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Byte getRefundType() {
        return refundType;
    }

    public void setRefundType(Byte refundType) {
        this.refundType = refundType;
    }

    public Long getSendNotifyUrlId() {
        return sendNotifyUrlId;
    }

    public void setSendNotifyUrlId(Long sendNotifyUrlId) {
        this.sendNotifyUrlId = sendNotifyUrlId;
    }

    public Long getRefundNotifyUrlId() {
        return refundNotifyUrlId;
    }

    public void setRefundNotifyUrlId(Long refundNotifyUrlId) {
        this.refundNotifyUrlId = refundNotifyUrlId;
    }

    public String getPromotionCode() {
        return promotionCode;
    }

    public void setPromotionCode(String promotionCode) {
        this.promotionCode = promotionCode;
    }

    public Integer getSupportInventory() {
        return supportInventory;
    }

    public void setSupportInventory(Integer supportInventory) {
        this.supportInventory = supportInventory;
    }

    public Integer getSupportLimit() {
        return supportLimit;
    }

    public void setSupportLimit(Integer supportLimit) {
        this.supportLimit = supportLimit;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getBeforeCheckUrlId() {
        return beforeCheckUrlId;
    }

    public void setBeforeCheckUrlId(Long beforeCheckUrlId) {
        this.beforeCheckUrlId = beforeCheckUrlId;
    }
}