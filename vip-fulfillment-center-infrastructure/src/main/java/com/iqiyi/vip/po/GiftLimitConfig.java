package com.iqiyi.vip.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GiftLimitConfig {

    private Long id;

    private String giftCode;

    private Integer limitTimeType;

    private Integer limitMaxCount;

    private Integer status;

    private Date createTime;

    private Date updateTime;

}