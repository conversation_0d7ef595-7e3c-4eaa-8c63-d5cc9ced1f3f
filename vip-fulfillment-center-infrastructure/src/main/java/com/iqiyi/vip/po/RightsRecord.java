package com.iqiyi.vip.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

import com.iqiyi.vip.kms.mybatis.annotations.DecryptClass;
import com.iqiyi.vip.kms.mybatis.annotations.DecryptField;
import com.iqiyi.vip.kms.mybatis.annotations.EncryptClass;
import com.iqiyi.vip.kms.mybatis.annotations.EncryptField;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EncryptClass
@DecryptClass
public class RightsRecord {
    private Long id;

    private String orderCode;

    private String refundCode;

    private String presentOrderCode;

    private Long uid;

    @EncryptField
    @DecryptField
    private String encryptMobile;

    @EncryptField
    @DecryptField
    private String encryptAccount;

    private String promotionCode;

    private String skuId;

    private String actCode;

    private String activityCode;

    private String ruleCode;

    private Long conditionGroupId;

    private String presentNo;

    private Integer actType;

    private String partnerNo;

    private Integer receiveType;

    private Integer sendType;

    private Integer sendStatus;

    private Integer receiveStatus;

    private Integer refundStatus;

    private Integer callBackStatus;

    private Integer partnerVipUsed;

    private Long settlementPrice;

    private Integer settlementPercent;

    private Long refundSettlementPrice;

    private Date orderTime;

    private Date sendTime;

    private Date receiveDeadlineTime;

    private Date receiveTime;

    private Date refundTime;

    private String sendRes;

    private String refundRes;

    private Date iqRefundTime;

    private Integer type;

    private String refundMsg;

    private String cardCode;

    private String remark;

    private Date createTime;

    private Date updateTime;

    private Integer amount;

    private Integer refundAmount;

    private String tradeCode;

    @EncryptField
    @DecryptField
    private String couponCode;

}