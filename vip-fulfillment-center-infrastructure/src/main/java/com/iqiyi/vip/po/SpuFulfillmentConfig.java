package com.iqiyi.vip.po;

import java.util.Date;

public class SpuFulfillmentConfig {
    private Long id;

    private String spuId;

    private Integer giftType;

    private String name;

    private String giftCode;

    private String orderParams;

    private String skuParams;

    private String beforeCheckSkuParams;

    private String operator;

    private Date createTime;

    private Date updateTime;

    private Long deleteFlag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSpuId() {
        return spuId;
    }

    public void setSpuId(String spuId) {
        this.spuId = spuId;
    }

    public Integer getGiftType() {
        return giftType;
    }

    public void setGiftType(Integer giftType) {
        this.giftType = giftType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getGiftCode() {
        return giftCode;
    }

    public void setGiftCode(String giftCode) {
        this.giftCode = giftCode;
    }

    public String getOrderParams() {
        return orderParams;
    }

    public void setOrderParams(String orderParams) {
        this.orderParams = orderParams;
    }

    public String getSkuParams() {
        return skuParams;
    }

    public void setSkuParams(String skuParams) {
        this.skuParams = skuParams;
    }

    public String getBeforeCheckSkuParams() {
        return beforeCheckSkuParams;
    }

    public void setBeforeCheckSkuParams(String beforeCheckSkuParams) {
        this.beforeCheckSkuParams = beforeCheckSkuParams;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Long deleteFlag) {
        this.deleteFlag = deleteFlag;
    }
}