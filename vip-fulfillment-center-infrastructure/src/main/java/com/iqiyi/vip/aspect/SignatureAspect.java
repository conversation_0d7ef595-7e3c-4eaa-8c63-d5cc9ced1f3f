package com.iqiyi.vip.aspect;

import com.iqiyi.solar.config.client.spring.annotation.ConfigJsonValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.util.ContentCachingRequestWrapper;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.HashMap;
import java.util.Map;

@Aspect
@Slf4j
public class SignatureAspect {

    private static final String SIGNATURE_HEADER = "X-Signature";
    private static final String SOURCE_HEADER = "X-Source";
    /**
     * 通用密钥
     */
    private static final String UNIVERSAL_KEY = "universalKey";

    @Value("${signKey:123456}")
    private String signKey;

    @Pointcut("@annotation(signatureRequired)")
    public void signatureRequiredPointcut(SignatureRequired signatureRequired) {
    }

    @Before("signatureRequiredPointcut(signatureRequired)")
    public void verifySignature(SignatureRequired signatureRequired) throws Exception {

        // 获取HttpServletRequest对象
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = (HttpServletRequest) requestAttributes.resolveReference(RequestAttributes.REFERENCE_REQUEST);

        assert request != null;
        String requestSignature = request.getHeader(SIGNATURE_HEADER);
        String requestSource = request.getHeader(SOURCE_HEADER);
        String requestBody = null;
        // 获取请求参数
        Map<String, String[]> requestParams = signatureRequired.parameter() ? request.getParameterMap() : new HashMap<>();

        if(StringUtils.equalsIgnoreCase(requestSignature, UNIVERSAL_KEY)){
            return;
        }
        if (signatureRequired.body() && request instanceof ContentCachingRequestWrapper) {
            requestBody = new String(((ContentCachingRequestWrapper) request).getContentAsByteArray(), StandardCharsets.UTF_8);
        }

        if (CollectionUtils.isEmpty(requestParams) && StringUtils.isEmpty(requestBody)) {
            return;
        }
        if (StringUtils.isBlank(signKey)) {
            return;
        }
        String actualSignature = generateSignature(requestParams, requestBody, signKey, signatureRequired.algorithm());

        String sign = requestParams.get("sign") != null ? requestParams.get("sign")[0] : null;

        // 比较签名是否匹配
        if (!actualSignature.equals(sign) ) {
            throw new InvalidSignatureException(requestSource);
        }
    }

    private String generateSignature(Map<String, String[]> requestParams, String requestBody, String signatureKey, String algorithm)
        throws Exception {
        // 将请求参数和请求体按照一定规则排序和拼接
        // 可根据具体需求调整排序和拼接逻辑
        StringBuilder sb = new StringBuilder();
        requestParams.keySet().stream()
            .filter(key -> !"sign".equals(key))
            .sorted()
            .forEach(requestKey -> sb.append(requestKey).append("=").append(
                String.join(",", requestParams.get(requestKey))).append("&"));
        sb.append(requestBody);
        sb.append(signatureKey);

        // 使用指定的签名算法计算签名
        MessageDigest md = MessageDigest.getInstance(algorithm);
        md.update(sb.toString().getBytes());
        byte[] digest = md.digest();

        // 将签名转换为十六进制
        return byte2Hex(digest);
    }

    private static String byte2Hex(byte[] digest) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : digest) {
            hexString.append(String.format("%02x", b));
        }
        return hexString.toString();
    }
}