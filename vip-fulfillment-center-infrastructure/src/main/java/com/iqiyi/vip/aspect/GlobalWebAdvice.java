package com.iqiyi.vip.aspect;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.alibaba.csp.sentinel.slots.block.authority.AuthorityException;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeException;
import com.alibaba.csp.sentinel.slots.block.flow.FlowException;
import com.alibaba.csp.sentinel.slots.block.flow.param.ParamFlowException;
import com.alibaba.csp.sentinel.slots.system.SystemBlockException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.UndeclaredThrowableException;

import com.iqiyi.vip.dto.base.BaseResponse;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.exception.BizRuntimeException;

/**
 * <AUTHOR>
 * @date 2021/1/24 02:01
 */
@RestControllerAdvice
@Slf4j
public class GlobalWebAdvice {

    public static final ObjectMapper MAPPER = new ObjectMapper();

    @ExceptionHandler(value = BizRuntimeException.class)
    public BaseResponse<Void> bizExceptionHandler(HttpServletRequest request, BizRuntimeException e)
        throws JsonProcessingException {
        BaseResponse<Void> result = BaseResponse.create(e.getCodeEnum());
        log.warn("bizExceptionHandler requestURI:{}, parameterMap:{}, result:{}", request.getRequestURI(), MAPPER.writeValueAsString(request
            .getParameterMap()), result);
        return result;
    }

    @ExceptionHandler(value = {BlockException.class, SystemBlockException.class, ParamFlowException.class, FlowException.class,
        DegradeException.class, AuthorityException.class})
    public BaseResponse<Void> blockExceptionHandler(HttpServletRequest request, BlockException e)
        throws JsonProcessingException {
        BaseResponse<Void> result = BaseResponse.create(CodeEnum.SENTINEL_LIMIT);
        log.error("blockExceptionHandler requestURI:{}, parameterMap:{}, result:{}, e:", request.getRequestURI(), MAPPER.writeValueAsString(request
            .getParameterMap()), result, e);
        return result;
    }

    @ExceptionHandler(value = Exception.class)
    public BaseResponse<Void> defaultErrorHandler(HttpServletRequest request, Exception e) {
        if (e instanceof UndeclaredThrowableException) {
            UndeclaredThrowableException ute = (UndeclaredThrowableException) e;
            //限流异常处理
            if (ute.getUndeclaredThrowable() instanceof FlowException) {
                log.warn("flowExceptionHandler requestURI:{}", request.getRequestURI());
                return BaseResponse.create(CodeEnum.RESPONSE_LOAD_TOO_HIGH);
            }
        }

        String reason = e.getMessage();
        if (StringUtils.isBlank(reason)) {
            reason = CodeEnum.ERROR_SYSTEM.getMessage();
        }
        BaseResponse<Void> result = BaseResponse.create(CodeEnum.ERROR_SYSTEM);
        log.warn("defaultErrorHandler requestURI:{}, result:{}, reason:{}", request.getRequestURI(), result, reason, e);
        return result;
    }
}
