package com.iqiyi.vip.aspect;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import java.util.UUID;


/**
 * 日志切面
 *
 * <AUTHOR>
 * @date 2017/8/15
 */
@Aspect
@Component
@Slf4j
public class WebLogAspect {

    @Around("@annotation(webLog)")
    public Object actionAround(ProceedingJoinPoint joinPoint, WebLog webLog) throws Throwable {
        MDC.put("traceId", UUID.randomUUID().toString().replace("-", ""));
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Object result = "";
        try {
            result = joinPoint.proceed();
            return result;
        } finally {
            long cost = stopWatch.getTime();
            if (webLog.simplify()) {
                log.info("method:{}, param:{}, cost:{}", joinPoint.getSignature()
                    .getName(), joinPoint.getArgs(), cost);
            } else {
                log.info("method:{}, param:{}, result:{}, cost:{}", joinPoint.getSignature()
                    .getName(), joinPoint.getArgs(), result, cost);
            }
            MDC.clear();
        }
    }
}