package com.iqiyi.vip.aspect;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface SignatureRequired {

    /**
     * 默认签名算法:MD5
     * @return
     */
    String algorithm() default "MD5";

    /**
     * parameter是否参与签名,默认true
     * @return
     */
    boolean parameter() default true;

    /**
     * body是否参与签名,默认true
     * @return
     */
    boolean body() default true;

}
