package com.iqiyi.vip.aspect;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import com.iqiyi.vip.enums.SignTypeEnum;

import static java.lang.annotation.ElementType.METHOD;

/**
 * <AUTHOR>
 * @date 2023/10/16 13:30
 */
@Target(METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface SignCheck {

    SignTypeEnum type() default SignTypeEnum.MD5;

}
