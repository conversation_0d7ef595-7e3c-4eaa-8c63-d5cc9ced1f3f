<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.GoodsMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.po.Goods">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="sku_id" jdbcType="VARCHAR" property="skuId"/>
        <result column="gift_code" jdbcType="VARCHAR" property="giftCode"/>
        <result column="promotion_code" jdbcType="VARCHAR" property="promotionCode" />
    </resultMap>

    <sql id="Base_Column_List">
    id, sku_id, gift_code, promotion_code
    </sql>

    <select id="selectBySkuId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from partner_rights_goods
        where sku_id = #{skuId,jdbcType=VARCHAR}
    </select>

    <select id="selectByGiftCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from partner_rights_goods
        where gift_code = #{giftCode,jdbcType=VARCHAR}
    </select>

    <select id="selectByPromotionCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from partner_rights_goods
        where promotion_code = #{promotionCode,jdbcType=VARCHAR}
    </select>
</mapper>