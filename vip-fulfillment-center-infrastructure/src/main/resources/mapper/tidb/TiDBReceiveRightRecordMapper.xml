<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.tidbmapper.TiDBReceiveRightRecordMapper">
  <resultMap id="BaseResultMap" type="com.iqiyi.vip.po.RightsRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_code" jdbcType="VARCHAR" property="orderCode" />
    <result column="refund_code" jdbcType="VARCHAR" property="refundCode" />
    <result column="present_order_code" jdbcType="VARCHAR" property="presentOrderCode" />
    <result column="uid" jdbcType="BIGINT" property="uid" />
    <result column="encrypt_mobile" jdbcType="VARCHAR" property="encryptMobile" />
    <result column="encrypt_account" jdbcType="VARCHAR" property="encryptAccount" />
    <result column="promotion_code" jdbcType="VARCHAR" property="promotionCode" />
    <result column="activity_code" jdbcType="VARCHAR" property="activityCode" />
    <result column="rule_code" jdbcType="VARCHAR" property="ruleCode" />
    <result column="condition_group_id" jdbcType="BIGINT" property="conditionGroupId" />
    <result column="present_no" jdbcType="VARCHAR" property="presentNo" />
    <result column="act_type" jdbcType="INTEGER" property="actType" />
    <result column="partner_no" jdbcType="VARCHAR" property="partnerNo" />
    <result column="receive_type" jdbcType="INTEGER" property="receiveType" />
    <result column="send_type" jdbcType="INTEGER" property="sendType" />
    <result column="send_status" jdbcType="INTEGER" property="sendStatus" />
    <result column="receive_status" jdbcType="INTEGER" property="receiveStatus" />
    <result column="refund_status" jdbcType="INTEGER" property="refundStatus" />
    <result column="call_back_status" jdbcType="INTEGER" property="callBackStatus" />
    <result column="partner_vip_used" jdbcType="INTEGER" property="partnerVipUsed" />
    <result column="settlement_price" jdbcType="BIGINT" property="settlementPrice" />
    <result column="refund_settlement_price" jdbcType="BIGINT" property="refundSettlementPrice" />
    <result column="order_time" jdbcType="TIMESTAMP" property="orderTime" />
    <result column="send_time" jdbcType="TIMESTAMP" property="sendTime" />
    <result column="receive_deadline_time" jdbcType="TIMESTAMP" property="receiveDeadlineTime" />
    <result column="receive_time" jdbcType="TIMESTAMP" property="receiveTime" />
    <result column="refund_time" jdbcType="TIMESTAMP" property="refundTime" />
    <result column="send_res" jdbcType="VARCHAR" property="sendRes" />
    <result column="refund_res" jdbcType="VARCHAR" property="refundRes" />
    <result column="iq_refund_time" jdbcType="TIMESTAMP" property="iqRefundTime" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="refund_msg" jdbcType="VARCHAR" property="refundMsg" />
    <result column="card_code" jdbcType="VARCHAR" property="cardCode" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="amount" jdbcType="INTEGER" property="amount" />
    <result column="refund_amount" jdbcType="INTEGER" property="refundAmount" />
  </resultMap>
  
  <sql id="Base_Column_List">
    id, order_code, refund_code, present_order_code, uid,  encrypt_mobile,  encrypt_account, promotion_code,
    activity_code, rule_code, condition_group_id, present_no, act_type, partner_no, receive_type, 
    send_type, send_status, receive_status, refund_status, call_back_status, partner_vip_used, 
    settlement_price, refund_settlement_price, order_time, send_time, receive_deadline_time, 
    receive_time, refund_time, send_res, refund_res, iq_refund_time, type, refund_msg, 
    card_code, remark, create_time, update_time, amount, refund_amount
  </sql>

    <select id="selectByCon" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from partner_rights_receive_record_tidb
        <trim prefix="WHERE" prefixOverrides="AND |OR">
            uid = #{uid}
            <if test="orderCode != null and orderCode != ''">
                AND order_code = #{orderCode}
            </if>
            <if test="promotionCode != null and promotionCode != ''">
                AND promotion_code = #{promotionCode}
            </if>
            <if test="skuId != null and skuId != ''">
                AND sku_id = #{skuId,jdbcType=VARCHAR}
            </if>
            <if test="receiveType != null">
                AND receive_type = #{receiveType,jdbcType=INTEGER}
            </if>
             <if test="receiveStatus != null">
                AND receive_status = #{receiveStatus,jdbcType=INTEGER}
            </if>
            <if test="refundStatus != null">
                AND refund_status = #{refundStatus,jdbcType=INTEGER}
            </if>
            <if test="queryValidReceiveDeadlineTime != null and queryValidReceiveDeadlineTime == 1">
                AND (receive_deadline_time is null or receive_deadline_time = '' or receive_deadline_time  <![CDATA[ > ]]> #{date})
            </if>
            <if test="orderCodeList != null and orderCodeList.size() > 0">
                AND order_code IN
                <foreach collection="orderCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </trim>
    </select>

</mapper>