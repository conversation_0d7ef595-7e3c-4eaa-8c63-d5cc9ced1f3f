<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.GiftLimitConfigMapper">
  <resultMap id="BaseResultMap" type="com.iqiyi.vip.po.GiftLimitConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gift_code" jdbcType="VARCHAR" property="giftCode" />
    <result column="limit_time_type" jdbcType="TINYINT" property="limitTimeType" />
    <result column="limit_max_count" jdbcType="INTEGER" property="limitMaxCount" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, gift_code, limit_time_type, limit_max_count, status, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from partner_rights_gift_limit_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from partner_rights_gift_limit_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.iqiyi.vip.po.GiftLimitConfig">
    insert into partner_rights_gift_limit_config (id, gift_code, limit_time_type, 
      limit_max_count, status, create_time, 
      update_time)
    values (#{id,jdbcType=BIGINT}, #{giftCode,jdbcType=VARCHAR}, #{limitTimeType,jdbcType=TINYINT}, 
      #{limitMaxCount,jdbcType=INTEGER}, #{status,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.iqiyi.vip.po.GiftLimitConfig">
    insert into partner_rights_gift_limit_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="giftCode != null">
        gift_code,
      </if>
      <if test="limitTimeType != null">
        limit_time_type,
      </if>
      <if test="limitMaxCount != null">
        limit_max_count,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="giftCode != null">
        #{giftCode,jdbcType=VARCHAR},
      </if>
      <if test="limitTimeType != null">
        #{limitTimeType,jdbcType=TINYINT},
      </if>
      <if test="limitMaxCount != null">
        #{limitMaxCount,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iqiyi.vip.po.GiftLimitConfig">
    update partner_rights_gift_limit_config
    <set>
      <if test="giftCode != null">
        gift_code = #{giftCode,jdbcType=VARCHAR},
      </if>
      <if test="limitTimeType != null">
        limit_time_type = #{limitTimeType,jdbcType=TINYINT},
      </if>
      <if test="limitMaxCount != null">
        limit_max_count = #{limitMaxCount,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iqiyi.vip.po.GiftLimitConfig">
    update partner_rights_gift_limit_config
    set gift_code = #{giftCode,jdbcType=VARCHAR},
      limit_time_type = #{limitTimeType,jdbcType=TINYINT},
      limit_max_count = #{limitMaxCount,jdbcType=INTEGER},
      status = #{status,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>