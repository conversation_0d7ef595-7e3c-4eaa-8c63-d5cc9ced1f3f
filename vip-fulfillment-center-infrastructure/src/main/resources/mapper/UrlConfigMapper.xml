<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.UrlConfigMapper">
    <resultMap id="BaseResultMap" type="com.iqiyi.vip.po.UrlConfig">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="sync_url" jdbcType="VARCHAR" property="syncUrl"/>
        <result column="account_type" jdbcType="TINYINT" property="accountType" />
    </resultMap>

    <sql id="Base_Column_List">
    id, sync_url, account_type
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from partner_rights_url_config
        where id = #{id,jdbcType=BIGINT}
    </select>

</mapper>