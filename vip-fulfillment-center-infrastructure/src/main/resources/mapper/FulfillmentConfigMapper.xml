<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.FulfillmentConfigMapper">
  <resultMap id="BaseResultMap" type="com.iqiyi.vip.po.FulfillmentConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sku_id" jdbcType="VARCHAR" property="skuId" />
    <result column="spu_id" jdbcType="VARCHAR" property="spuId" />
    <result column="catalog_id" jdbcType="VARCHAR" property="catalogId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="act_code" jdbcType="VARCHAR" property="actCode" />
    <result column="send_type" jdbcType="BIT" property="sendType" />
    <result column="receive_deadline_type" jdbcType="BIT" property="receiveDeadlineType" />
    <result column="receive_deadline_relative_days" jdbcType="INTEGER" property="receiveDeadlineRelativeDays" />
    <result column="receive_deadline_absolute" jdbcType="TIMESTAMP" property="receiveDeadlineAbsolute" />
    <result column="refund_partner_type" jdbcType="BIT" property="refundPartnerType" />
    <result column="refund_partner_deadline" jdbcType="TIMESTAMP" property="refundPartnerDeadline" />
    <result column="act_type" jdbcType="BIT" property="actType" />
    <result column="deal_module" jdbcType="BIT" property="dealModule" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="delete_flag" jdbcType="BIGINT" property="deleteFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    id, sku_id, spu_id, catalog_id, name, act_code, send_type, receive_deadline_type, 
    receive_deadline_relative_days, receive_deadline_absolute, refund_partner_type, refund_partner_deadline, 
    act_type, deal_module, version, operator, description, delete_flag
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fulfillment_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from fulfillment_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.iqiyi.vip.po.FulfillmentConfig">
    insert into fulfillment_config (id, sku_id, spu_id, 
      catalog_id, name, act_code, 
      send_type, receive_deadline_type, receive_deadline_relative_days, 
      receive_deadline_absolute, refund_partner_type, 
      refund_partner_deadline, act_type, deal_module, 
      version, operator, description, 
      delete_flag)
    values (#{id,jdbcType=BIGINT}, #{skuId,jdbcType=VARCHAR}, #{spuId,jdbcType=VARCHAR}, 
      #{catalogId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{actCode,jdbcType=VARCHAR}, 
      #{sendType,jdbcType=BIT}, #{receiveDeadlineType,jdbcType=BIT}, #{receiveDeadlineRelativeDays,jdbcType=INTEGER}, 
      #{receiveDeadlineAbsolute,jdbcType=TIMESTAMP}, #{refundPartnerType,jdbcType=BIT}, 
      #{refundPartnerDeadline,jdbcType=TIMESTAMP}, #{actType,jdbcType=BIT}, #{dealModule,jdbcType=BIT}, 
      #{version,jdbcType=INTEGER}, #{operator,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{deleteFlag,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.iqiyi.vip.po.FulfillmentConfig">
    insert into fulfillment_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="skuId != null">
        sku_id,
      </if>
      <if test="spuId != null">
        spu_id,
      </if>
      <if test="catalogId != null">
        catalog_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="actCode != null">
        act_code,
      </if>
      <if test="sendType != null">
        send_type,
      </if>
      <if test="receiveDeadlineType != null">
        receive_deadline_type,
      </if>
      <if test="receiveDeadlineRelativeDays != null">
        receive_deadline_relative_days,
      </if>
      <if test="receiveDeadlineAbsolute != null">
        receive_deadline_absolute,
      </if>
      <if test="refundPartnerType != null">
        refund_partner_type,
      </if>
      <if test="refundPartnerDeadline != null">
        refund_partner_deadline,
      </if>
      <if test="actType != null">
        act_type,
      </if>
      <if test="dealModule != null">
        deal_module,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="operator != null">
        operator,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=VARCHAR},
      </if>
      <if test="spuId != null">
        #{spuId,jdbcType=VARCHAR},
      </if>
      <if test="catalogId != null">
        #{catalogId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="actCode != null">
        #{actCode,jdbcType=VARCHAR},
      </if>
      <if test="sendType != null">
        #{sendType,jdbcType=BIT},
      </if>
      <if test="receiveDeadlineType != null">
        #{receiveDeadlineType,jdbcType=BIT},
      </if>
      <if test="receiveDeadlineRelativeDays != null">
        #{receiveDeadlineRelativeDays,jdbcType=INTEGER},
      </if>
      <if test="receiveDeadlineAbsolute != null">
        #{receiveDeadlineAbsolute,jdbcType=TIMESTAMP},
      </if>
      <if test="refundPartnerType != null">
        #{refundPartnerType,jdbcType=BIT},
      </if>
      <if test="refundPartnerDeadline != null">
        #{refundPartnerDeadline,jdbcType=TIMESTAMP},
      </if>
      <if test="actType != null">
        #{actType,jdbcType=BIT},
      </if>
      <if test="dealModule != null">
        #{dealModule,jdbcType=BIT},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iqiyi.vip.po.FulfillmentConfig">
    update fulfillment_config
    <set>
      <if test="skuId != null">
        sku_id = #{skuId,jdbcType=VARCHAR},
      </if>
      <if test="spuId != null">
        spu_id = #{spuId,jdbcType=VARCHAR},
      </if>
      <if test="catalogId != null">
        catalog_id = #{catalogId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="actCode != null">
        act_code = #{actCode,jdbcType=VARCHAR},
      </if>
      <if test="sendType != null">
        send_type = #{sendType,jdbcType=BIT},
      </if>
      <if test="receiveDeadlineType != null">
        receive_deadline_type = #{receiveDeadlineType,jdbcType=BIT},
      </if>
      <if test="receiveDeadlineRelativeDays != null">
        receive_deadline_relative_days = #{receiveDeadlineRelativeDays,jdbcType=INTEGER},
      </if>
      <if test="receiveDeadlineAbsolute != null">
        receive_deadline_absolute = #{receiveDeadlineAbsolute,jdbcType=TIMESTAMP},
      </if>
      <if test="refundPartnerType != null">
        refund_partner_type = #{refundPartnerType,jdbcType=BIT},
      </if>
      <if test="refundPartnerDeadline != null">
        refund_partner_deadline = #{refundPartnerDeadline,jdbcType=TIMESTAMP},
      </if>
      <if test="actType != null">
        act_type = #{actType,jdbcType=BIT},
      </if>
      <if test="dealModule != null">
        deal_module = #{dealModule,jdbcType=BIT},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="operator != null">
        operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iqiyi.vip.po.FulfillmentConfig">
    update fulfillment_config
    set sku_id = #{skuId,jdbcType=VARCHAR},
      spu_id = #{spuId,jdbcType=VARCHAR},
      catalog_id = #{catalogId,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      act_code = #{actCode,jdbcType=VARCHAR},
      send_type = #{sendType,jdbcType=BIT},
      receive_deadline_type = #{receiveDeadlineType,jdbcType=BIT},
      receive_deadline_relative_days = #{receiveDeadlineRelativeDays,jdbcType=INTEGER},
      receive_deadline_absolute = #{receiveDeadlineAbsolute,jdbcType=TIMESTAMP},
      refund_partner_type = #{refundPartnerType,jdbcType=BIT},
      refund_partner_deadline = #{refundPartnerDeadline,jdbcType=TIMESTAMP},
      act_type = #{actType,jdbcType=BIT},
      deal_module = #{dealModule,jdbcType=BIT},
      version = #{version,jdbcType=INTEGER},
      operator = #{operator,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      delete_flag = #{deleteFlag,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>