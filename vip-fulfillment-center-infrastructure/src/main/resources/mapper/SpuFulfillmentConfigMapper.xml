<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.SpuFulfillmentConfigMapper">
  <resultMap id="BaseResultMap" type="com.iqiyi.vip.po.SpuFulfillmentConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="spu_id" jdbcType="VARCHAR" property="spuId" />
    <result column="gift_type" jdbcType="TINYINT" property="giftType" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="gift_code" jdbcType="VARCHAR" property="giftCode" />
    <result column="order_params" jdbcType="VARCHAR" property="orderParams" />
    <result column="sku_params" jdbcType="VARCHAR" property="skuParams" />
    <result column="before_check_sku_params" jdbcType="VARCHAR" property="beforeCheckSkuParams" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="delete_flag" jdbcType="BIGINT" property="deleteFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    id, spu_id, gift_type, name, gift_code, order_params, sku_params, before_check_sku_params, 
    operator, create_time, update_time, delete_flag
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from spu_fulfillment_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from spu_fulfillment_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.iqiyi.vip.po.SpuFulfillmentConfig">
    insert into spu_fulfillment_config (id, spu_id, gift_type, 
      name, gift_code, order_params, 
      sku_params, before_check_sku_params, operator, 
      create_time, update_time, delete_flag
      )
    values (#{id,jdbcType=BIGINT}, #{spuId,jdbcType=VARCHAR}, #{giftType,jdbcType=TINYINT}, 
      #{name,jdbcType=VARCHAR}, #{giftCode,jdbcType=VARCHAR}, #{orderParams,jdbcType=VARCHAR}, 
      #{skuParams,jdbcType=VARCHAR}, #{beforeCheckSkuParams,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{deleteFlag,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.iqiyi.vip.po.SpuFulfillmentConfig">
    insert into spu_fulfillment_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="spuId != null">
        spu_id,
      </if>
      <if test="giftType != null">
        gift_type,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="giftCode != null">
        gift_code,
      </if>
      <if test="orderParams != null">
        order_params,
      </if>
      <if test="skuParams != null">
        sku_params,
      </if>
      <if test="beforeCheckSkuParams != null">
        before_check_sku_params,
      </if>
      <if test="operator != null">
        operator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="spuId != null">
        #{spuId,jdbcType=VARCHAR},
      </if>
      <if test="giftType != null">
        #{giftType,jdbcType=TINYINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="giftCode != null">
        #{giftCode,jdbcType=VARCHAR},
      </if>
      <if test="orderParams != null">
        #{orderParams,jdbcType=VARCHAR},
      </if>
      <if test="skuParams != null">
        #{skuParams,jdbcType=VARCHAR},
      </if>
      <if test="beforeCheckSkuParams != null">
        #{beforeCheckSkuParams,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iqiyi.vip.po.SpuFulfillmentConfig">
    update spu_fulfillment_config
    <set>
      <if test="spuId != null">
        spu_id = #{spuId,jdbcType=VARCHAR},
      </if>
      <if test="giftType != null">
        gift_type = #{giftType,jdbcType=TINYINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="giftCode != null">
        gift_code = #{giftCode,jdbcType=VARCHAR},
      </if>
      <if test="orderParams != null">
        order_params = #{orderParams,jdbcType=VARCHAR},
      </if>
      <if test="skuParams != null">
        sku_params = #{skuParams,jdbcType=VARCHAR},
      </if>
      <if test="beforeCheckSkuParams != null">
        before_check_sku_params = #{beforeCheckSkuParams,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iqiyi.vip.po.SpuFulfillmentConfig">
    update spu_fulfillment_config
    set spu_id = #{spuId,jdbcType=VARCHAR},
      gift_type = #{giftType,jdbcType=TINYINT},
      name = #{name,jdbcType=VARCHAR},
      gift_code = #{giftCode,jdbcType=VARCHAR},
      order_params = #{orderParams,jdbcType=VARCHAR},
      sku_params = #{skuParams,jdbcType=VARCHAR},
      before_check_sku_params = #{beforeCheckSkuParams,jdbcType=VARCHAR},
      operator = #{operator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      delete_flag = #{deleteFlag,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>