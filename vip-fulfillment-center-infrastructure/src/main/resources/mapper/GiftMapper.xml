<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.GiftMapper">
  <resultMap id="BaseResultMap" type="com.iqiyi.vip.po.Gift">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="gift_type" jdbcType="TINYINT" property="giftType" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="refund_type" jdbcType="TINYINT" property="refundType" />
    <result column="send_notify_url_id" jdbcType="BIGINT" property="sendNotifyUrlId" />
    <result column="refund_notify_url_id" jdbcType="BIGINT" property="refundNotifyUrlId" />
    <result column="promotion_code" jdbcType="VARCHAR" property="promotionCode" />
    <result column="support_inventory" jdbcType="INTEGER" property="supportInventory" />
    <result column="support_limit" jdbcType="INTEGER" property="supportLimit" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="before_check_url_id" jdbcType="BIGINT" property="beforeCheckUrlId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, code, name, gift_type, start_time, end_time, status, refund_type, send_notify_url_id, 
    refund_notify_url_id, promotion_code, support_inventory, support_limit, operator, 
    create_time, update_time, before_check_url_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from partner_rights_gift
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from partner_rights_gift
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.iqiyi.vip.po.Gift">
    insert into partner_rights_gift (id, code, name, 
      gift_type, start_time, end_time, 
      status, refund_type, send_notify_url_id, 
      refund_notify_url_id, promotion_code, support_inventory, 
      support_limit, operator, create_time, 
      update_time, before_check_url_id)
    values (#{id,jdbcType=BIGINT}, #{code,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{giftType,jdbcType=TINYINT}, #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, 
      #{status,jdbcType=INTEGER}, #{refundType,jdbcType=TINYINT}, #{sendNotifyUrlId,jdbcType=BIGINT}, 
      #{refundNotifyUrlId,jdbcType=BIGINT}, #{promotionCode,jdbcType=VARCHAR}, #{supportInventory,jdbcType=INTEGER}, 
      #{supportLimit,jdbcType=INTEGER}, #{operator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{beforeCheckUrlId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.iqiyi.vip.po.Gift">
    insert into partner_rights_gift
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="giftType != null">
        gift_type,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="refundType != null">
        refund_type,
      </if>
      <if test="sendNotifyUrlId != null">
        send_notify_url_id,
      </if>
      <if test="refundNotifyUrlId != null">
        refund_notify_url_id,
      </if>
      <if test="promotionCode != null">
        promotion_code,
      </if>
      <if test="supportInventory != null">
        support_inventory,
      </if>
      <if test="supportLimit != null">
        support_limit,
      </if>
      <if test="operator != null">
        operator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="beforeCheckUrlId != null">
        before_check_url_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="giftType != null">
        #{giftType,jdbcType=TINYINT},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="refundType != null">
        #{refundType,jdbcType=TINYINT},
      </if>
      <if test="sendNotifyUrlId != null">
        #{sendNotifyUrlId,jdbcType=BIGINT},
      </if>
      <if test="refundNotifyUrlId != null">
        #{refundNotifyUrlId,jdbcType=BIGINT},
      </if>
      <if test="promotionCode != null">
        #{promotionCode,jdbcType=VARCHAR},
      </if>
      <if test="supportInventory != null">
        #{supportInventory,jdbcType=INTEGER},
      </if>
      <if test="supportLimit != null">
        #{supportLimit,jdbcType=INTEGER},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="beforeCheckUrlId != null">
        #{beforeCheckUrlId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iqiyi.vip.po.Gift">
    update partner_rights_gift
    <set>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="giftType != null">
        gift_type = #{giftType,jdbcType=TINYINT},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="refundType != null">
        refund_type = #{refundType,jdbcType=TINYINT},
      </if>
      <if test="sendNotifyUrlId != null">
        send_notify_url_id = #{sendNotifyUrlId,jdbcType=BIGINT},
      </if>
      <if test="refundNotifyUrlId != null">
        refund_notify_url_id = #{refundNotifyUrlId,jdbcType=BIGINT},
      </if>
      <if test="promotionCode != null">
        promotion_code = #{promotionCode,jdbcType=VARCHAR},
      </if>
      <if test="supportInventory != null">
        support_inventory = #{supportInventory,jdbcType=INTEGER},
      </if>
      <if test="supportLimit != null">
        support_limit = #{supportLimit,jdbcType=INTEGER},
      </if>
      <if test="operator != null">
        operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="beforeCheckUrlId != null">
        before_check_url_id = #{beforeCheckUrlId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iqiyi.vip.po.Gift">
    update partner_rights_gift
    set code = #{code,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      gift_type = #{giftType,jdbcType=TINYINT},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=INTEGER},
      refund_type = #{refundType,jdbcType=TINYINT},
      send_notify_url_id = #{sendNotifyUrlId,jdbcType=BIGINT},
      refund_notify_url_id = #{refundNotifyUrlId,jdbcType=BIGINT},
      promotion_code = #{promotionCode,jdbcType=VARCHAR},
      support_inventory = #{supportInventory,jdbcType=INTEGER},
      support_limit = #{supportLimit,jdbcType=INTEGER},
      operator = #{operator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      before_check_url_id = #{beforeCheckUrlId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>