<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.SpuFulfillmentConfigMapper">

    <select id="selectBySpuId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from spu_fulfillment_config
        where spu_id = #{spuId,jdbcType=VARCHAR} and delete_flag = 0
    </select>

    <select id="selectByGiftType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from spu_fulfillment_config
        where gift_type = #{giftType,jdbcType=TINYINT} and spu_id = 'all_spu' and delete_flag = 0
    </select>
</mapper>