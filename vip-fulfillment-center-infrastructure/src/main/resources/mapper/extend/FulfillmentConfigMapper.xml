<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.FulfillmentConfigMapper">

    <select id="selectBySkuIdAndActCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fulfillment_config
        <where>
            sku_id = #{skuId,jdbcType=VARCHAR}
            <if test="actCode != null and actCode!=''">
                and act_code = #{actCode,jdbcType=VARCHAR}
            </if>
            <if test="actCode == null or actCode==''">
                and (act_code is null or act_code = '')
            </if>
            and delete_flag = 0
        </where>
    </select>

    <select id="selectBySkuId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fulfillment_config
        <where>
            sku_id = #{skuId,jdbcType=VARCHAR}
            and delete_flag = 0
        </where>
    </select>

    <update id="updateInvalidBySkuId" parameterType="com.iqiyi.vip.po.FulfillmentConfig">
        update fulfillment_config SET `delete_flag` = #{deleteFlag,jdbcType=BIGINT}
        where sku_id = #{skuId,jdbcType=VARCHAR}
    </update>
</mapper>