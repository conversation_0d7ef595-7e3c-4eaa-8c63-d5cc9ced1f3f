<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.RightsRecordMapper">

    <select id="selectByUidAndOrderCodeAndPromotionCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rights_record
        where uid = #{uid}
        and order_code = #{orderCode}
        and promotion_code = #{promotionCode}
    </select>

    <select id="selectByUidAndOrderCodeAndSkuId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rights_record
        where uid = #{uid}
        and order_code = #{orderCode}
        and sku_id = #{skuId,jdbcType=VARCHAR}
    </select>

    <update id="updateByOrderCodeAndPromotionCodeSelective" parameterType="com.iqiyi.vip.po.RightsRecord">
        update rights_record
        <set>
            <if test="orderCode != null">
                order_code = #{orderCode,jdbcType=VARCHAR},
            </if>
            <if test="refundCode != null">
                refund_code = #{refundCode,jdbcType=VARCHAR},
            </if>
            <if test="presentOrderCode != null">
                present_order_code = #{presentOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="encryptMobile != null">
                encrypt_mobile = #{encryptMobile,jdbcType=VARCHAR},
            </if>
            <if test="encryptAccount != null">
                encrypt_account = #{encryptAccount,jdbcType=VARCHAR},
            </if>
            <if test="refundAmount != null">
                refund_amount = #{refundAmount,jdbcType=INTEGER},
            </if>

            <if test="promotionCode != null">
                promotion_code = #{promotionCode,jdbcType=VARCHAR},
            </if>
            <if test="activityCode != null">
                activity_code = #{activityCode,jdbcType=VARCHAR},
            </if>
            <if test="ruleCode != null">
                rule_code = #{ruleCode,jdbcType=VARCHAR},
            </if>
            <if test="conditionGroupId != null">
                condition_group_id = #{conditionGroupId,jdbcType=BIGINT},
            </if>
            <if test="presentNo != null">
                present_no = #{presentNo,jdbcType=VARCHAR},
            </if>
            <if test="actType != null">
                act_type = #{actType,jdbcType=INTEGER},
            </if>
            <if test="partnerNo != null">
                partner_no = #{partnerNo,jdbcType=VARCHAR},
            </if>
            <if test="receiveType != null">
                receive_type = #{receiveType,jdbcType=INTEGER},
            </if>
            <if test="sendType != null">
                send_type = #{sendType,jdbcType=INTEGER},
            </if>
            <if test="sendStatus != null">
                send_status = #{sendStatus,jdbcType=INTEGER},
            </if>
            <if test="receiveStatus != null">
                receive_status = #{receiveStatus,jdbcType=INTEGER},
            </if>
            <if test="refundStatus != null">
                refund_status = #{refundStatus,jdbcType=INTEGER},
            </if>
            <if test="callBackStatus != null">
                call_back_status = #{callBackStatus,jdbcType=INTEGER},
            </if>
            <if test="partnerVipUsed != null">
                partner_vip_used = #{partnerVipUsed,jdbcType=INTEGER},
            </if>
            <if test="settlementPrice != null">
                settlement_price = #{settlementPrice,jdbcType=BIGINT},
            </if>
            <!--            <if test="refundSettlementPrice != null">-->
            <!--                refund_settlement_price = #{refundSettlementPrice,jdbcType=BIGINT},-->
            <!--            </if>-->
            <if test="orderTime != null">
                order_time = #{orderTime,jdbcType=TIMESTAMP},
            </if>
            <if test="sendTime != null">
                send_time = #{sendTime,jdbcType=TIMESTAMP},
            </if>
            <if test="receiveDeadlineTime != null">
                receive_deadline_time = #{receiveDeadlineTime,jdbcType=TIMESTAMP},
            </if>
            <if test="receiveTime != null">
                receive_time = #{receiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="refundTime != null">
                refund_time = #{refundTime,jdbcType=TIMESTAMP},
            </if>
            <if test="sendRes != null">
                send_res = #{sendRes,jdbcType=VARCHAR},
            </if>
            <if test="refundRes != null">
                refund_res = #{refundRes,jdbcType=VARCHAR},
            </if>
            <if test="iqRefundTime != null">
                iq_refund_time = #{iqRefundTime,jdbcType=TIMESTAMP},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=INTEGER},
            </if>
            <if test="refundMsg != null">
                refund_msg = #{refundMsg,jdbcType=VARCHAR},
            </if>
            <if test="cardCode != null">
                card_code = #{cardCode,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="settlementPercent != null">
                settlement_percent = #{settlementPercent,jdbcType=INTEGER},
            </if>
            <if test="couponCode != null">
                coupon_code = #{couponCode,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=INTEGER},
            </if>
        </set>
        where order_code = #{orderCode,jdbcType=VARCHAR}
        and promotion_code = #{promotionCode,jdbcType=VARCHAR}
        and uid = #{uid}
    </update>

    <update id="updateCallBackStatus" parameterType="com.iqiyi.vip.po.RightsRecord">
        update rights_record
        <set>
            call_back_status = #{callBackStatus,jdbcType=INTEGER}
        </set>
        where order_code = #{orderCode,jdbcType=VARCHAR}
        and promotion_code = #{promotionCode,jdbcType=VARCHAR}
        and uid = #{uid}
    </update>

    <select id="selectByCon" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rights_record
        <trim prefix="WHERE" prefixOverrides="AND |OR">
            uid = #{uid}
            <if test="orderCode != null and orderCode != ''">
                AND order_code = #{orderCode}
            </if>
            <if test="promotionCode != null and promotionCode != ''">
                AND promotion_code = #{promotionCode}
            </if>
            <if test="skuId != null and skuId != ''">
                AND sku_id = #{skuId,jdbcType=VARCHAR}
            </if>
            <if test="receiveType != null">
                AND receive_type = #{receiveType,jdbcType=INTEGER}
            </if>
            <if test="receiveStatus != null">
                AND receive_status = #{receiveStatus,jdbcType=INTEGER}
            </if>
            <if test="refundStatus != null">
                AND refund_status = #{refundStatus,jdbcType=INTEGER}
            </if>
            <if test="queryValidReceiveDeadlineTime != null and queryValidReceiveDeadlineTime == 1">
                AND (receive_deadline_time is null or receive_deadline_time = '' or receive_deadline_time  <![CDATA[ > ]]> #{date})
            </if>
            <if test="orderCodeList != null and orderCodeList.size() > 0">
                AND order_code IN
                <foreach collection="orderCodeList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </trim>
    </select>

</mapper>