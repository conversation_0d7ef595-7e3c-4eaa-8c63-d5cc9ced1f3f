<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.AsyncTaskMapper">

    <!-- AsyncTaskRawPO结果映射 -->
    <resultMap id="AsyncTaskRawResultMap" type="com.iqiyi.vip.po.AsyncTaskRawPO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="exe_count" jdbcType="INTEGER" property="exeCount" />
        <result column="class_name" jdbcType="VARCHAR" property="className" />
        <result column="data" jdbcType="VARCHAR" property="data" />
    </resultMap>

    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        id, exe_count, class_name, data
    </sql>

    <!-- 优化后的查询：简化SQL，在程序层处理JSON解析和聚合 -->
    <select id="selectExeCountStatWithRightsInfo" resultMap="AsyncTaskRawResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
            fulfillment_center_async_task
        WHERE
            exe_count >= 1 AND run_time > NOW()
        LIMIT 5000
    </select>

</mapper>