<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iqiyi.vip.mapper.RightsRecordMapper">
  <resultMap id="BaseResultMap" type="com.iqiyi.vip.po.RightsRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_code" jdbcType="VARCHAR" property="orderCode" />
    <result column="refund_code" jdbcType="VARCHAR" property="refundCode" />
    <result column="present_order_code" jdbcType="VARCHAR" property="presentOrderCode" />
    <result column="uid" jdbcType="BIGINT" property="uid" />
    <result column="encrypt_mobile" jdbcType="VARCHAR" property="encryptMobile" />
    <result column="encrypt_account" jdbcType="VARCHAR" property="encryptAccount" />
    <result column="promotion_code" jdbcType="VARCHAR" property="promotionCode" />
    <result column="sku_id" jdbcType="VARCHAR" property="skuId" />
    <result column="act_code" jdbcType="VARCHAR" property="actCode" />
    <result column="activity_code" jdbcType="VARCHAR" property="activityCode" />
    <result column="rule_code" jdbcType="VARCHAR" property="ruleCode" />
    <result column="condition_group_id" jdbcType="BIGINT" property="conditionGroupId" />
    <result column="present_no" jdbcType="VARCHAR" property="presentNo" />
    <result column="act_type" jdbcType="INTEGER" property="actType" />
    <result column="partner_no" jdbcType="VARCHAR" property="partnerNo" />
    <result column="receive_type" jdbcType="INTEGER" property="receiveType" />
    <result column="send_type" jdbcType="INTEGER" property="sendType" />
    <result column="send_status" jdbcType="INTEGER" property="sendStatus" />
    <result column="receive_status" jdbcType="INTEGER" property="receiveStatus" />
    <result column="refund_status" jdbcType="INTEGER" property="refundStatus" />
    <result column="call_back_status" jdbcType="INTEGER" property="callBackStatus" />
    <result column="partner_vip_used" jdbcType="INTEGER" property="partnerVipUsed" />
    <result column="settlement_price" jdbcType="BIGINT" property="settlementPrice" />
    <result column="settlement_percent" jdbcType="INTEGER" property="settlementPercent" />
    <result column="refund_settlement_price" jdbcType="BIGINT" property="refundSettlementPrice" />
    <result column="order_time" jdbcType="TIMESTAMP" property="orderTime" />
    <result column="send_time" jdbcType="TIMESTAMP" property="sendTime" />
    <result column="receive_deadline_time" jdbcType="TIMESTAMP" property="receiveDeadlineTime" />
    <result column="receive_time" jdbcType="TIMESTAMP" property="receiveTime" />
    <result column="refund_time" jdbcType="TIMESTAMP" property="refundTime" />
    <result column="send_res" jdbcType="VARCHAR" property="sendRes" />
    <result column="refund_res" jdbcType="VARCHAR" property="refundRes" />
    <result column="iq_refund_time" jdbcType="TIMESTAMP" property="iqRefundTime" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="refund_msg" jdbcType="VARCHAR" property="refundMsg" />
    <result column="card_code" jdbcType="VARCHAR" property="cardCode" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="amount" jdbcType="INTEGER" property="amount" />
    <result column="refund_amount" jdbcType="INTEGER" property="refundAmount" />
    <result column="trade_code" jdbcType="VARCHAR" property="tradeCode" />
    <result column="coupon_code" jdbcType="VARCHAR" property="couponCode" />
  </resultMap>
  <sql id="Base_Column_List">
    id, order_code, refund_code, present_order_code, uid, encrypt_mobile, encrypt_account, 
    promotion_code, sku_id, act_code, activity_code, rule_code, condition_group_id, present_no, 
    act_type, partner_no, receive_type, send_type, send_status, receive_status, refund_status, 
    call_back_status, partner_vip_used, settlement_price, settlement_percent, refund_settlement_price, 
    order_time, send_time, receive_deadline_time, receive_time, refund_time, send_res, 
    refund_res, iq_refund_time, type, refund_msg, card_code, remark, create_time, update_time, 
    amount, refund_amount, trade_code, coupon_code
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from rights_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from rights_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.iqiyi.vip.po.RightsRecord">
    insert into rights_record (id, order_code, refund_code, 
      present_order_code, uid, encrypt_mobile, 
      encrypt_account, promotion_code, sku_id, 
      act_code, activity_code, rule_code, 
      condition_group_id, present_no, act_type, 
      partner_no, receive_type, send_type, 
      send_status, receive_status, refund_status, 
      call_back_status, partner_vip_used, settlement_price, 
      settlement_percent, refund_settlement_price, 
      order_time, send_time, receive_deadline_time, 
      receive_time, refund_time, send_res, 
      refund_res, iq_refund_time, type, 
      refund_msg, card_code, remark, 
      create_time, update_time, amount, 
      refund_amount, trade_code, coupon_code
      )
    values (#{id,jdbcType=BIGINT}, #{orderCode,jdbcType=VARCHAR}, #{refundCode,jdbcType=VARCHAR}, 
      #{presentOrderCode,jdbcType=VARCHAR}, #{uid,jdbcType=BIGINT}, #{encryptMobile,jdbcType=VARCHAR}, 
      #{encryptAccount,jdbcType=VARCHAR}, #{promotionCode,jdbcType=VARCHAR}, #{skuId,jdbcType=VARCHAR}, 
      #{actCode,jdbcType=VARCHAR}, #{activityCode,jdbcType=VARCHAR}, #{ruleCode,jdbcType=VARCHAR}, 
      #{conditionGroupId,jdbcType=BIGINT}, #{presentNo,jdbcType=VARCHAR}, #{actType,jdbcType=INTEGER}, 
      #{partnerNo,jdbcType=VARCHAR}, #{receiveType,jdbcType=INTEGER}, #{sendType,jdbcType=INTEGER}, 
      #{sendStatus,jdbcType=INTEGER}, #{receiveStatus,jdbcType=INTEGER}, #{refundStatus,jdbcType=INTEGER}, 
      #{callBackStatus,jdbcType=INTEGER}, #{partnerVipUsed,jdbcType=INTEGER}, #{settlementPrice,jdbcType=BIGINT}, 
      #{settlementPercent,jdbcType=INTEGER}, #{refundSettlementPrice,jdbcType=BIGINT}, 
      #{orderTime,jdbcType=TIMESTAMP}, #{sendTime,jdbcType=TIMESTAMP}, #{receiveDeadlineTime,jdbcType=TIMESTAMP}, 
      #{receiveTime,jdbcType=TIMESTAMP}, #{refundTime,jdbcType=TIMESTAMP}, #{sendRes,jdbcType=VARCHAR}, 
      #{refundRes,jdbcType=VARCHAR}, #{iqRefundTime,jdbcType=TIMESTAMP}, #{type,jdbcType=INTEGER}, 
      #{refundMsg,jdbcType=VARCHAR}, #{cardCode,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{amount,jdbcType=INTEGER}, 
      #{refundAmount,jdbcType=INTEGER}, #{tradeCode,jdbcType=VARCHAR}, #{couponCode,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.iqiyi.vip.po.RightsRecord">
    insert into rights_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderCode != null">
        order_code,
      </if>
      <if test="refundCode != null">
        refund_code,
      </if>
      <if test="presentOrderCode != null">
        present_order_code,
      </if>
      <if test="uid != null">
        uid,
      </if>
      <if test="encryptMobile != null">
        encrypt_mobile,
      </if>
      <if test="encryptAccount != null">
        encrypt_account,
      </if>
      <if test="promotionCode != null">
        promotion_code,
      </if>
      <if test="skuId != null">
        sku_id,
      </if>
      <if test="actCode != null">
        act_code,
      </if>
      <if test="activityCode != null">
        activity_code,
      </if>
      <if test="ruleCode != null">
        rule_code,
      </if>
      <if test="conditionGroupId != null">
        condition_group_id,
      </if>
      <if test="presentNo != null">
        present_no,
      </if>
      <if test="actType != null">
        act_type,
      </if>
      <if test="partnerNo != null">
        partner_no,
      </if>
      <if test="receiveType != null">
        receive_type,
      </if>
      <if test="sendType != null">
        send_type,
      </if>
      <if test="sendStatus != null">
        send_status,
      </if>
      <if test="receiveStatus != null">
        receive_status,
      </if>
      <if test="refundStatus != null">
        refund_status,
      </if>
      <if test="callBackStatus != null">
        call_back_status,
      </if>
      <if test="partnerVipUsed != null">
        partner_vip_used,
      </if>
      <if test="settlementPrice != null">
        settlement_price,
      </if>
      <if test="settlementPercent != null">
        settlement_percent,
      </if>
      <if test="refundSettlementPrice != null">
        refund_settlement_price,
      </if>
      <if test="orderTime != null">
        order_time,
      </if>
      <if test="sendTime != null">
        send_time,
      </if>
      <if test="receiveDeadlineTime != null">
        receive_deadline_time,
      </if>
      <if test="receiveTime != null">
        receive_time,
      </if>
      <if test="refundTime != null">
        refund_time,
      </if>
      <if test="sendRes != null">
        send_res,
      </if>
      <if test="refundRes != null">
        refund_res,
      </if>
      <if test="iqRefundTime != null">
        iq_refund_time,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="refundMsg != null">
        refund_msg,
      </if>
      <if test="cardCode != null">
        card_code,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="refundAmount != null">
        refund_amount,
      </if>
      <if test="tradeCode != null">
        trade_code,
      </if>
      <if test="couponCode != null">
        coupon_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orderCode != null">
        #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="refundCode != null">
        #{refundCode,jdbcType=VARCHAR},
      </if>
      <if test="presentOrderCode != null">
        #{presentOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="uid != null">
        #{uid,jdbcType=BIGINT},
      </if>
      <if test="encryptMobile != null">
        #{encryptMobile,jdbcType=VARCHAR},
      </if>
      <if test="encryptAccount != null">
        #{encryptAccount,jdbcType=VARCHAR},
      </if>
      <if test="promotionCode != null">
        #{promotionCode,jdbcType=VARCHAR},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=VARCHAR},
      </if>
      <if test="actCode != null">
        #{actCode,jdbcType=VARCHAR},
      </if>
      <if test="activityCode != null">
        #{activityCode,jdbcType=VARCHAR},
      </if>
      <if test="ruleCode != null">
        #{ruleCode,jdbcType=VARCHAR},
      </if>
      <if test="conditionGroupId != null">
        #{conditionGroupId,jdbcType=BIGINT},
      </if>
      <if test="presentNo != null">
        #{presentNo,jdbcType=VARCHAR},
      </if>
      <if test="actType != null">
        #{actType,jdbcType=INTEGER},
      </if>
      <if test="partnerNo != null">
        #{partnerNo,jdbcType=VARCHAR},
      </if>
      <if test="receiveType != null">
        #{receiveType,jdbcType=INTEGER},
      </if>
      <if test="sendType != null">
        #{sendType,jdbcType=INTEGER},
      </if>
      <if test="sendStatus != null">
        #{sendStatus,jdbcType=INTEGER},
      </if>
      <if test="receiveStatus != null">
        #{receiveStatus,jdbcType=INTEGER},
      </if>
      <if test="refundStatus != null">
        #{refundStatus,jdbcType=INTEGER},
      </if>
      <if test="callBackStatus != null">
        #{callBackStatus,jdbcType=INTEGER},
      </if>
      <if test="partnerVipUsed != null">
        #{partnerVipUsed,jdbcType=INTEGER},
      </if>
      <if test="settlementPrice != null">
        #{settlementPrice,jdbcType=BIGINT},
      </if>
      <if test="settlementPercent != null">
        #{settlementPercent,jdbcType=INTEGER},
      </if>
      <if test="refundSettlementPrice != null">
        #{refundSettlementPrice,jdbcType=BIGINT},
      </if>
      <if test="orderTime != null">
        #{orderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sendTime != null">
        #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="receiveDeadlineTime != null">
        #{receiveDeadlineTime,jdbcType=TIMESTAMP},
      </if>
      <if test="receiveTime != null">
        #{receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundTime != null">
        #{refundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sendRes != null">
        #{sendRes,jdbcType=VARCHAR},
      </if>
      <if test="refundRes != null">
        #{refundRes,jdbcType=VARCHAR},
      </if>
      <if test="iqRefundTime != null">
        #{iqRefundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="refundMsg != null">
        #{refundMsg,jdbcType=VARCHAR},
      </if>
      <if test="cardCode != null">
        #{cardCode,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=INTEGER},
      </if>
      <if test="refundAmount != null">
        #{refundAmount,jdbcType=INTEGER},
      </if>
      <if test="tradeCode != null">
        #{tradeCode,jdbcType=VARCHAR},
      </if>
      <if test="couponCode != null">
        #{couponCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.iqiyi.vip.po.RightsRecord">
    update rights_record
    <set>
      <if test="orderCode != null">
        order_code = #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="refundCode != null">
        refund_code = #{refundCode,jdbcType=VARCHAR},
      </if>
      <if test="presentOrderCode != null">
        present_order_code = #{presentOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="uid != null">
        uid = #{uid,jdbcType=BIGINT},
      </if>
      <if test="encryptMobile != null">
        encrypt_mobile = #{encryptMobile,jdbcType=VARCHAR},
      </if>
      <if test="encryptAccount != null">
        encrypt_account = #{encryptAccount,jdbcType=VARCHAR},
      </if>
      <if test="promotionCode != null">
        promotion_code = #{promotionCode,jdbcType=VARCHAR},
      </if>
      <if test="skuId != null">
        sku_id = #{skuId,jdbcType=VARCHAR},
      </if>
      <if test="actCode != null">
        act_code = #{actCode,jdbcType=VARCHAR},
      </if>
      <if test="activityCode != null">
        activity_code = #{activityCode,jdbcType=VARCHAR},
      </if>
      <if test="ruleCode != null">
        rule_code = #{ruleCode,jdbcType=VARCHAR},
      </if>
      <if test="conditionGroupId != null">
        condition_group_id = #{conditionGroupId,jdbcType=BIGINT},
      </if>
      <if test="presentNo != null">
        present_no = #{presentNo,jdbcType=VARCHAR},
      </if>
      <if test="actType != null">
        act_type = #{actType,jdbcType=INTEGER},
      </if>
      <if test="partnerNo != null">
        partner_no = #{partnerNo,jdbcType=VARCHAR},
      </if>
      <if test="receiveType != null">
        receive_type = #{receiveType,jdbcType=INTEGER},
      </if>
      <if test="sendType != null">
        send_type = #{sendType,jdbcType=INTEGER},
      </if>
      <if test="sendStatus != null">
        send_status = #{sendStatus,jdbcType=INTEGER},
      </if>
      <if test="receiveStatus != null">
        receive_status = #{receiveStatus,jdbcType=INTEGER},
      </if>
      <if test="refundStatus != null">
        refund_status = #{refundStatus,jdbcType=INTEGER},
      </if>
      <if test="callBackStatus != null">
        call_back_status = #{callBackStatus,jdbcType=INTEGER},
      </if>
      <if test="partnerVipUsed != null">
        partner_vip_used = #{partnerVipUsed,jdbcType=INTEGER},
      </if>
      <if test="settlementPrice != null">
        settlement_price = #{settlementPrice,jdbcType=BIGINT},
      </if>
      <if test="settlementPercent != null">
        settlement_percent = #{settlementPercent,jdbcType=INTEGER},
      </if>
      <if test="refundSettlementPrice != null">
        refund_settlement_price = #{refundSettlementPrice,jdbcType=BIGINT},
      </if>
      <if test="orderTime != null">
        order_time = #{orderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sendTime != null">
        send_time = #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="receiveDeadlineTime != null">
        receive_deadline_time = #{receiveDeadlineTime,jdbcType=TIMESTAMP},
      </if>
      <if test="receiveTime != null">
        receive_time = #{receiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundTime != null">
        refund_time = #{refundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sendRes != null">
        send_res = #{sendRes,jdbcType=VARCHAR},
      </if>
      <if test="refundRes != null">
        refund_res = #{refundRes,jdbcType=VARCHAR},
      </if>
      <if test="iqRefundTime != null">
        iq_refund_time = #{iqRefundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="refundMsg != null">
        refund_msg = #{refundMsg,jdbcType=VARCHAR},
      </if>
      <if test="cardCode != null">
        card_code = #{cardCode,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=INTEGER},
      </if>
      <if test="refundAmount != null">
        refund_amount = #{refundAmount,jdbcType=INTEGER},
      </if>
      <if test="tradeCode != null">
        trade_code = #{tradeCode,jdbcType=VARCHAR},
      </if>
      <if test="couponCode != null">
        coupon_code = #{couponCode,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.iqiyi.vip.po.RightsRecord">
    update rights_record
    set order_code = #{orderCode,jdbcType=VARCHAR},
      refund_code = #{refundCode,jdbcType=VARCHAR},
      present_order_code = #{presentOrderCode,jdbcType=VARCHAR},
      uid = #{uid,jdbcType=BIGINT},
      encrypt_mobile = #{encryptMobile,jdbcType=VARCHAR},
      encrypt_account = #{encryptAccount,jdbcType=VARCHAR},
      promotion_code = #{promotionCode,jdbcType=VARCHAR},
      sku_id = #{skuId,jdbcType=VARCHAR},
      act_code = #{actCode,jdbcType=VARCHAR},
      activity_code = #{activityCode,jdbcType=VARCHAR},
      rule_code = #{ruleCode,jdbcType=VARCHAR},
      condition_group_id = #{conditionGroupId,jdbcType=BIGINT},
      present_no = #{presentNo,jdbcType=VARCHAR},
      act_type = #{actType,jdbcType=INTEGER},
      partner_no = #{partnerNo,jdbcType=VARCHAR},
      receive_type = #{receiveType,jdbcType=INTEGER},
      send_type = #{sendType,jdbcType=INTEGER},
      send_status = #{sendStatus,jdbcType=INTEGER},
      receive_status = #{receiveStatus,jdbcType=INTEGER},
      refund_status = #{refundStatus,jdbcType=INTEGER},
      call_back_status = #{callBackStatus,jdbcType=INTEGER},
      partner_vip_used = #{partnerVipUsed,jdbcType=INTEGER},
      settlement_price = #{settlementPrice,jdbcType=BIGINT},
      settlement_percent = #{settlementPercent,jdbcType=INTEGER},
      refund_settlement_price = #{refundSettlementPrice,jdbcType=BIGINT},
      order_time = #{orderTime,jdbcType=TIMESTAMP},
      send_time = #{sendTime,jdbcType=TIMESTAMP},
      receive_deadline_time = #{receiveDeadlineTime,jdbcType=TIMESTAMP},
      receive_time = #{receiveTime,jdbcType=TIMESTAMP},
      refund_time = #{refundTime,jdbcType=TIMESTAMP},
      send_res = #{sendRes,jdbcType=VARCHAR},
      refund_res = #{refundRes,jdbcType=VARCHAR},
      iq_refund_time = #{iqRefundTime,jdbcType=TIMESTAMP},
      type = #{type,jdbcType=INTEGER},
      refund_msg = #{refundMsg,jdbcType=VARCHAR},
      card_code = #{cardCode,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      amount = #{amount,jdbcType=INTEGER},
      refund_amount = #{refundAmount,jdbcType=INTEGER},
      trade_code = #{tradeCode,jdbcType=VARCHAR},
      coupon_code = #{couponCode,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>