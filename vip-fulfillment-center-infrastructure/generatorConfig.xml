<!DOCTYPE generatorConfiguration
    PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
    "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
<!--导入属性配置 -->
<context id="default" targetRuntime="MyBatis3">

  <property name="javaFileEncoding" value="UTF-8"/>

  <commentGenerator>
    <property name="suppressDate" value="true"/>
    <property name="suppressAllComments" value="true"/>
  </commentGenerator>

<!--  <jdbcConnection-->
<!--      driverClass="com.mysql.jdbc.Driver"-->
<!--    connectionURL="**********************************************************************************************"-->
<!--    userId="vipqa_iqiyi"-->
<!--    password="vipqa@IQIYI"-->
<!--  />-->

    <jdbcConnection
        driverClass="com.mysql.jdbc.Driver"
        connectionURL="********************************************************************************************************"
        userId="vipqa_iqiyi"
        password="vipqa@IQIYI"
    />

  <!-- Model模型生成器,用来生成含有主键key的类，记录类 以及查询Example类
      targetPackage     指定生成的model生成所在的包名
      targetProject     指定在该项目下所在的路径  -->
  <javaModelGenerator
    targetPackage="com.iqiyi.vip.po"
    targetProject="src/main/java">
    <!-- 是否对model添加 构造函数 -->
    <property name="enableSubPackages" value="true"/>
    <!-- 给Model添加一个父类 -->
    <!--<property name="rootClass" value="com.itfsw.base"/>-->
  </javaModelGenerator>
  <!--Mapper映射文件生成所在的目录 为每一个数据库的表生成对应的SqlMap文件 -->
  <sqlMapGenerator targetPackage="mapper"
    targetProject="src/main/resources"/>
  <!-- 客户端代码，生成易于使用的针对Model对象和XML配置文件 的代码
      type="ANNOTATEDMAPPER",生成Java Model 和基于注解的Mapper对象
      type="MIXEDMAPPER",生成基于注解的Java Model 和相应的Mapper对象
      type="XMLMAPPER",生成SQLMap XML文件和独立的Mapper接口 -->
  <javaClientGenerator targetPackage="com.iqiyi.vip.mapper"
    targetProject="src/main/java"
    type="XMLMAPPER"/>


  <!-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ 要自动生成的表 +++++++++++++++++++++++++++++++++++++++++++++++++++++++ -->
<!--  <table schema="" tableName="rights_record" domainObjectName="RightsRecord" modelType="flat"-->
<!--    enableCountByExample="false" enableUpdateByExample="false"-->
<!--    enableDeleteByExample="false" enableSelectByExample="false"-->
<!--    selectByExampleQueryId="false">-->
<!--      <ignoreColumn column="mobile" />-->
<!--      <ignoreColumn column="account" />-->
<!--  </table>-->

    <table schema="" tableName="fulfillment_config" domainObjectName="FulfillmentConfig" modelType="flat"
        enableCountByExample="false" enableUpdateByExample="false"
        enableDeleteByExample="false" enableSelectByExample="false"
        selectByExampleQueryId="false">
        <ignoreColumn column="create_time" />
        <ignoreColumn column="update_time" />
    </table>
</context>
</generatorConfiguration>