package com.iqiyi.vip.job;

import com.iqiyi.vip.BaseTest;
import com.iqiyi.vip.domain.task.entity.AsyncTaskStat;
import com.iqiyi.vip.domain.task.repository.AsyncTaskRepository;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * AsyncTaskStatScheduler 测试类
 *
 */
@Slf4j
public class AsyncTaskStatSchedulerTest extends BaseTest {

    @Resource
    private AsyncTaskStatScheduler asyncTaskStatScheduler;

    @Resource
    private AsyncTaskRepository asyncTaskRepository;

    /**
     * 测试异步任务统计上报功能
     */
    @Test
    public void testReportAsyncTaskStats() {
        log.info("=== 开始测试异步任务统计上报功能 ===");

        try {
            // 直接调用真实的方法
            asyncTaskStatScheduler.reportAsyncTaskStats();
            log.info("=== 异步任务统计上报测试完成 ===");
        } catch (Exception e) {
            log.error("异步任务统计上报测试失败", e);
            throw e;
        }
    }

    /**
     * 测试查询异步任务统计数据
     */
    @Test
    public void testQueryAsyncTaskStats() {
        log.info("=== 开始测试查询异步任务统计数据 ===");

        try {
            List<AsyncTaskStat> statList = asyncTaskRepository.queryExeCountStatWithRightsInfo();
            log.info("查询到异步任务统计数据数量: {}", statList != null ? statList.size() : 0);

            if (statList != null && !statList.isEmpty()) {
                for (int i = 0; i < Math.min(statList.size(), 3); i++) {
                    AsyncTaskStat stat = statList.get(i);
                    log.info("统计数据[{}]: className={}, skuId={}, exeCount={}, num={}, uid={}, orderCode={}, uri={}, sendStatus={}, response={}",
                            i, stat.getClassName(), stat.getSkuId(), stat.getExeCount(), stat.getNum(),
                            stat.getUid(), stat.getOrderCode(), stat.getUri(), stat.getSendStatus(), stat.getResponse());
                }
            }

            log.info("=== 查询异步任务统计数据测试完成 ===");
        } catch (Exception e) {
            log.error("查询异步任务统计数据测试失败", e);
            throw e;
        }
    }

}
