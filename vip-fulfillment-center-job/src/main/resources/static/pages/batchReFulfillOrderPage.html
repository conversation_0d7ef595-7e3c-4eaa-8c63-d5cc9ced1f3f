<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>订单重试履约工具</title>
</head>
<body>
<form name="form1" id="form1">
    <p>文件：<input type="file" name="file" id="file"></p>
    <p><input type="button" name="b1" value="submit" onclick="fsubmit()"></p>
</form>
<div id="result"></div>


<script type="text/javascript">
    function fsubmit() {
        var form = document.getElementById("form1");
        var formData = new FormData(form);
        var oReq = new XMLHttpRequest();
        oReq.onreadystatechange = function () {
            if (oReq.readyState == 4) {
                if (oReq.status == 200) {
                    var json = JSON.parse(oReq.responseText);
                    alert(JSON.stringify(json))
                }
            }
        }
        oReq.open("POST", "/tool/uploadFulfillOrderFile");
        oReq.send(formData);
        return false;
    }
</script>

</body>
</html>