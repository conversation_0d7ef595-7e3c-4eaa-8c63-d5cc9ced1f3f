package com.iqiyi.vip.job;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.iqiyi.job.core.handler.annotation.Job;
import com.iqiyi.v.eagle.EagleMonitor;
import com.iqiyi.v.eagle.bo.EagleParam;
import com.iqiyi.vip.domain.gift.entity.Gift;
import com.iqiyi.vip.domain.gift.service.GiftService;
import com.iqiyi.vip.domain.outgateway.service.OutGatewayService;
import com.iqiyi.vip.domain.rights.entity.ReceiveRightRecord;
import com.iqiyi.vip.domain.rights.repository.ReceiveRecordRepository;
import com.iqiyi.vip.domain.sku.entity.Sku;
import com.iqiyi.vip.domain.sku.repository.SkuRepository;
import com.iqiyi.vip.domain.task.entity.AsyncTaskStat;
import com.iqiyi.vip.domain.task.repository.AsyncTaskRepository;
import com.iqiyi.vip.domain.urlconfig.entity.UrlConfig;
import com.iqiyi.vip.domain.urlconfig.repository.UrlConfigRepository;
import com.iqiyi.vip.dto.rights.RecordByOrderCodeSkuIdQry;
import com.iqiyi.vip.dto.sku.SkuBatchQuery;
import com.iqiyi.vip.enums.EagleParamNameEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 异步任务统计定时任务
 */
@Slf4j
@Component
public class AsyncTaskStatScheduler {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    @Value("${asyncTaskStat.monitor.tag.maxLength:20}")
    private Integer asyncTaskStatMonitorTagMaxLength;

    @Resource
    private AsyncTaskRepository asyncTaskRepository;

    @Resource
    private SkuRepository skuRepository;

    @Resource
    private GiftService giftService;

    @Resource
    private UrlConfigRepository urlConfigRepository;

    @Resource
    private OutGatewayService outGatewayService;

    @Resource
    private ReceiveRecordRepository receiveRecordRepository;

    /**
     * 定时上报异步任务执行统计
     *
     */
    @Job("reportAsyncTaskStats")
    public void reportAsyncTaskStats() {
        log.info("[AsyncTaskStatScheduler] Start reporting async task execution statistics");
        long startTime = System.currentTimeMillis();

        try {
            // 1. 获取基础统计数据（包含uid、orderCode）
            List<AsyncTaskStat> statList = asyncTaskRepository.queryExeCountStatWithRightsInfo();
            if (CollectionUtils.isEmpty(statList)) {
                log.info("[AsyncTaskStatScheduler] No stats found");
                return;
            }

            // 2. 批量查询sku信息
            Set<String> skuIds = extractSkuIds(statList);
            Map<String, Sku> skuMap = Collections.emptyMap();
            if (!skuIds.isEmpty()) {
                skuMap = skuRepository.batchQueryFromCache(SkuBatchQuery.builder()
                        .skuIds(String.join(",", skuIds))
                        .onlyQueryValid(false)
                        .build());
            }

            // 3. 补充sendStatus和response信息
            enrichReceiveRecordInfo(statList);

            // 4. 补充uri信息
            enrichUriInfo(statList, skuMap);

            // 5. 批量上报
            reportTaskExeStats(statList, skuMap);

            log.info("[AsyncTaskStatScheduler] Finished reporting {} stats, cost: {}ms",
                    statList.size(), System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("[AsyncTaskStatScheduler] Failed to report async task stats, cost: {}ms",
                    System.currentTimeMillis() - startTime, e);
        }
    }

    /**
     * 补充sendStatus和response信息
     * 参考 monitorService.orderOpenFailEagleMonitor 的实现方式
     * 对于聚合后的数据，取第一个不为空的sendStatus和response
     */
    private void enrichReceiveRecordInfo(List<AsyncTaskStat> statList) {
        if (CollectionUtils.isEmpty(statList)) {
            return;
        }

        for (AsyncTaskStat stat : statList) {
            try {
                // 增强空指针检查
                if (stat == null) {
                    log.warn("[enrichReceiveRecordInfo] stat is null, skip");
                    continue;
                }

                // 对于聚合后的数据，如果已经有response（从data中解析得到），则跳过
                if (StringUtils.isNotBlank(stat.getResponse())) {
                    continue;
                }

                if (stat.getUid() == null || StringUtils.isBlank(stat.getOrderCode())) {
                    continue;
                }

                // 构建查询参数
                RecordByOrderCodeSkuIdQry qry = RecordByOrderCodeSkuIdQry.builder()
                        .orderCode(stat.getOrderCode())
                        .uid(stat.getUid())
                        .skuId(stat.getSkuId())
                        .build();

                // 查询权益记录
                ReceiveRightRecord receiveRightRecord = receiveRecordRepository.queryByOrderCode(qry);
                if (receiveRightRecord != null) {
                    if (StringUtils.isBlank(stat.getResponse()) && StringUtils.isNotBlank(receiveRightRecord.getSendRes())) {
                        stat.setResponse(receiveRightRecord.getSendRes());
                    }
                }
            } catch (Exception e) {
                log.error("[enrichReceiveRecordInfo] Failed to enrich receive record info for uid:{}, orderCode:{}",
                        stat != null ? stat.getUid() : "null",
                        stat != null ? stat.getOrderCode() : "null", e);
            }
        }
    }

    /**
     * 补充uri信息
     * 通过skuId -> Gift -> sendNotifyUrlId -> UrlConfig -> syncUrl -> originalUrl 的链路获取uri
     */
    private void enrichUriInfo(List<AsyncTaskStat> statList, Map<String, Sku> skuMap) {
        if (CollectionUtils.isEmpty(statList)) {
            return;
        }

        try {
            for (AsyncTaskStat stat : statList) {
                try {
                    // 增强空指针检查
                    if (stat == null) {
                        log.warn("[enrichUriInfo] stat is null, skip");
                        continue;
                    }

                    // 如果已经有uri，则跳过（取第一个不为空的uri）
                    if (StringUtils.isNotBlank(stat.getUri())) {
                        continue;
                    }

                    // 只处理有skuId的记录
                    if (StringUtils.isBlank(stat.getSkuId())) {
                        continue;
                    }

                    // 获取SKU信息
                    if (skuMap == null) {
                        log.warn("[enrichUriInfo] skuMap is null, skip");
                        continue;
                    }

                    Sku sku = skuMap.get(stat.getSkuId());
                    if (sku == null) {
                        continue;
                    }

                    // 获取Gift信息
                    Gift gift = giftService.queryBySkuId(sku);
                    if (gift == null || gift.getSendNotifyUrlId() == null) {
                        continue;
                    }

                    // 获取UrlConfig信息
                    UrlConfig urlConfig = urlConfigRepository.queryByIdFromCache(gift.getSendNotifyUrlId());
                    if (urlConfig == null || StringUtils.isBlank(urlConfig.getSyncUrl())) {
                        continue;
                    }

                    // 获取原始URL（去除网关地址）
                    String originalUrl = outGatewayService.getOriginalUrlFromCache(urlConfig.getSyncUrl());
                    if (StringUtils.isNotBlank(originalUrl)) {
                        // 取第一个不为空的uri
                        stat.setUri(originalUrl);
                    }

                } catch (Exception e) {
                    log.warn("[enrichUriInfo] Failed to enrich uri info for stat skuId: {}, className: {}",
                            stat != null ? stat.getSkuId() : "null",
                            stat != null ? stat.getClassName() : "null", e);
                }
            }
        } catch (Exception e) {
            log.error("[enrichUriInfo] Failed to enrich uri info, statListSize: {}, skuMapSize: {}",
                    statList != null ? statList.size() : 0,
                    skuMap != null ? skuMap.size() : 0, e);
        }
    }

    /**
     * 从统计数据中提取所有skuId
     */
    private Set<String> extractSkuIds(List<AsyncTaskStat> statList) {
        if (statList == null || statList.isEmpty()) {
            return Collections.emptySet();
        }

        return statList.stream()
                .filter(Objects::nonNull) // 过滤null的stat
                .map(AsyncTaskStat::getSkuId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
    }

    /**
     * 批量上报任务执行统计数据
     *
     * @param statList 任务统计数据列表
     * @param skuMap SKU缓存Map(skuId -> Sku)
     */
    private void reportTaskExeStats(List<AsyncTaskStat> statList, Map<String, Sku> skuMap) {
        if (statList == null || statList.isEmpty()) {
            return;
        }

        try {
            for (AsyncTaskStat stat : statList) {
                try {
                    // 增强空指针检查
                    if (stat == null) {
                        log.warn("[EagleMonitor] stat is null, skip");
                        continue;
                    }

                    // 1. 处理className (取简单类名)
                    String className = stat.getClassName();
                    if (StringUtils.isNotBlank(className) && className.contains(".")) {
                        className = className.substring(className.lastIndexOf('.') + 1);
                    }
                    // 如果className为空，使用默认值
                    if (StringUtils.isBlank(className)) {
                        className = "UnknownTask ";
                    }

                    // 2. 获取skuName
                    String skuId = stat.getSkuId();
                    String skuDisplayName = "";
                    if (StringUtils.isNotBlank(skuId) && skuMap != null) {
                        Sku sku = skuMap.get(skuId);
                        if (sku != null && StringUtils.isNotBlank(sku.getDisplayName())) {
                            skuDisplayName = sku.getDisplayName();
                        }
                    }

                    // 3. 构建基础上报参数
                    Integer exeCount = stat.getExeCount();
                    if (exeCount == null) {
                        exeCount = 0;
                    }

                    // 按照指定格式构建标签：{{class}}-{{sku}}-{{failedInfo}}-{{exeCount}}

                    // 1. class 标签
                    String classTag = safeTruncateTagValue(className) + " ";

                    // 2. sku 标签：{{skuId}} [{{skuDisplayName}}]
                    String skuTag = "";
                    if (StringUtils.isNotBlank(skuId)) {
                        skuTag = " " + skuId;
                        if (StringUtils.isNotBlank(skuDisplayName)) {
                            skuTag += " [" + skuDisplayName + "] ";
                        }
                    }

                    // 3. failedInfo 标签：异常：{{resp}} [uri：{{uri}}]
                    String failedInfoTag = "";
                    String processedResponse = null;
                    if (StringUtils.isNotBlank(stat.getResponse())) {
                        processedResponse = processResponseField(stat.getResponse());
                        if (StringUtils.isNotBlank(processedResponse)) {
                            failedInfoTag = " 异常:" + processedResponse;  // processedResponse 已经处理过，无需二次处理
                        }
                    }
                    if (StringUtils.isNotBlank(stat.getUri())) {
                        String processedUri = processUriTagValue(stat.getUri());
                        if (StringUtils.isNotBlank(failedInfoTag)) {
                            failedInfoTag += " [uri:" + processedUri + "] ";
                        } else {
                            failedInfoTag = " [uri:" + processedUri + "] ";
                        }
                    }

                    // 4. exeCount 标签：已执行{{exeCn}}次
                    String exeCountTag = " 已执行 " + exeCount + " 次 ";

                    // 创建 EagleParam 并添加标签。前缀a_ b_ c_ d_是控制tag的前后顺序，进而影响告警通知的展示顺序
                    EagleParam eagleParam = new EagleParam(EagleParamNameEnum.ASYNC_TASK_STAT_MONITOR.getName())
                            .tag("a_class", classTag)
                            .tag("b_sku", skuTag)
                            .tag("c_failedInfo", failedInfoTag)
                            .tag("d_exeCount", exeCountTag);

                    // 6. 上报
                    Integer num = stat.getNum();
                    if (num == null) {
                        num = 0;
                    }

                    EagleMonitor.gaugeSetValue(eagleParam, num);

                    log.info("[EagleMonitor] Reported task stat: class={}, sku={}, failedInfo={}, exeCount={}, num={}, uid={}, orderCode={}",
                            classTag, skuTag, failedInfoTag, exeCountTag, num, stat.getUid(), stat.getOrderCode());
                } catch (Exception e) {
                    log.warn("[EagleMonitor] Failed to report task stat for skuId: {}, className: {}, exeCount: {}",
                            stat != null ? stat.getSkuId() : "null",
                            stat != null ? stat.getClassName() : "null",
                            stat != null ? stat.getExeCount() : "null", e);
                }
            }
        } catch (Exception ex) {
            log.error("[EagleMonitor] Failed to batch report task stats, statListSize: {}, skuMapSize: {}",
                    statList != null ? statList.size() : 0,
                    skuMap != null ? skuMap.size() : 0, ex);
        }
    }

    /**
     * 处理response字段，提取msg字段或截取前20个字符
     *
     * @param response 原始response字符串
     * @return 处理后的response字符串
     */
    private String processResponseField(String response) {
        try {
            // 尝试解析JSON并提取msg或message字段
            JsonNode jsonNode = OBJECT_MAPPER.readTree(response);
            if (jsonNode != null) {
                JsonNode msgNode = null;

                // 优先检查msg字段
                if (jsonNode.has("msg")) {
                    msgNode = jsonNode.get("msg");
                }
                // 如果没有msg字段，检查message字段
                else if (jsonNode.has("message")) {
                    msgNode = jsonNode.get("message");
                }

                if (msgNode != null && !msgNode.isNull()) {
                    String msg = msgNode.asText();
                    if (StringUtils.isNotBlank(msg)) {
                        // 检查是否包含敏感信息，如果包含则返回简化信息
                        String sanitizedMsg = sanitizeSensitiveInfo(msg);
                        // 删除空格，避免鹰眼分割问题，并限制长度
                        String cleanMsg = StringUtils.deleteWhitespace(sanitizedMsg);
                        if (cleanMsg.length() > asyncTaskStatMonitorTagMaxLength) {
                            return StringUtils.substring(cleanMsg, 0, asyncTaskStatMonitorTagMaxLength) + "...";
                        }
                        return cleanMsg;
                    }
                }
            }
        } catch (Exception e) {
            log.warn("[processResponseField] Failed to parse JSON, fallback to substring: {}", e.getMessage());
            return "解析返回的信息失败";
        }

        // 如果没有msg字段，则截取前N个字符(N可动态配置)
        if (StringUtils.isNotBlank(response)) {
            String sanitizedResponse = sanitizeSensitiveInfo(response);
            String cleanResponse = StringUtils.deleteWhitespace(sanitizedResponse);
            if (cleanResponse.length() > asyncTaskStatMonitorTagMaxLength) {
                return StringUtils.substring(cleanResponse, 0, asyncTaskStatMonitorTagMaxLength) + "...";
            }
            return cleanResponse;
        }

        return null;
    }

    /**
     * 安全截取标签值，避免 Prometheus 标签值过长导致数据丢失
     *
     * @param value 原始值
     * @return 截取后的值
     */
    private String safeTruncateTagValue(String value) {
        if (StringUtils.isBlank(value)) {
            return value;
        }

        // 删除空格和换行符，避免 Prometheus 解析问题
        String cleanValue = StringUtils.deleteWhitespace(value);

        // 限制长度，如果被截断则添加省略号
        if (cleanValue.length() > asyncTaskStatMonitorTagMaxLength) {
            return StringUtils.substring(cleanValue, 0, asyncTaskStatMonitorTagMaxLength) + "...";
        }
        return cleanValue;
    }

    /**
     * 处理 URI 标签值，去掉 http:// 或 https:// 前缀，并截取到 ? 之前（去掉查询参数）
     *
     * @param uri 原始 URI
     * @return 处理后的 URI
     */
    private String processUriTagValue(String uri) {
        if (StringUtils.isBlank(uri)) {
            return uri;
        }

        String processedUri = uri;

        // 去掉 http:// 前缀
        if (processedUri.startsWith("http://")) {
            processedUri = processedUri.substring(7);
        }
        // 去掉 https:// 前缀
        else if (processedUri.startsWith("https://")) {
            processedUri = processedUri.substring(8);
        }

        // 截取到 ? 之前，去掉查询参数
        int queryIndex = processedUri.indexOf('?');
        if (queryIndex != -1) {
            processedUri = processedUri.substring(0, queryIndex);
        }

        // 删除空格和换行符，但不按长度截断
        return StringUtils.deleteWhitespace(processedUri);
    }

    /**
     * 清理敏感信息，避免 metric 条数过多影响性能
     * 检测多个连续数字、字母或数字字母组合，包括用连字符分割的标识符
     *
     * @param text 原始文本
     * @return 清理后的文本
     */
    private String sanitizeSensitiveInfo(String text) {
        if (StringUtils.isBlank(text)) {
            return text;
        }

        // 统一的返回信息
        final String SANITIZED_MESSAGE = "下游返回的信息中包含随机字母或数字，无法上报详情";

        // 检查是否包含多个连续数字（6位以上，涵盖用户ID、订单号等）
        if (text.matches(".*\\d{6,}.*")) {
            return SANITIZED_MESSAGE;
        }

        // 检查是否包含多个连续字母（6位以上，涵盖各种编码）
        if (text.matches(".*[a-zA-Z]{6,}.*")) {
            return SANITIZED_MESSAGE;
        }

        // 检查是否包含用连字符分割的多个字母组合（如批次号：ABC-DEF-123）
        if (text.matches(".*[a-zA-Z]{2,}-[a-zA-Z0-9]{2,}.*") ||
            text.matches(".*[a-zA-Z0-9]{2,}-[a-zA-Z]{2,}.*") ||
            text.matches(".*[a-zA-Z]{2,}-[a-zA-Z]{2,}-[a-zA-Z0-9]{2,}.*")) {
            return SANITIZED_MESSAGE;
        }

        // 检查是否包含数字和字母的组合（6位以上，涵盖混合编码）
        if (text.matches(".*[a-zA-Z0-9]{6,}.*") &&
            text.matches(".*\\d.*") && text.matches(".*[a-zA-Z].*")) {
            return SANITIZED_MESSAGE;
        }

        return text;
    }
}