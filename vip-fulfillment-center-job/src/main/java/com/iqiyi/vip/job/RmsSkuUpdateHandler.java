package com.iqiyi.vip.job;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import com.iqiyi.job.core.context.JobHelper;
import com.iqiyi.job.core.handler.annotation.Job;
import com.iqiyi.vip.domain.rms.repository.RmsRepository;

/**
 * <AUTHOR>
 * @date 2023/10/16 10:40
 */
@Component
public class RmsSkuUpdateHandler {

    private static Logger logger = LoggerFactory.getLogger(RmsSkuUpdateHandler.class);

    @Resource
    private RmsRepository rmsRepository;

    @Job("rmsSkuUpdateHandler")
    public void rmsSkuUpdateHandler() throws Exception {
        JobHelper.log("rmsSkuUpdateHandler start");
        try {
            rmsRepository.syncAssetRmsSkuIds();
        } catch (Exception e) {
            logger.error("[Exception]", e);
            JobHelper.handleFail();
        }
        JobHelper.log("rmsSkuUpdateHandler end");
    }
}
