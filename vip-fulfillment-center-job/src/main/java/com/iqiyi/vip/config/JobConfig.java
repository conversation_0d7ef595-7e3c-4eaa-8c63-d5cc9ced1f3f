//package com.iqiyi.vip.config;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.web.client.RestTemplate;
//
//import com.iqiyi.job.core.executor.JobExecutor;
//import com.iqiyi.job.core.qae.register.QaeRegister;
//
///**
// * vip-job配置
// *
// * <AUTHOR>
// * @date 2022-06-20
// */
//@Configuration
//public class JobConfig {
//
//    @Value("${vip.job.admin.addresses}")
//    private String adminAddresses;
//    @Value("${vip.job.executor.appname}")
//    private String executorAppName;
//    @Value("${vip.job.executor.port}")
//    private int executorPort;
//    @Value("${vip.job.executor.logpath}")
//    private String executorLogPath;
//    @Value("${vip.job.accessToken}")
//    private String accessToken;
//    @Value("${vip.job.access.way}")
//    private String accessWay;
//    @Value("${vip.job.executor.switch}")
//    private String executorSwitch;
//    /**
//     * QAE API access key
//     */
//    @Value("${vip.job.qae.api.access.key}")
//    private String qaeApiAccessKey;
//    /**
//     * QAE开放API地址
//     */
//    @Value("${vip.job.qae.api.url}")
//    private String qaeApiUrl;
//    /**
//     * QAE应用名
//     */
//    @Value("${vip.job.qae.app.id}")
//    private String appId;
//    @Value("${vip.job.qae.switch}")
//    private String qaeSwitch;
//    @Autowired
//    private RestTemplate restTemplate;
//
//    /**
//     * 配置执行器
//     */
//    @Bean(initMethod = "start", destroyMethod = "destroy")
//    public JobExecutor jobExecutor() {
//        JobExecutor jobExe = new JobExecutor();
//        jobExe.setAdminAddresses(adminAddresses);
//        jobExe.setAppName(executorAppName);
//        jobExe.setPort(executorPort);
//        jobExe.setLogPath(executorLogPath);
//        jobExe.setAccessToken(accessToken);
//        jobExe.setAccessWay(accessWay);
//        jobExe.setExecutorSwitch(executorSwitch);
//        return jobExe;
//    }
//
//    /**
//     * 配置注册器，虚机接入不需要配置
//     */
//    @Bean(initMethod = "start", destroyMethod = "destroy")
//    public QaeRegister qaeRegister() {
//        QaeRegister qaeRegister = new QaeRegister();
//        qaeRegister.setAppId(appId);
//        qaeRegister.setQaeApiUrl(qaeApiUrl);
//        qaeRegister.setQaeApiAccessKey(qaeApiAccessKey);
//        qaeRegister.setQaeSwitch(qaeSwitch);
//        qaeRegister.setJobExecutor(jobExecutor());
//        qaeRegister.setQaeSwitch(qaeSwitch);
//        qaeRegister.setRestTemplate(restTemplate);
//        return qaeRegister;
//    }
//}
