package com.iqiyi.vip.controller;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.List;

import com.iqiyi.vip.aspect.WebLog;
import com.iqiyi.vip.domain.compensate.service.CompensateService;
import com.iqiyi.vip.domain.sku.repository.SkuRepository;
import com.iqiyi.vip.domain.tool.service.ToolService;
import com.iqiyi.vip.dto.base.BaseResponse;
import com.iqiyi.vip.dto.http.HttpResDTO;
import com.iqiyi.vip.dto.rights.BatchDeliverOrderReq;
import com.iqiyi.vip.dto.rights.BatchNotifyVmcReq;
import com.iqiyi.vip.dto.rights.BatchRefundOrderReq;
import com.iqiyi.vip.dto.sku.SkuByAttrQry;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.CompensateOptEnum;
import com.iqiyi.vip.utils.DateUtils;
import com.iqiyi.vip.utils.FileUtils;

/**
 * 工具接口
 *
 * <AUTHOR>
 * @date 2023/10/18 11:20
 */
@Slf4j
@Controller
@RequestMapping(value = "tool")
public class ToolController {

    @Resource
    private CompensateService compensateService;
    @Resource
    private ToolService toolService;
    @Resource
    private SkuRepository skuRepository;

    @WebLog
    @ApiOperation(value = "根据商品属性查询商品(分页)", httpMethod = "POST")
    @ResponseBody
    @RequestMapping(value = "/batchQuerySkuAttrPage")
    public HttpResDTO batchQuerySkuAttr(@RequestBody SkuByAttrQry req) {
        return skuRepository.queryByAttribute(req);
    }

    @WebLog
    @ApiOperation(value = "根据商品属性查询商品(不分页，查全量)", httpMethod = "POST")
    @ResponseBody
    @RequestMapping(value = "/batchQuerySkuAttrOneTime")
    public BaseResponse batchQuerySkuAttrOneTime(@RequestBody SkuByAttrQry req) {
        return BaseResponse.createSuccessResponse(toolService.batchQuerySkuByAttributeOneTime(req));
    }

    @WebLog
    @ApiOperation(value = "根据商品属性查询商品（分页），保存到履约配置表", httpMethod = "POST")
    @ResponseBody
    @RequestMapping(value = "/batchSaveFulfillConfigBySkuAttrPage")
    public BaseResponse batchSaveFulfillConfigBySkuAttr(@RequestBody SkuByAttrQry req) {
        toolService.batchSaveFulfillConfigBySkuAttr(req);
        return BaseResponse.createSuccessResponse();
    }

    @WebLog
    @ApiOperation(value = "根据商品属性查询商品(不分页，全量)，之后保存到履约配置表", httpMethod = "POST")
    @ResponseBody
    @RequestMapping(value = "/batchSaveFulfillConfigBySkuAttrOneTime")
    public BaseResponse batchSaveFulfillConfigBySkuAttrOneTime(@RequestBody SkuByAttrQry req) {
        toolService.batchSaveFulfillConfigBySkuAttrOneTime(req);
        return BaseResponse.createSuccessResponse();
    }

    @WebLog
    @ApiOperation(value = "根据skuId查商品信息，保存到履约配置表", httpMethod = "POST")
    @ResponseBody
    @RequestMapping(value = "/batchSaveFulfillmentConfig")
    public BaseResponse batchSaveFulfillmentConfig(@RequestParam String skuIds) {
        toolService.batchSaveFulfillConfig(skuIds);
        return BaseResponse.createSuccessResponse();
    }

    @RequestMapping(value = {"batchReFulfillOrderPage"})
    public String batchReFulfillOrderPage() {
        return "batchReFulfillOrderPage";
    }

    @RequestMapping(value = {"batchReRefundOrderPage"})
    public String batchReRefundOrderPage() {
        return "batchReRefundOrderPage";
    }

    @ResponseBody
    @PostMapping("/uploadFulfillOrderFile")
    public BaseResponse uploadFulfillOrderFile(@RequestParam("file") MultipartFile srcFile) {
        return batchCompensate(srcFile, CompensateOptEnum.RE_FULFILL_ORDER);
    }

    @ResponseBody
    @PostMapping("/uploadRefundOrderFile")
    public BaseResponse uploadRefundOrderFile(@RequestParam("file") MultipartFile srcFile) {
        return batchCompensate(srcFile, CompensateOptEnum.RE_REFUND_ORDER_RIGHTS);
    }

    private BaseResponse batchCompensate(MultipartFile srcFile, CompensateOptEnum compensateOptEnum) {
        if (srcFile.isEmpty()) {
            return new BaseResponse(CodeEnum.ERROR_PARAM.getCode(), "请选择一个文件");
        }
        try {
            String fileName = srcFile.getOriginalFilename();
            if (fileName.contains("\\") || fileName.contains("/")) {
                throw new RuntimeException("文件名称中不能包含\\和/字符");
            }
            String fileExt = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length());
            String url =
                "/upload" + File.separator + fileName.substring(0, fileName.lastIndexOf(".")) + "_" + DateUtils.getLocalLongDate14() + "." + fileExt;
            File destFile = new File(url);
            destFile.getParentFile().mkdirs();
            srcFile.transferTo(destFile);
            List<String> lineList = FileUtils.readFile(destFile);

            compensateService.batchFulOrder(fileName, lineList, compensateOptEnum);
            //将文件上传成功的信息写入message
            return BaseResponse.createSuccessResponse();
        } catch (IOException e) {
            log.info("[fileUpload][Exception]", e);
            return BaseResponse.create(CodeEnum.ERROR_SYSTEM);
        }
    }

    @WebLog
    @ApiOperation(value = "批量通知VMC-不会存储数据", httpMethod = "POST")
    @ResponseBody
    @RequestMapping(value = "/batchNotifyVmc")
    public BaseResponse batchNotifyVmc(@RequestBody BatchNotifyVmcReq req) {
        toolService.batchNotifyVmc(req);
        return BaseResponse.createSuccessResponse();
    }

    @WebLog
    @ApiOperation(value = "批量重新调用交易deliver接口", httpMethod = "POST")
    @ResponseBody
    @RequestMapping(value = "/batchDeliverOrder")
    public BaseResponse batchDeliverOrder(@RequestBody BatchDeliverOrderReq req) {
        toolService.batchDeliverOrder(req);
        return BaseResponse.createSuccessResponse();
    }

    @WebLog
    @ApiOperation(value = "批量重试解约", httpMethod = "POST")
    @ResponseBody
    @RequestMapping(value = "/batchRefundOrder")
    public BaseResponse refundOrder(@RequestBody BatchRefundOrderReq req) {
        return BaseResponse.createSuccessResponse(toolService.batchRefundOrder(req));
    }


}
