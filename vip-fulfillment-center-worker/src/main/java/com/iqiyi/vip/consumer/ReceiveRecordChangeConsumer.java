package com.iqiyi.vip.consumer;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.iqiyi.vip.aspect.WebLog;
import com.iqiyi.vip.domain.monitors.service.MonitorService;
import com.iqiyi.vip.domain.rights.entity.ReceiveRightRecord;
import com.iqiyi.vip.dto.base.MqMsgCanalEvent;
import com.iqiyi.vip.dto.rights.ReceiveRecordByOrderCodeQry;

/**
 * 领取记录变更监听mysqlIO消息
 *
 * <AUTHOR>
 * @date 2023/12/19 16:53
 */
@Slf4j
@Service
@RocketMQMessageListener(
    nameServer = "${rocketmq.receiveRecordChange.nameServer}",
    topic = "${rocketmq.receiveRecordChange.topic}",
    consumerGroup = "${rocketmq.receiveRecordChange.consumerGroup}",
    token = "${rocketmq.receiveRecordChange.token}")
public class ReceiveRecordChangeConsumer implements RocketMQListener<String> {

    @Resource
    private MonitorService monitorService;

    @WebLog
    @Override
    public void onMessage(String originalMsg) {
        long startTime = System.currentTimeMillis();
        try {
            MqMsgCanalEvent mqMsgCanalEvent = JSON.parseObject(originalMsg, MqMsgCanalEvent.class);
            log.info("[start][originalMsg:{}]", originalMsg);
            if (null == mqMsgCanalEvent.getRowAfter()) {
                log.info("[rowAfter null ignore][originalMsg:{}]", originalMsg);
                return;
            }
            ReceiveRightRecord receiveRightRecord = JSON.parseObject(JSON.toJSONString(mqMsgCanalEvent.getRowAfter()), ReceiveRightRecord.class);
            ReceiveRecordByOrderCodeQry qry = ReceiveRecordByOrderCodeQry.builder()
                .orderCode(receiveRightRecord.getOrderCode())
                .uid(receiveRightRecord.getUid())
                .promotionCode(receiveRightRecord.getPromotionCode())
                .build();
            monitorService.orderOpenFailEagleMonitor(qry);
        } catch (Exception e) {
            log.error("[dealMsg Exception][originalMsg:{}]", originalMsg, e);
            throw e;
        } finally {
            log.info("[end][originalMsg:{},cost:{}]", originalMsg, System.currentTimeMillis() - startTime);
        }
    }
}
