package com.iqiyi.vip.consumer;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;

import com.iqiyi.vip.aspect.WebLog;
import com.iqiyi.vip.domain.fulfillment.service.FulfillmentEventService;
import com.iqiyi.vip.domain.inventory.service.InventoryService;
import com.iqiyi.vip.domain.rights.factory.RightsFactory;
import com.iqiyi.vip.dto.order.OrderPaidMsg;
import com.iqiyi.vip.enums.DealModuleEnum;
import com.iqiyi.vip.enums.TradeOrderStatusEnum;

import static java.util.concurrent.TimeUnit.SECONDS;

/**
 * orderPaid消息
 *
 * <AUTHOR>
 * @date 2023/8/29 18:24
 */
@Slf4j
@Service
@RocketMQMessageListener(
    nameServer = "${rocketmq.orderPaid.nameServer}",
    topic = "${rocketmq.orderPaid.topic}",
    consumerGroup = "${rocketmq.orderPaid.consumerGroup}",
    token = "${rocketmq.orderPaid.token}")
public class OrderPaidConsumer implements RocketMQListener<String> {

    @Resource
    private FulfillmentEventService fulfillmentEventService;
    @Resource
    private InventoryService inventoryService;

    private static final ThreadPoolExecutor stockThreadPoolExecutor = new ThreadPoolExecutor(32, 128, 60, SECONDS, new LinkedBlockingQueue<Runnable>(100));

    @WebLog
    @Override
    public void onMessage(String originalMsg) {
        try {
            OrderPaidMsg orderPaidMsg = JSON.parseObject(originalMsg, OrderPaidMsg.class);
            log.info("[start][orderPaidMsg:{}]", orderPaidMsg);
            if (orderPaidMsg.needDealOrder()) {
                //正单履约
                if (TradeOrderStatusEnum.isPaidOrder(orderPaidMsg.getStatus())) {
                    CompletableFuture.runAsync(() -> inventoryService.deductStockFailRetry(orderPaidMsg), stockThreadPoolExecutor);
                    fulfillmentEventService.fulfillOrderFailRetry(RightsFactory.buildPaidOrderInfo(orderPaidMsg), DealModuleEnum.WORKER);
                }

                //退单解约
                if (TradeOrderStatusEnum.isRefundStatus(orderPaidMsg.getStatus())) {
                    fulfillmentEventService.refundOrderFailRetry(RightsFactory.buildRefundOrderInfo(orderPaidMsg));
                }
            } else {
                log.info("[ignore][orderCode:{}]", orderPaidMsg.getOrderCode());
            }
        } catch (Exception e) {
            log.error("[dealMsg Exception][originalMsg:{}]", originalMsg, e);
            throw e;
        }
    }

}
