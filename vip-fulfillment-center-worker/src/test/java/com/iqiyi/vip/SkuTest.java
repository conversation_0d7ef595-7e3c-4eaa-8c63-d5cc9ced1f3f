package com.iqiyi.vip;

import com.iqiyi.vip.domain.exact.service.ExactMqService;
import com.iqiyi.vip.domain.fulfillment.service.FulfillmentEventService;
import com.iqiyi.vip.domain.order.service.OrderService;
import com.iqiyi.vip.domain.rights.repository.ReceiveRecordRepository;
import com.iqiyi.vip.domain.rights.repository.RightsRepository;
import com.iqiyi.vip.domain.rms.repository.RmsRepository;
import com.iqiyi.vip.domain.rms.service.RmsService;
import com.iqiyi.vip.domain.sku.entity.Sku;
import com.iqiyi.vip.domain.sku.repository.SkuRepository;
import com.iqiyi.vip.dto.order.PaidOrderInfo;
import com.iqiyi.vip.dto.rights.ReceiveRightRecordQryCon;
import com.iqiyi.vip.dto.sku.SkuBatchQuery;
import com.iqiyi.vip.dto.sku.SkuQuery;
import com.iqiyi.vip.tidbmapper.TiDBReceiveRightRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/8/3 10:41
 */
@Slf4j
public class SkuTest extends BaseTest {


    @Resource
    private TiDBReceiveRightRecordMapper tiDBReceiveRightRecordMapper;
    @Resource
    private RightsRepository rightsRepository;
    @Resource
    private ExactMqService exactMqService;

    @Resource
    private SkuRepository SkuRepository;
    @Resource
    private OrderService orderService;

    @Resource
    private FulfillmentEventService fulfillmentEventService;

    @Resource
    private ReceiveRecordRepository receiveRecordRepository;
    @Resource
    private RmsRepository rmsRepository;
    @Resource
    private RmsService rmsService;

    @Test
    public void testQueryRms() {
//        String skuIds = rmsRepository.queryAssetRmsSkuIdList();
//        log.info("skuIds:{} ", skuIds);
        List<String> skuIdList = rmsService.queryAssetRMSSkuIdList();
        log.info("[skuIdList:{}]", skuIdList);
    }

    @Test
    public void testFulfillment() {
        PaidOrderInfo paidOrderInfo = new PaidOrderInfo();
        paidOrderInfo.setUid(11L);
        paidOrderInfo.setOrderCode("aa");
        paidOrderInfo.setStatus(1);
        paidOrderInfo.setSkuId("sku_371335450393257002");
        fulfillmentEventService.fulfillOrder(paidOrderInfo.getOrderCode());
    }


    @Test
    public void testQuerySku() {
        SkuQuery query = new SkuQuery();
        query.setSkuId("sku_372041649891776601");
        query.setOnlyQueryValid(false);
        System.out.println(SkuRepository.query(query));
    }

    @Test
    public void testBatchQueryFromCache() {
        SkuBatchQuery query = SkuBatchQuery.builder().skuIds("sku_588768691486738439,sku_590559064266981432").onlyQueryValid(false).build();
        Map<String, Sku> result = SkuRepository.batchQueryFromCache(query);
        System.out.println(result);
        Map<String, Sku> result2 = SkuRepository.batchQueryFromCache(query);
        System.out.println(result2);
    }


    @Test
    public void testQueryRecordCon() {
        ReceiveRightRecordQryCon con = new ReceiveRightRecordQryCon();
        con.setUid(1480402254L);
        queryRights(con);

        con.setOrderCode("20220719180002_202301091208");
        queryRights(con);

        con.setPromotionCode("************");
        queryRights(con);

        con.setReceiveType(1);
        queryRights(con);

        con.setReceiveStatus(1);
        queryRights(con);

        con.setRefundStatus(1);
        queryRights(con);

        con.setQueryValidReceiveDeadlineTime(1);
        con.setDate(new Date());
        queryRights(con);
    }

    private void queryRights(ReceiveRightRecordQryCon con) {
        List<com.iqiyi.vip.domain.rights.entity.ReceiveRightRecord> rights = receiveRecordRepository.queryByCon(con);
        log.info("[size:{},rights:{}]", rights.size(), rights);
    }
}
