package com.iqiyi.vip;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;

import com.iqiyi.vip.consumer.OrderPaidConsumer;

/**
 * <AUTHOR>
 * @date 2023/10/19 17:57
 */
@Slf4j
public class OrderTest extends BaseTest {

    @Resource
    private OrderPaidConsumer orderPaidConsumer;


    @Test
    public void testOrder() {
        String originalMsg = " {\n"
            + "    \"orderType\": \"SUB_PROD\",\n"
            + "    \"payTime\": \"2023-05-16 09:46:49.0\",\n"
            + "    \"fee\": \"0\",\n"
            + "    \"channel\": \"0\",\n"
            + "    \"chargeType\": \"2\",\n"
            + "    \"notNegativeOrder\": \"true\",\n"
            + "    \"renewalsFlag\": \"0\",\n"
            + "    \"phoneNum\": \"9381307b81079b39e0d0eb4965957c5f\",\n"
            + "    \"type\": \"1\",\n"
            + "    \"pushChannel\": \"0\",\n"
            + "    \"payType\": \"64\",\n"
            + "    \"tradePayment\": \"2023-05-16 09:46:49.0\",\n"
            + "    \"id\": \"********\",\n"
            + "    \"skuId\": \"sku_174553142765264967\",\n"
            + "    \"productId\": \"42790\",\n"
            + "    \"tradeNo\": \"202305160946380389160035630001\",\n"
            + "    \"count\": \"0\",\n"
            + "    \"settlementFee\": \"0\",\n"
            + "    \"tradeCode\": \"4200001827202305160688658466\",\n"
            + "    \"renewType\": \"2100801\",\n"
            + "    \"centerPayType\": \"WECHATAPPV3\",\n"
            + "    \"accountId\": \"oveuTji91w7fI6aJ7cU5H3Rwu0B0\",\n"
            + "    \"notGiftOrder\": \"true\",\n"
            + "    \"name\": \"FUN会员7天\",\n"
            + "    \"platformCode\": \"bb136ff4276771f3\",\n"
            + "    \"status\": \"10\",\n"
            + "    \"couponFee\": \"0\",\n"
            + "    \"rightTransferOrder\": \"false\",\n"
            + "    \"parentOrderCode\": \"20230516094638038916003563\",\n"
            + "    \"couponSettlementFee\": \"0\",\n"
            + "    \"platform\": \"10\",\n"
            + "    \"fv\": \"zh2767610fb39d41c6c1b25f5caac039\",\n"
            + "    \"centerCode\": \"2023051690000320088\",\n"
            + "    \"tradeCreate\": \"2023-05-16 09:46:39.0\",\n"
            + "    \"autoRenew\": \"0\",\n"
            + "    \"frVersion\": \"cellphoneModel=STK-AL00&dfp=15e510a5770e924548bd929bf28e554fa865245650f9c84b9cf1b2244ea9843dc2&d=4800722f167ca92b18e3639ff444dabe110f&k=b398b8ccbaeacca840073a7ee9b7e7e6&v=14.5.0&aid=&fr=&test=&qylct=970acfbbe4f994798c1189fafd260cf75d4f70d0f8b45bc8b12c4eb8b267060de1857f4dfd49e963c6d9841c67e586ce&qybdlct=75e53c084fb60d65df3fce0b50c999feb2282f2b45ae1df4ed19eba875d99157&qyctxv=1&coordType=2&FromCasher=1&login=1_1&isPasswordFreePay=0&mod=CN&isAliPay=1&sid=oqobd6d038yp91sk.0&bkt=send-redpacket;coupon-algo;coupon-rank-e&e=27ad7d5fe79740e8&gatewayAbtest=vipGroupC&loginResultType=0&integral=60&diy_tag=9a8ec817-1e29-4448-ad00-9795a000dcbe&domainOrigin=FromGwDomain\",\n"
            + "    \"returnUrl\": \"iqiyi-phone://com.qiyi.video/pay?\",\n"
            + "    \"serviceId\": \"11\",\n"
            + "    \"currencyUnit\": \"CNY\",\n"
            + "    \"giftOrder\": \"false\",\n"
            + "    \"productType\": \"1\",\n"
            + "    \"realFee\": \"0\",\n"
            + "    \"amount\": \"1\",\n"
            + "    \"notified\": \"true\",\n"
            + "    \"skuAmount\": \"1\",\n"
            + "    \"updateTime\": \"2023-05-16 09:46:49.0\",\n"
            + "    \"productFee\": \"1300\",\n"
            + "    \"userId\": \"2278836568\",\n"
            + "    \"productCode\": \"b4683d881839e4f7\",\n"
            + "    \"createTime\": \"2023-05-16 09:46:39.0\",\n"
            + "    \"refer\": \"{\\\"businessProperty\\\":{\\\"orderExt\\\":\\\"{\\\\\\\"rs\\\\\\\":2,\\\\\\\"showBlock\\\\\\\":0,\\\\\\\"userCrowd\\\\\\\":\\\\\\\"vip\\\\\\\",\\\\\\\"ruleUserCrowd\\\\\\\":\\\\\\\"1;expire_0_30,58;other,13;new,56;expire_0_3\\\\\\\",\\\\\\\"GW-Unity-Host\\\\\\\":\\\\\\\"api.iqiyi.com\\\\\\\"}\\\",\\\"combineActCode\\\":\\\"1000200220205157853535\\\"},\\\"phoneNum\\\":\\\"9381307b81079b39e0d0eb4965957c5f\\\",\\\"strategy\\\":\\\"VIP_PLUS\\\"}\",\n"
            + "    \"paid\": \"false\",\n"
            + "    \"orderCode\": \"202305160946380389160035630001\",\n"
            + "    \"parentRealFee\": \"1500\",\n"
            + "    \"gateway\": \"5225\"\n"
            + "}";

//        orderPaidConsumer.onMessage(originalMsg);
        orderPaidConsumer.onMessage(getPressureTestReferOrder());
    }

    private String getPressureTestReferOrder() {
        return "{\n"
            + "            \"payTime\": \"2023-06-15 13:37:05.0\",\n"
            + "            \"fee\": \"300\",\n"
            + "            \"channel\": \"0\",\n"
            + "            \"chargeType\": \"1\",\n"
            + "            \"notNegativeOrder\": \"true\",\n"
            + "            \"renewalsFlag\": \"0\",\n"
            + "            \"phoneNum\": \"53d9a5d3a4d03ce911b972d2cc9709ba\",\n"
            + "            \"type\": \"1\",\n"
            + "            \"pushChannel\": \"0\",\n"
            + "            \"payType\": \"98\",\n"
            + "            \"tradePayment\": \"2023-06-15 13:37:05.0\",\n"
            + "            \"id\": \"36720242\",\n"
            + "            \"skuId\": \"sku_273452722298276948\",\n"
            + "            \"productId\": \"1884342\",\n"
            + "            \"tradeNo\": \"20230615133704308982003758\",\n"
            + "            \"count\": \"0\",\n"
            + "            \"settlementFee\": \"300\",\n"
            + "            \"tradeCode\": \"140001768170095\",\n"
            + "            \"actCode\": \"iqiyi_partner_vip_iphone_video_1m_3yuan\",\n"
            + "            \"centerPayType\": \"APPLEIAP\",\n"
            + "            \"notGiftOrder\": \"true\",\n"
            + "            \"name\": \"喜马拉雅会员月卡\",\n"
            + "            \"platformCode\": \"bb35a104d95490f6\",\n"
            + "            \"status\": \"1\",\n"
            + "            \"couponFee\": \"0\",\n"
            + "            \"rightTransferOrder\": \"false\",\n"
            + "            \"couponSettlementFee\": \"0\",\n"
            + "            \"platform\": \"12\",\n"
            + "            \"fv\": \"aa5094b76579a36f\",\n"
            + "            \"centerCode\": \"2023061590002506210\",\n"
            + "            \"tradeCreate\": \"2023-06-15 13:37:05.0\",\n"
            + "            \"autoRenew\": \"0\",\n"
            + "            \"frVersion\": \"d=2d46578d395dd3a859ef13e0b6960bea101f&k=8e48946f144759d86a50075555fd5862&v=14.5.5&aid=&test=&login=&dfp=87c837db2a77a5462790c3380bcc6051c528000a894a14140b46f0147740c1795b&sid=&pointActCode=20230426113402000005p0024&pointSkuCode=200&gatewayAbtest=gatewayGroupA\",\n"
            + "            \"serviceId\": \"0\",\n"
            + "            \"currencyUnit\": \"CNY\",\n"
            + "            \"giftOrder\": \"false\",\n"
            + "            \"productType\": \"2\",\n"
            + "            \"realFee\": \"300\",\n"
            + "            \"amount\": \"1\",\n"
            + "            \"notified\": \"false\",\n"
            + "            \"skuAmount\": \"1\",\n"
            + "            \"updateTime\": \"2023-06-15 13:37:05.0\",\n"
            + "            \"productFee\": \"2500\",\n"
            + "            \"userId\": \"1684031570\",\n"
            + "            \"productCode\": \"a9cbd228cee0221b\",\n"
            + "            \"createTime\": \"2023-06-15 13:37:04.0\",\n"
            + "            \"refer\": \"{\\\"modeType\\\":\\\"pressureTest\\\",\\\"appId\\\":\\\"iqiyi_partner_vip_iphone_video_1m_3yuan\\\",\\\"bundleID\\\":\\\"com.qiyi.iphone\\\",\\\"businessProperty\\\":{\\\"discountTypeUse\\\":\\\"10\\\",\\\"sellScene\\\":\\\"VIP_BENEFIT\\\",\\\"tradeType\\\":\\\"pay\\\"},\\\"merchantNo\\\":\\\"aiqiyi\\\",\\\"phoneNum\\\":\\\"53d9a5d3a4d03ce911b972d2cc9709ba\\\",\\\"suitABTestId\\\":\\\"[{\\\\\\\"abTestGroup\\\\\\\":\\\\\\\"iosbanner2\\\\\\\",\\\\\\\"status\\\\\\\":1}]\\\",\\\"wechatBankCoupon\\\":{}}\",\n"
            + "            \"paid\": \"true\",\n"
            + "            \"orderCode\": \"20230615133704308982003758\",\n"
            + "            \"centerPayService\": \"445\",\n"
            + "            \"gateway\": \"1\"\n"
            + "        }";
    }
}
