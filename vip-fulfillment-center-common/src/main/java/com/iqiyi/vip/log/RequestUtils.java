package com.iqiyi.vip.log;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * 用于处理HTTP请求的工具类
 *
 * <AUTHOR>
 * @date 16-8-2
 */
@Slf4j
public class RequestUtils {

    /**
     * 获取COOKIE的值
     *
     * @param request HttpServletRequest
     * @param name Cookie的名称
     * @return CookieValue or null
     */
    public static String getCookieValue(HttpServletRequest request, String name) {
        Cookie[] cookies = request.getCookies();
        if (cookies == null) {
            return null;
        }
        for (<PERSON>ie ck : cookies) {
            if (StringUtils.equalsIgnoreCase(name, ck.getName())) {
                return ck.getValue();
            }
        }
        return null;
    }

    /**
     * 判断字符串是否是一个IP地址
     *
     * @param addr 字符串
     * @return true:IP地址，false：非IP地址
     */
    public static boolean isIPAddr(String addr) {
        if (StringUtils.isEmpty(addr)) {
            return false;
        }
        String[] ips = StringUtils.split(addr, '.');
        if (ips.length != 4) {
            return false;
        }
        try {
            int ipa = Integer.parseInt(ips[0]);
            int ipb = Integer.parseInt(ips[1]);
            int ipc = Integer.parseInt(ips[2]);
            int ipd = Integer.parseInt(ips[3]);
            return ipa >= 0 && ipa <= 255 && ipb >= 0 && ipb <= 255 && ipc >= 0
                && ipc <= 255 && ipd >= 0 && ipd <= 255;
        } catch (Exception e) {
        }
        return false;
    }

    /**
     * @return 获得本机IP内网地址
     */
    public static String getLocalIP() {
        // 本地IP，如果没有配置外网IP则返回它
        String localip = null;
        // 外网IP
        String netip = null;
        try {
            Enumeration<NetworkInterface> netInterfaces = NetworkInterface.getNetworkInterfaces();
            if (netInterfaces == null) {
                return StringUtils.EMPTY;
            }
            InetAddress ip;
            // 是否找到外网IP
            boolean finded = false;
            while (netInterfaces.hasMoreElements() && !finded) {
                NetworkInterface ni = netInterfaces.nextElement();
                if (ni.getName().equals("eth0")) {
                    Enumeration<InetAddress> address = ni.getInetAddresses();
                    while (address.hasMoreElements()) {
                        ip = address.nextElement();
                        if (!ip.isSiteLocalAddress() && !ip.isLoopbackAddress()
                            && ip.getHostAddress().indexOf(":") == -1) {
                            // 外网IP
                            netip = ip.getHostAddress();
                            finded = true;
                            break;
                        } else if (ip.isSiteLocalAddress()
                            && !ip.isLoopbackAddress()
                            && ip.getHostAddress().indexOf(":") == -1) {
                            // 内网IP
                            localip = ip.getHostAddress();
                        }
                    }
                }
            }
        } catch (SocketException e) {
            log.error("[module:localip] [action:RequestUtils] [step:getLocalIP] ", e);
        } catch (Exception e) {
            log.error("[module:localip] [action:RequestUtils] [step:getLocalIP] ", e);
        }
        if (netip != null && !"".equals(netip)) {
            return netip;
        } else {
            return localip;
        }
    }

    /**
     * 获得POST 过来参数设置到新的params中
     *
     * @param requestParams POST 过来参数Map
     * @return 新的Map
     */
    public static Map<String, String> genMapByRequestParas(Map requestParams) {

        Map<String, String> params = new HashMap<String, String>();
        for (Iterator iter = requestParams.keySet().iterator(); iter.hasNext(); ) {
            String name = (String) iter.next();
            String[] values = (String[]) requestParams.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i]
                    : valueStr + values[i] + ",";
            }
            params.put(name, valueStr);
        }

        return params;
    }

    /**
     * 获取客户端IP地址，支持proxy
     *
     * @param req HttpServletRequest
     * @return IP地址
     */
    public static String getRemoteAddr(HttpServletRequest req) {
        String xffIp = req.getHeader("X-Forwarded-For");
        if (StringUtils.isNotBlank(xffIp)) {
            String[] ips = StringUtils.split(xffIp, ',');
            for (String ip : ips) {
                if (StringUtils.isBlank(ip)) {
                    continue;
                }
                ip = ip.trim();
                if (isIPAddr(ip) && !ip.startsWith("10.") && !ip.startsWith("192.168.") && !"127.0.0.1".equals(ip)) {
                    return ip.trim();
                }
            }

        }
        String ip = req.getHeader("x-real-ip");
        if (isIPAddr(ip)) {
            return ip;
        }
        ip = req.getRemoteAddr();
        if (ip.indexOf('.') == -1) {
            ip = "127.0.0.1";
        }
        return ip;
    }
}