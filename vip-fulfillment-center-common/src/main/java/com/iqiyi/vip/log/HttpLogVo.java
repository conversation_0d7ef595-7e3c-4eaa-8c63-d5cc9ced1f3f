package com.iqiyi.vip.log;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;

import java.util.Date;
import java.util.Map;

/**
 * http请求日志,包含URL, <PERSON><PERSON>, <PERSON><PERSON>,response响应
 *
 * <AUTHOR>
 * @date 2016/8/4
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class HttpLogVo {

    /**
     * 请求时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date time;
    /**
     * 处理请求的服务器IP
     */
    private String localIp;
    /**
     * GET or POST .etc
     */
    private String method;
    /**
     * 请求的URL(不包含参数)
     */
    private String httpUrl;
    /**
     * http request的参数
     */
    private Map params;
    /**
     * http request的headers
     */
    private Map headers;
    /**
     * http request的cockies
     */
    private Map cookies;
    /**
     * http response报文
     **/
    private Object response;
    /**
     * http 响应时间
     */
    private long cost;

    /**
     * 将对象转换成JSON字符串
     */
    public String toJSONString() {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            //将jsonstring转换成map
            if (response != null && response instanceof String) {
                response = objectMapper.readValue(response.toString(), Map.class);
            }
        } catch (Throwable e) {
            //对异常不作处理,保持response不变化
        }
        try {
            return objectMapper.writeValueAsString(this);
        } catch (JsonProcessingException e) {
            return "";
        }
    }

    @Override
    public String toString() {
        return toJSONString();
    }
}
