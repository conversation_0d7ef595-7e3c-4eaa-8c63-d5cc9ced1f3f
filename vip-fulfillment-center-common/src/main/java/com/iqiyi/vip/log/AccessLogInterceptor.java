package com.iqiyi.vip.log;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 该拦截器主要是记录日志使用。 记录进入http request之前的参数、request执行前的时间戳。 action执行结果和结束时间戳由内部监听器完成。
 *
 * <AUTHOR>
 * @date 2016/8/4
 */
public class AccessLogInterceptor implements MethodInterceptor {

    private static final Logger LOG = LoggerFactory.getLogger(AccessLogInterceptor.class);
    private static final Logger HTTP_LOG = LoggerFactory.getLogger(System.getProperty("app.access.log", "APP_ACCESS_LOG"));

    /**
     * 创建HttpLog
     */
    private HttpLogVo buildHttpLogVo(HttpServletRequest request) {
        //获得参数
        Map params = request.getParameterMap();
        //转成map格式，不然打印结果有问题
        params = RequestUtils.genMapByRequestParas(params);
        //构建日志对象
        HttpLogVo userAuthLogVo = new HttpLogVo();
        //本机IP
        userAuthLogVo.setLocalIp(RequestUtils.getLocalIP());
        //HTTP METHOD
        userAuthLogVo.setHttpUrl(request.getRequestURL().toString());
        //获得请求URL
        userAuthLogVo.setMethod(request.getMethod());
        userAuthLogVo.setParams(params);
        //获得Header
        userAuthLogVo.setHeaders(buildHeaderMap(request));
        //获得Cookie
        userAuthLogVo.setCookies(buildCookieMap(request));
        userAuthLogVo.setTime(new Date());
        userAuthLogVo.setCost(System.currentTimeMillis());
        return userAuthLogVo;
    }

    /**
     * 构建cookies的map
     */
    private Map<String, String> buildCookieMap(HttpServletRequest request) {
        Map<String, String> cookies = new HashMap();
        cookies.put(Constants.COOKIE_AUTH_COOKIE_KEY, RequestUtils.getCookieValue(request, Constants.COOKIE_AUTH_COOKIE_KEY));
        cookies.put(Constants.COOKIE_QIYUE_CK_KEY, RequestUtils.getCookieValue(request, Constants.COOKIE_QIYUE_CK_KEY));
        cookies.put(Constants.COOKIE_QC005, RequestUtils.getCookieValue(request, Constants.COOKIE_QC005));
        return cookies;
    }

    /**
     * 构建header的map
     */
    private Map<String, Object> buildHeaderMap(HttpServletRequest request) {
        Map<String, Object> headers = new HashMap();
        headers.put(Constants.HEADER_TICKET, request.getHeader(Constants.HEADER_TICKET));
        headers.put(Constants.HEADER_REFERER, request.getHeader(Constants.HEADER_REFERER));
        headers.put(Constants.HEADER_USER_AGENT, request.getHeader(Constants.HEADER_USER_AGENT));
        headers.put(Constants.HEADER_X_FORWARDED_FOR, request.getHeader(Constants.HEADER_X_FORWARDED_FOR));
        return headers;
    }

    @Override
    public Object invoke(MethodInvocation methodInvocation) throws Throwable {
        Object response = null;
        HttpLogVo httpLogVo = null;
        try {
            //原则上不应该会产生异常,这里面作防御编程,在下面代码出现异常时，能正确返回result
            final HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();

            // 只打印一次日志, 以请求的实际路径为准
            if (!requestMatchThisMethod(request, methodInvocation)) {
                return methodInvocation.proceed();
            }

            httpLogVo = buildHttpLogVo(request);

            long beforExecuteTime = System.currentTimeMillis();
            //程序执行结果，定义成final，防止意外修改
            final Object result = methodInvocation.proceed();
            //处理响应时间cost
            long afterActionTime = System.currentTimeMillis();
            //将结果赋给外部
            response = result;
            httpLogVo.setCost(afterActionTime - beforExecuteTime);
            //获得返回内容
            httpLogVo.setResponse(dealResponse(result));
            //打印日志
            HTTP_LOG.info(httpLogVo.toJSONString());
            //返回结果
            return response;
        } catch (Throwable e) {
            LOG.warn("[print_http_log] error, log:{}", httpLogVo, e);
            if (response != null) {
                return response;
            }
            //说明的确出现异常
            throw e;
        }
    }

    private boolean requestMatchThisMethod(HttpServletRequest request, MethodInvocation methodInvocation) {
        String servletPath = request.getServletPath();
        String requestURI = request.getRequestURI();
        RequestMapping requestMapping = AnnotationUtils.findAnnotation(methodInvocation.getMethod().getDeclaringClass(), RequestMapping.class);
        if (requestMapping == null) {
            return false;
        }
        String[] typeLevelPatterns = requestMapping.value();
        for (String typeLevelPattern : typeLevelPatterns) {
            if (!typeLevelPattern.startsWith("/")) {
                typeLevelPattern = "/" + typeLevelPattern;
            }
            if (StringUtils.startsWith(servletPath, typeLevelPattern) || StringUtils.startsWith(requestURI, typeLevelPattern)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 截取返回结果
     */
    private Object dealResponse(Object result) {
        if (result != null) {
            //截取返回内容
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                String resultJSON = objectMapper.writeValueAsString(result);
                if (StringUtils.length(resultJSON) > Constants.APP_LOG_RESPONSE_LENGTH) {
                    return resultJSON.substring(0, Constants.APP_LOG_RESPONSE_LENGTH) + " 长度限截取";
                }
            } catch (JsonProcessingException e) {
                LOG.error("[print_http_log] error, result:{}", result, e);
            }

        }
        return result;
    }
}
