package com.iqiyi.vip.dto.act;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/8/3 15:10
 */
@Data
public class SubActExtraMsg {

    private String coBrandCode;

    private String subPromotionCode;

    private String subProductCode;

    private Integer subAmount;

    private Integer subPayAutoRenew;

    /**
     * 系统代扣订单是否参与赠送(0：否 1：是)
     */
    private Integer sysAutoRenewPresent;
    /**
     * 手动支付订单是否参与赠送（0：否 1：是）
     */
    private Integer manualAutoRenewPresent;

    /**
     * 价格条件操作符 详见OperateEnum
     */
    private String orderRealFeeOperate;

    /**
     * 价格条件操作值
     */
    private Long orderRealFeeValue;

    /**
     * 限定人群 详见UserBeforePaidSignEnum
     */
    private Integer userBeforePaidSign;

    /**
     * uid白名单
     */
    private String uidWhiteList;

    /**
     * 子商品skuId
     */
    private String subSkuId;

    /**
     * 子商品spu
     */
    private String subSpuId;

    /**
     * 子商品发放方式
     */
    private SubActSubSkuSendMsg subSkuSendInfo;
}
