package com.iqiyi.vip.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.YesOrNoEnum;

/**
 * <AUTHOR>
 * @date 2023/9/28 14:17
 */
@Data
public class CheckOrderCanTerminateDetailRes {

    @ApiModelProperty(value = "订单号", required = true)
    private String orderCode;
    @ApiModelProperty(value = "是否允许退（0:不可退，1:可退）", required = true)
    private Integer canRefund;

    @ApiModelProperty(value = "不能退的错误码", required = true)
    private String checkCode;
    @ApiModelProperty(value = "不能退的原因", required = true)
    private String checkMsg;

    private CodeEnum checkCodeMsgEnum;

    public String getCheckCode() {
        return StringUtils.isBlank(checkCode) ? checkCodeMsgEnum.getCode() : checkCode;
    }

    public String getCheckMsg() {
        return StringUtils.isBlank(checkMsg) ? checkCodeMsgEnum.getMessage() : checkMsg;
    }

    public Integer getCanRefund() {
        if (null != canRefund) {
            return canRefund;
        }
        return CodeEnum.SUCCESS.getCode().equals(checkCodeMsgEnum.getCode()) ? YesOrNoEnum.YES.getValue() : YesOrNoEnum.NO.getValue();
    }

    public CheckOrderCanTerminateDetailRes() {

    }

    public CheckOrderCanTerminateDetailRes(String orderCode, CodeEnum checkCodeMsgEnum) {
        this.orderCode = orderCode;
        this.checkCodeMsgEnum = checkCodeMsgEnum;
    }

    public CheckOrderCanTerminateDetailRes(String orderCode, CodeEnum checkCodeMsgEnum, Integer canRefund) {
        this.orderCode = orderCode;
        this.checkCodeMsgEnum = checkCodeMsgEnum;
        this.canRefund = canRefund;
    }
}
