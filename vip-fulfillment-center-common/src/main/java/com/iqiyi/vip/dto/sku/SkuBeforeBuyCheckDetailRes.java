package com.iqiyi.vip.dto.sku;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

import com.iqiyi.vip.dto.rights.AskPartnerCanBuyByUrlIdDetailRes;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.YesOrNoEnum;

/**
 * <AUTHOR>
 * @date 2023/11/29 11:49
 */
@Data
public class SkuBeforeBuyCheckDetailRes {

    @ApiModelProperty(value = "商品skuId", required = true)
    private String skuId;
    @ApiModelProperty(value = "商品数量", required = true)
    private Integer skuAmount;

    @ApiModelProperty(value = "不能购买的具体原因", required = true)
    private String checkCode;
    private String checkMsg;

    @ApiModelProperty(value = "是否允许购买（0:不可以购买，1:可以购买）", required = true)
    private Integer canBuy;

    private List<SkuBeforeBuyCheckDetailRes> subSkuCheckResList;

    public Integer getCanBuy() {
        return CodeEnum.SUCCESS.getCode().equals(checkCode) ? YesOrNoEnum.YES.getValue() : YesOrNoEnum.NO.getValue();
    }

    public SkuBeforeBuyCheckDetailRes() {

    }

    public SkuBeforeBuyCheckDetailRes(String skuId, Integer skuAmount, CodeEnum codeEnum) {
        this.skuId = skuId;
        this.skuAmount = skuAmount;
        this.checkCode = codeEnum.getCode();
        this.checkMsg = codeEnum.getMessage();
    }

    public SkuBeforeBuyCheckDetailRes(SkuCheckReq skuCheckReq, CodeEnum codeEnum) {
        this.skuId = skuCheckReq.getSkuId();
        this.skuAmount = skuCheckReq.getSkuAmount();
        this.checkCode = codeEnum.getCode();
        this.checkMsg = codeEnum.getMessage();
    }

    public SkuBeforeBuyCheckDetailRes(String skuId, Integer skuAmount,AskPartnerCanBuyByUrlIdDetailRes askPartnerResult) {
        this.skuId = skuId;
        this.skuAmount = skuAmount;
        this.checkCode = askPartnerResult.getCheckCode();
        this.checkMsg = askPartnerResult.getCheckMsg();
        this.canBuy = askPartnerResult.getCanBuy();
    }
}
