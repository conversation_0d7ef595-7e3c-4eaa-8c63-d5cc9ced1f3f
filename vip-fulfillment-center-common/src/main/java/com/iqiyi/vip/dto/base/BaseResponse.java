package com.iqiyi.vip.dto.base;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.RetryEnum;

/**
 * 通用返回结构
 *
 * <AUTHOR>
 * @date 2021-05-25
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BaseResponse<T> implements Serializable {

    private static final long serialVersionUID = 1;
    @ApiModelProperty(value = "返回码", example = "A00000")
    private String code;
    @ApiModelProperty(value = "返回描述", example = "成功")
    private String msg;
    @ApiModelProperty(value = "返回DATA值")
    private T data;
    private Integer retry;

    public BaseResponse() {
    }

    public BaseResponse(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public BaseResponse(CodeEnum codeEnum) {
        this.code = codeEnum.getCode();
        this.msg = codeEnum.getMessage();
    }

    public BaseResponse(CodeEnum codeEnum, RetryEnum retry) {
        this.code = codeEnum.getCode();
        this.msg = codeEnum.getMessage();
        this.retry = retry.getStatus();
    }

    public BaseResponse(CodeEnum codeEnum, T data) {
        this.code = codeEnum.getCode();
        this.msg = codeEnum.getMessage();
        this.data = data;
    }

    /**
     * 是否成功
     */
    public boolean suc() {
        return CodeEnum.SUCCESS.getCode().equals(this.code);
    }

    public static BaseResponse create(CodeEnum codeEnum) {
        return new BaseResponse(codeEnum);
    }

    public static BaseResponse create(CodeEnum codeEnum, RetryEnum retry) {
        return new BaseResponse(codeEnum, retry);
    }

    public static BaseResponse createSuccessResponse() {
        return new BaseResponse(CodeEnum.SUCCESS);
    }

    public static BaseResponse createSuccessResponse(Object data) {
        BaseResponse response = new BaseResponse(CodeEnum.SUCCESS);
        response.setData(data);
        return response;
    }
}
