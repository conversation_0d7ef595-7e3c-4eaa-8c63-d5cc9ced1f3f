package com.iqiyi.vip.dto.rights;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.iqiyi.vip.enums.SendStatusEnum;

/**
 * <AUTHOR>
 * @date 2023/9/11 16:49
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PartnerRightsResponse {

    private Integer sendStatus;
    private String sendRes;
    /**
     * 合作方返回的券码
     */
    private String couponCode;
    private String account;
    private String mobile;
    private Integer amount;
    private Long sendTime;
    private RightRecordRemarkDTO rightRecordRemark;

    /**
     * 是否需要取消订单
     *
     * @return
     */
    public boolean needCancel() {
        return sendStatus != null && SendStatusEnum.NEED_CANCEL.getStatus() == sendStatus;
    }
}
