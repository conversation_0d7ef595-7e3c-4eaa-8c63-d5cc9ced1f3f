package com.iqiyi.vip.dto.base;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.RetryEnum;

/**
 * <AUTHOR>
 * @date 2023/10/12 19:10
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BaseListResponse<T> implements Serializable {

    private static final long serialVersionUID = 1;
    @ApiModelProperty(value = "返回码", example = "A00000")
    private String code;
    @ApiModelProperty(value = "返回描述", example = "成功")
    private String msg;
    @ApiModelProperty(value = "返回DATA值")
    private List<T> dataList;
    private Integer retry;

    public BaseListResponse() {
    }

    public BaseListResponse(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public BaseListResponse(CodeEnum codeEnum) {
        this.code = codeEnum.getCode();
        this.msg = codeEnum.getMessage();
    }

    public BaseListResponse(CodeEnum codeEnum, RetryEnum retry) {
        this.code = codeEnum.getCode();
        this.msg = codeEnum.getMessage();
        this.retry = retry.getStatus();
    }

    public BaseListResponse(CodeEnum codeEnum, List<T> dataList) {
        this.code = codeEnum.getCode();
        this.msg = codeEnum.getMessage();
        this.dataList = dataList;
    }

}
