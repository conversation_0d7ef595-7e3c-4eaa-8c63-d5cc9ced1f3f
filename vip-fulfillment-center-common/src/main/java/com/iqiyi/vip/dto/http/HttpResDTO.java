package com.iqiyi.vip.dto.http;

import lombok.Data;

import java.util.List;

import com.iqiyi.vip.dto.base.PageInfo;
import com.iqiyi.vip.enums.CodeEnum;

/**
 * httClient响应结果
 *
 * <AUTHOR> on 2018/7/29
 */
@Data
public class HttpResDTO<T> {

    private String message;
    private String code;
    private String msg;
    private T data;
    private Boolean success;
    private List<T> dataList;
    private Integer retry;
    private String messageId;
    private PageInfo pageInfo;

    public HttpResDTO() {

    }

    public HttpResDTO(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public static HttpResDTO create(CodeEnum codeEnum) {
        return new HttpResDTO(codeEnum.getCode(), codeEnum.getMessage());
    }

    public static HttpResDTO createSuc(String message) {
        return new HttpResDTO(CodeEnum.SUCCESS.getCode(), message);
    }

    public boolean suc() {
        return CodeEnum.SUCCESS.getCode().equals(this.getCode());
    }
}

