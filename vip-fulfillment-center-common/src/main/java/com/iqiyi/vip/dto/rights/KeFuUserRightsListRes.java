package com.iqiyi.vip.dto.rights;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 客服系统-查询用户记录返回结果
 *
 * <AUTHOR>
 * @date 2023/10/12 17:43
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KeFuUserRightsListRes {

    private Long uid;
    private String orderCode;
    /**
     * 商品名称
     */
    private String skuId;
    private String skuName;
    /**
     * 是否赠送权益
     */
    private String sendRight;
    /**
     * 领取状态
     */
    private String receiveStatus;
    /**
     * 领取手机号/第三方账号
     */
    private String account;
    /**
     * 领取时间
     */
    private Date receiveTime;
    /**
     * 领取信息
     */
    private String receiveMsg;
    /**
     * 领取类型
     */
    private String receiveType;
    /**
     * 领取权益截止时间
     */
    private Date receiveDeadlineTime;
    /**
     * 退权益状态
     */
    private String refundStatus;
    /**
     * 退权益时间
     */
    private Date refundTime;
    /**
     * 退单信息
     */
    private String refundInfo;

}
