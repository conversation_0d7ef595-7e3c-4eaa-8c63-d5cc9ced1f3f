package com.iqiyi.vip.dto.rights;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * rights_record表的remark字段
 *
 * <AUTHOR>
 * @date 2024/10/29 17:50
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RightRecordRemarkDTO {

    private Long contentId;
    /**
     * 权益开始时间。格式：“YYYY-MM-DD HH:mm:ss”
     */
    private String startTime;
    /**
     * 权益结束时间。格式：“YYYY-MM-DD HH:mm:ss”(买赠是同一个产品时，时长累加)
     */
    private String endTime;

    /**
     * 1续费，0首次开通
     */
    private Integer renewFlag;

    /**
     * 开通前用户会员deadline，从来没开过为空 (仅开通会员有这个字段)
     */
    private String beforeDeadline;

    /**
     * 1:付费，0:免费(仅开通会员有这个字段)
     */
    private Integer beforePaidSign;

}
