package com.iqiyi.vip.dto.rights;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 批量回调交易deliver接口
 *
 * <AUTHOR>
 * @date 2024/10/30 10:03
 */
@Data
public class BatchDeliverOrderReq {

    @ApiModelProperty(value = "订单号，多个用,分隔", required = true)
    private String orderCodes;

    @ApiModelProperty(value = "orderCodes与该字段数据进行整合，传这个就不用根据orderCodes调用交易接口获取uid了", required = true)
    private List<RecordByOrderCodeSkuIdQry> qryList;
}
