package com.iqiyi.vip.dto.base;

import lombok.Data;

import java.util.Map;

/**
 * mysqlIo mq消息体 消息类型：CanalEvent https://iq.feishu.cn/wiki/MGxwwem1xiHXc0kezGlcRLM2nKv
 *
 * <AUTHOR>
 * @date 2023/12/19 17:22
 */
@Data
public class MqMsgCanalEvent {

    /**
     * 数据库名
     */
    private String schemaName;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 数据库操作类型 MySqlIoEventTypeEnum
     */
    private String eventType;

    /**
     * 该条记录在MySQL中变更的时间，单位为ms，但只精确到 秒
     */
    private Long timestamp;

    /**
     * 该操作发生前，该行记录所有列的值。对于DELTET操作，rowBefore即指被删除的记录内容
     */
    private Map<String, Object> rowBefore;

    /**
     * 该操作发生后，该行记录所有列的值。对于INSERT操作，rowAfter即指新插入的记录内容。
     */
    private Map<String, Object> rowAfter;

    //对于INSERT操作，rowBefore为空。对于DELETE操作，rowAfter为空。对于UPDATE，二者皆不为空
}
