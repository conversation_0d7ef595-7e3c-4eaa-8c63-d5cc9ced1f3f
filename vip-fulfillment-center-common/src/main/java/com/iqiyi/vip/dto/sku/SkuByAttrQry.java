package com.iqiyi.vip.dto.sku;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/1 11:42
 */
@Data
public class SkuByAttrQry {

    private Integer pageNo = 1;
    private Integer pageSize = 20;

    private CommonAttrQueryParam skuId;

    private CommonAttrQueryParam spuId;

    private CommonAttrQueryParam skuName;

    private CommonAttrQueryParam status;

    private List<SkuAttrQueryParam> queryParamList;

    @Data
    @NoArgsConstructor
    public static class CommonAttrQueryParam {

        /**
         * 参数值
         */
        private String value;

        /**
         * 是否为精确匹配 默认精确匹配
         */
        private boolean precise = true;

        public CommonAttrQueryParam(String value, boolean precise) {
            this.value = value;
            this.precise = precise;
        }
    }

}
