package com.iqiyi.vip.dto.order;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.Date;

import com.iqiyi.vip.enums.DealModuleEnum;
import com.iqiyi.vip.utils.PressureTestUtils;

/**
 * <AUTHOR>
 * @date 2023/9/11 16:57
 */
@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaidOrderInfo {

    private Long uid;
    private String skuId;
    private Integer skuAmount;
    private String orderCode;
    private Date payTime;
    private Integer status;

    private Long realFee;
    private String parentOrderCode;
    private String tradeNo;
    private String refer;

    /**
     * 透传订单字段
     */
    private String tradeCode;
    private String platformCode;
    private String frVersion;
    private String fv;
    private Long gateway;
    private Integer chargeType;
    private String partner;
    private Integer amount;
    private String businessValues;
    private Long contentId;
    private String productCode;
    /**
     * 当前的重试次数
     */
    private Integer currentRetryCount;

    private String accountId;

    private Integer payType;
    /**
     * 过滤需要处理的订单
     */
    public boolean needDealOrder(DealModuleEnum dealModule, Boolean skipVipPressureOrder) {
        if (null == payTime || null == status) {
            log.error("[orderPaid payTime null ignore][orderCode:{}]", orderCode);
            return false;
        }
        if (StringUtils.isBlank(skuId)) {
            log.info("[skuId null ignore][orderCode:{}]", orderCode);
            return false;
        }
        boolean isPressureOrder = PressureTestUtils.isPressureOrder(refer);
        //是压测单
        if (isPressureOrder) {
            if (!DealModuleEnum.VIP_WORKER.equals(dealModule)) {
                //非vip-worker模块，则无需处理该订单 (京东、喜马拉雅)
                log.info("[non-vip worker skip pressureTest order][orderCode:{}]", orderCode);
                return false;
            } else if (skipVipPressureOrder) {
                //配置中心配了跳过压测单，则无需处理该订单
                log.info("[vip worker skip pressureTest order][orderCode:{}]", orderCode);
                return false;
            }
        }
        return true;
    }


}
