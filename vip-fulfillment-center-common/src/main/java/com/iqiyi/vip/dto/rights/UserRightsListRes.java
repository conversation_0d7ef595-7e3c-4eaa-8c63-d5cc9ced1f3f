package com.iqiyi.vip.dto.rights;

import lombok.Data;

import com.iqiyi.vip.enums.ReceiveChanceInvalidTypeEnum;
import com.iqiyi.vip.enums.ReceiveChanceStatusEnum;

/**
 * 我的资产-数据详情
 * <AUTHOR>
 * @date 2023/10/12 18:34
 */
@Data
public class UserRightsListRes {

    /** 领取Code 2022.12.28 废弃，改为使用skuId*/
    @Deprecated
    private String receiveCode;

    /** 订单号 */
    private String orderCode;

    /** 订单生成时间 */
    private Long createTime;

    /** 资格状态 */
    private ReceiveChanceStatusEnum status;

    /** 失效类型 */
    private ReceiveChanceInvalidTypeEnum invalidType;

    /** 退权益状态 */
    private Integer refundStatus;

    /** 退权益时间 */
    private Long refundTime;

    /** 领取状态 */
    private Integer receiveStatus;

    /** 领取截止时间 */
    private Long receiveDeadline;

    /** 领取账号 */
    private String receiveAccount;

    /** 领取时间 */
    private Long receiveTime;


    /** 发送方 */
    private String presentNo;

    /**商品skuId (2022.12.28 新增字段) */
    private String skuId;
}
