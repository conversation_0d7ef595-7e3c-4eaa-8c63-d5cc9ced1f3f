package com.iqiyi.vip.dto.sku;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/29 11:37
 */
@Data
public class SkuBeforeBuyCheckReq {

    @ApiModelProperty(value = "用户uid", required = true)
    private Long uid;

    @ApiModelProperty(value = "qyid")
    private String qyid;

    @ApiModelProperty(value = "商品信息", required = true)
    private List<SkuCheckReq> skuCheckList;

    @ApiModelProperty(value = "校验用户是否绑定了手机号（0:不校验，1:校验）\n"
        + "\n(不传默认会校验)")
    private Integer checkBindPhone;

    @ApiModelProperty(value = "调用方")
    private String caller;
}
