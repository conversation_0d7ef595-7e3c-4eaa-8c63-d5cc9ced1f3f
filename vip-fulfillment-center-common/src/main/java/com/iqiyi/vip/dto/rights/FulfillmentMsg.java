/**
 * 
 */
package com.iqiyi.vip.dto.rights;

import lombok.Builder;
import lombok.Data;

/**
 * 履约消息
 */
@Data
@Builder
public class FulfillmentMsg {
	/** 履约订单号 */
	private String orderCode;
	
	/** 履约类型 */
	private String type;
	
	/** 用户UID */
	private Long uid;
	
	/** 商品ID */
	private String skuId;
	
	/** 履约状态 */
	private Integer status;
	
	/** 合作方接口返回 */
	private String res;
	
	/** 开通权益账号 */
	private String account;
	
	/** 券码类权益特有字段，记录发放的券码编号 */
	private String couponCode;
	
	/** 订单时间 */
	private Long orderTime;
	
	/** 权益发送时间 */
	private Long sendTime;
	
	/** 权益回收时间 */
	private Long refundTime;
	
	/** 订单透传字段 - tradeNo */
    private String tradeNo;
	
	/** 订单透传字段 - tradeCode */
    private String tradeCode;
	
	/** 订单透传字段 - platformCode */
    private String platformCode;
	
	/** 订单透传字段 - sellScene */
    private String sellScene;
}
