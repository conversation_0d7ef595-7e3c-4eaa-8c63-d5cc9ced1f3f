package com.iqiyi.vip.dto.rights;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/25 15:27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReceiveRightRecordQryCon {

    private Long uid;
    private String promotionCode;
    private String orderCode;
    private Integer receiveType;
    private Integer receiveStatus;
    private Integer refundStatus;
    private Integer queryValidReceiveDeadlineTime;
    private Date date;
    private List<String> orderCodeList;
    private String skuId;
}
