package com.iqiyi.vip.dto.order;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 加价购活动码和地址码结果
 *
 * <AUTHOR>
 * @date 2025/08/27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CombineActCodeResult {

    /**
     * 加价购活动code
     */
    private String combineActCode;

    /**
     * 实物商品的收获地址
     */
    private String addressCode;
}
