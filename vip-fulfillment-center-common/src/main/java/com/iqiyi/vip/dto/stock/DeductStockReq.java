package com.iqiyi.vip.dto.stock;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 减库存
 *
 * <AUTHOR>
 * @date 2023/11/24 14:42
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeductStockReq {

    private String orderId;

    private String skuId;

    /**
     * 库存变化量
     */
    private Integer inventoryNum;

    private Long orderCreateTime;

    private String actCode;
}
