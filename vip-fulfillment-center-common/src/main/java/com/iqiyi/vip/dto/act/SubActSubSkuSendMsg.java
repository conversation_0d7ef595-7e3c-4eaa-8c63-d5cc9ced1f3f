package com.iqiyi.vip.dto.act;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/8/3 15:11
 */
@Data
public class SubActSubSkuSendMsg {

    /**
     * 权益发放类型 参考 SendTypeEnum
     */
    private Integer constraintSendType;

    private Integer receiveDeadlineRelativeDays;

    private Long receiveDeadlineAbsolute;

    /**
     * 退单是否回收商品权益 0:不回收  1:回收
     */
    private Integer refundPartnerType;

    /**
     * 处理退单最晚截止时间
     */
    private Long refundPartnerDeadline;
}
