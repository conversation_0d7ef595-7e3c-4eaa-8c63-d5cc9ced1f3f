package com.iqiyi.vip.dto.rights;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

import com.iqiyi.vip.enums.CodeEnum;

/**
 * 查询打包购的子sku是否有可领权益
 *
 * <AUTHOR>
 * @date 2022/5/30 17:15
 */
@Getter
@Setter
public class CheckSubSkuCanReceiveResVO {

    private String skuId;
    private CodeEnum checkCodeMsgEnum;
    private String checkCode;
    private String checkMsg;

    @ApiModelProperty(value = "是否支持切换账号领取(0:不支持，1:支持)constraintSendType=1或2时必填")
    private Integer changeAccount;

    //SkuAccountTypeEnum
    private Integer accountType;

    public String getCheckCode() {
        return checkCodeMsgEnum.getCode();
    }

    public String getCheckMsg() {
        return checkCodeMsgEnum.getMessage();
    }

    private List<CheckSubSkuCanReceiveResVO> subSkuCheckList;

}
