package com.iqiyi.vip.dto.act;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/3 15:08
 */
@Data
public class ActMsg {

    private String actCode;

    private String name;

    private Long startTime;

    private Long endTime;

    /**
     * 加价购 or 买赠 用这个字段区分，是同一个MQ tag
     */
    private Integer type;

    private Integer status;

    private String platformCode;

    private Integer version;

    private String updateUser;

    private List<SubActMsg> subActList;


    /**
     * 扩展字段
     */
    private Object extraData;


    private String showEnv;

    private String mainAppid ;

    private String actProductName;

    private String priceUnit;

    private String partnerReceiveResultTitile;

    private String receiveRseat;

    private String payRseat;

    private String fc;
}
