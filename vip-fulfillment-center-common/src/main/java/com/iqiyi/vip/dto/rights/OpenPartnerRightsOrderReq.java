package com.iqiyi.vip.dto.rights;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/9/11 16:51
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OpenPartnerRightsOrderReq {

    private Long uid;
    private String skuId;
    private Integer skuAmount;
    private String orderCode;
    private Date payTime;
    private Integer status;

    /**
     * 实物品类权益开通需要
     */
    private Long realFee;
    private String parentOrderCode;
    private String tradeNo;
    private String refer;
    
    /**
     * 透传订单字段
     */
    private String tradeCode;
    private String platformCode;
    private String sellScene;
    private String frVersion;
    private String fv;
    private Long gateway;
    private Integer chargeType;
    private String partner;
    private Integer amount;
    private String businessValues;
    private Long contentId;
    private String productCode;
    /**
     * 当前的重试次数
     */
    private Integer currentRetryCount;
}
