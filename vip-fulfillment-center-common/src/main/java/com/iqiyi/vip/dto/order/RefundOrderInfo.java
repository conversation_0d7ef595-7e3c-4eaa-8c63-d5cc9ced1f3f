package com.iqiyi.vip.dto.order;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.Date;

import com.iqiyi.vip.utils.PressureTestUtils;

/**
 * 退单信息
 *
 * <AUTHOR>
 * @date 2023/9/26 10:30
 */
@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RefundOrderInfo {

    private Long uid;
    private String skuId;
    private Integer skuAmount;
    private String paidOrderCode;
    private String refundOrderCode;
    private Integer status;
    private Date refundTime;
    private String refer;

    /**
     * 透传订单字段
     */
    private String tradeNo;
    private String tradeCode;
    private String platformCode;
    private String sellScene;

    /**
     * 过滤需要处理的订单
     */
    public boolean needDealOrder() {
        if (null == refundTime || null == status) {
            log.error("[orderPaid payTime null ignore][refundOrderCode:{}]", refundOrderCode);
            return false;
        }
        if (StringUtils.isBlank(skuId)) {
            log.info("[skuId null ignore][refundOrderCode:{}]", refundOrderCode);
            return false;
        }
        if (PressureTestUtils.isPressureOrder(refer)) {
            log.info("[pressureTest order ignore][refundOrderCode:{}]", refundOrderCode);
            return false;
        }
        return true;
    }
}
