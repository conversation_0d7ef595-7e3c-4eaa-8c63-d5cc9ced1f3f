package com.iqiyi.vip.dto.rights;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.YesOrNoEnum;

/**
 * <AUTHOR>
 * @date 2023/11/30 15:15
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AskPartnerCanBuyByUrlIdDetailRes {

    private Long urlId;

    private String checkCode;
    private String checkMsg;
    private Integer canBuy;

    public Integer getCanBuy() {
        return CodeEnum.SUCCESS.getCode().equals(checkCode) ? YesOrNoEnum.YES.getValue() : YesOrNoEnum.NO.getValue();
    }

    public AskPartnerCanBuyByUrlIdDetailRes() {

    }

    public AskPartnerCanBuyByUrlIdDetailRes(Long urlId, CodeEnum codeMsgEnum) {
        this.urlId = urlId;
        this.checkCode = codeMsgEnum.getCode();
        this.checkMsg = codeMsgEnum.getMessage();
    }

    public AskPartnerCanBuyByUrlIdDetailRes(Long urlId, String checkCode, String checkMsg) {
        this.urlId = urlId;
        this.checkCode = checkCode;
        this.checkMsg = checkMsg;
    }
}
