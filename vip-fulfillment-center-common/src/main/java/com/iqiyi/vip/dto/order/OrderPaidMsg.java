package com.iqiyi.vip.dto.order;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.Date;

import com.iqiyi.vip.utils.PressureTestUtils;

/**
 * orderPaid消息
 *
 * <AUTHOR>
 * @date 2019/5/15 10:00
 */
@Slf4j
@Data
public class OrderPaidMsg {

    private String orderType;

    private String parentOrderCode;
    private String orderCode;
    private String tradeCode;
    private String actCode;
    private String centerCode;
    private String tradeNo;

    private Long userId;
    private Integer status;

    private Integer productId;
    private Integer amount;
    private int autoRenew;
    private Long platform;

    @JSONField(name = "fr_version")
    private String fr_version;
    private String frVersion;
    private String fv;
    private Integer pushChannel;
    private String businessValues;
    private Integer payType;
    private Long serviceId;
    private Integer productType;
    private String accountId;
    private Long gateway;
    private Integer couponFee;
    private String channel;
    private Integer chargeType;
    private Integer renewalsFlag;

    /**
     * 金额相关
     */
    private Integer fee;
    private Long realFee;
    private Integer settlementFee;
    private Integer productFee;

    /**
     * 时间相关字段
     */
    private Date payTime;
    private Date createTime;
    private Date updateTime;

    private String productCode;

    private String platformCode;

    private String partner;

    private String skuId;
    private Integer skuAmount;

    /**
     * 原始订单信息
     */
    private String originalOrderInfo;

    private String refer;

    private String extraInfo;

    private Long contentId;

    /**
     * 当前的重试次数
     */
    private Integer currentRetryCount;

    /**
     * 过滤需要处理的订单
     */
    public boolean needDealOrder() {
        if (null == payTime || null == status) {
            log.error("[orderPaid payTime null ignore][orderCode:{}]", orderCode);
            return false;
        }
        if (StringUtils.isBlank(skuId)) {
            log.info("[skuId null ignore][orderCode:{}]", orderCode);
            return false;
        }
        if (PressureTestUtils.isPressureOrder(refer)) {
            log.info("[pressureTest order ignore][orderCode:{}]", orderCode);
            return false;
        }
        return true;
    }
}
