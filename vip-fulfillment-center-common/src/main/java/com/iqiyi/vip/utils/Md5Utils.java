package com.iqiyi.vip.utils;

import com.google.common.base.Joiner;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;

/**
 * md5签名
 *
 * <AUTHOR>
 * @date 2019/4/16 10:10
 */
@Slf4j
public class Md5Utils {

    public static boolean verifySign(Map<String, Object> params, String key) {
        String reqSign = String.valueOf(params.get("sign"));
        if (StringUtils.isEmpty(reqSign)) {
            return false;
        }

        params.remove("sign");
        String mySign = sign(params, key);
        boolean checkRes = mySign.equals(reqSign);
        if (!checkRes) {
            log.error("[sign diff][mySign:{},reqSign:{}]", mySign, reqSign);
        }
        return checkRes;
    }

    public static boolean verifySignNotNull(Map<String, Object> params, String key) {
        String reqSign = String.valueOf(params.get("sign"));
        if (StringUtils.isEmpty(reqSign)) {
            return false;
        }

        params.remove("sign");
        String mySign = signNotNull(params, key);
        boolean checkRes = mySign.equals(reqSign);
        if (!checkRes) {
            log.error("[sign diff][mySign:{},reqSign:{}]", mySign, reqSign);
        }
        return checkRes;
    }

    public static String signNotNull(Map<String, Object> params, String key) {
        SortedMap<String, Object> sortedParams = new TreeMap<>(params);
        StringBuffer sb = new StringBuffer();
        for (String para : sortedParams.keySet()) {
            Object ov = params.get(para);
            if (ov == null || StringUtils.isBlank(ov.toString())) {
                continue;
            }
            sb.append(para).append("=").append(params.get(para)).append("&");
        }
        sb.delete(sb.length() - 1, sb.length()).append(key);
        log.info("md5 before:" + sb.toString());
        return EncodeUtils.MD5(sb.toString(), "UTF-8");
    }


    public static String sign(Map<String, Object> params, String key) {
        SortedMap<String, Object> sortedParams = new TreeMap<>(params);
        StringBuffer sb = new StringBuffer();
        for (String para : sortedParams.keySet()) {
            sb.append(para).append("=").append(params.get(para)).append("&");
        }
        sb.delete(sb.length() - 1, sb.length()).append(key);
        log.info("md5 before:" + sb.toString());
        return EncodeUtils.MD5(sb.toString(), "UTF-8");
    }

    public static String signForTag(Map<String, Object> params, String secretKey) {
        SortedMap<String, Object> sortedParams = new TreeMap<>(params);
        String strForSign = Joiner.on("&").useForNull("").withKeyValueSeparator("=").join(sortedParams).concat(secretKey);
        String sign = DigestUtils.md5Hex(strForSign);
        return sign;
    }


    public static String sortParam(Map<String, Object> params) {
        SortedMap<String, Object> sortedParams = new TreeMap<>(params);
        StringBuffer sb = new StringBuffer();
        for (String para : sortedParams.keySet()) {
            sb.append(para).append("=").append(params.get(para)).append("|");
        }
        sb.delete(sb.length() - 1, sb.length());
        log.info("sortParam result:" + sb.toString());
        return sb.toString();
    }

    public static String sortParam2(Map<String, String> params) {
        SortedMap<String, Object> sortedParams = new TreeMap<>(params);
        StringBuffer sb = new StringBuffer();
        for (String para : sortedParams.keySet()) {
            sb.append(para).append("=").append(params.get(para)).append("&");
        }
        sb.delete(sb.length() - 1, sb.length());
        log.info("sortParam result:" + sb.toString());
        return sb.toString();
    }

    public static String verticalVirguleSign(Map<String, Object> params, String secretKey) {
        SortedMap<String, Object> sortedParams = new TreeMap<>(params);
        StringBuilder sb = new StringBuilder();
        for (String key : sortedParams.keySet()) {
            String val = String.valueOf(sortedParams.get(key));
            sb.append(key).append("=").append(org.apache.commons.lang.StringUtils.defaultIfEmpty(val, "")).append("|");
        }
        return EncodeUtils.MD5(sb.append(secretKey).toString(), "UTF-8");
    }
}
