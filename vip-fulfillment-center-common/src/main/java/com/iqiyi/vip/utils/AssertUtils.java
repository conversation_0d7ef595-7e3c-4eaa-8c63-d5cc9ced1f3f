package com.iqiyi.vip.utils;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Map;

import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.exception.BizRuntimeException;

/**
 * <AUTHOR>
 * @date 2023/8/1 14:35
 */
public class AssertUtils {

    public static void notBlank(String str, CodeEnum codeEnum) {
        if (StringUtils.isBlank(str)) {
            throw new BizRuntimeException(codeEnum);
        }
    }

    public static void notNull(Object obj, CodeEnum codeEnum) {
        if (null == obj) {
            throw new BizRuntimeException(codeEnum);
        }
    }

    public static void notEmpty(Collection<?> collection, CodeEnum codeEnum) {
        if (CollectionUtils.isEmpty(collection)) {
            throw new BizRuntimeException(codeEnum);
        }
    }

    public static void mapNotEmpty(Map map, CodeEnum codeEnum) {
        if (MapUtils.isEmpty(map)) {
            throw new BizRuntimeException(codeEnum);
        }
    }
}
