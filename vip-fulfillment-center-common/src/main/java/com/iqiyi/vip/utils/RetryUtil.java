package com.iqiyi.vip.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Callable;

/**
 * <AUTHOR>
 * @date 2025/4/2 14:17
 */
@Slf4j
public class RetryUtil {

    public static <T> T executeWithRetry(Callable<T> task, int maxAttempts, Class<? extends Exception>... retryFor) {
        int attempts = 0;
        while (attempts < maxAttempts) {
            try {
                return task.call();
            } catch (Exception e) {
                attempts++;
                // 判断是否是需要重试的异常类型
                boolean shouldRetry = false;
                for (Class<? extends Exception> retryException : retryFor) {
                    if (retryException.isInstance(e)) {
                        shouldRetry = true;
                        break;
                    }
                }

                if (!shouldRetry || attempts >= maxAttempts) {
                    throw new RuntimeException(e);
                }

                log.warn("Retry attempt {} of {} failed: {}", attempts, maxAttempts, e.getMessage());
            }
        }
        throw new RuntimeException("Max retry attempts reached");
    }
}
