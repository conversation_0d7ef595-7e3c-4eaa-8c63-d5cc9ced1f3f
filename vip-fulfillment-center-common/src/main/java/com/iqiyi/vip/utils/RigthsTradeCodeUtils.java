package com.iqiyi.vip.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 第三方流水号生成
 *
 * <AUTHOR>
 * @date 2019/4/16 10:10
 */
@Slf4j
public class RigthsTradeCodeUtils {


    /**
     * 第三方业务流水号格式：prefix+18位日期(yyyyMMddHHmmssSSSS) + 5位随机数 (随机三次, 每次0-9间数字+订单数量数字)
     * 字符总位数需小于50
     * @param prefix     前缀字符
     * @param businessNo 任意业务字符
     * @return
     */
    public static String getRightsTradeCode(String prefix, String businessNo) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmssSSSS");
        String datePre = formatter.format(new Date());
        return prefix + datePre + businessNo + getNumR(5);
    }

    private static String getNumR(int length) {
        final int maxNum = 10;
        int i; // 生成的随机数
        int count = 0; // 生成的密码的长度
        char[] str = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9'};
        StringBuffer pwd = new StringBuffer("");
        //Random每次需要一个随机值时创建一个新对象都是低效的
//        Random r = new Random();
        while (count < length) {
            // 生成随机数，取绝对值，防止生成负数，
            i = Math.abs(RandomUtils.nextInt(0,maxNum));
            if (i >= 0 && i < str.length) {
                pwd.append(str[i]);
                count++;
            }
        }

        return pwd.toString();
    }

}
