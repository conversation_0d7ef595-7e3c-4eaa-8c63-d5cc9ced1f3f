package com.iqiyi.vip.utils;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

/**
 * 日期工具类
 *
 * <AUTHOR> on 2019/1/1
 */
public class DateUtils {

    public static Date longToDate(Long time) {
        return null == time ? null : new Date(time);
    }

    public static boolean verifyReceiveDeadlineTime(Date recordReceiveDeadlineTime) {
        return null == recordReceiveDeadlineTime || recordReceiveDeadlineTime.after(new Date());
    }

    public static boolean isSameYear(final Date source, final Date target) {
        if (null == source || null == target) {
            return false;
        }
        Calendar sourceDateTime = Calendar.getInstance();
        sourceDateTime.setTime(source);

        Calendar targetDateTime = Calendar.getInstance();
        targetDateTime.setTime(target);
        return sourceDateTime.get(Calendar.YEAR) == targetDateTime.get(Calendar.YEAR);
    }

    public static Date long2Date(Long time) {
        return null == time ? null : new Date(time);
    }

    public static Date long2DateNUllDefaultNow(Long time) {
        return null == time ? new Date() : new Date(time);
    }

    public static Date getDateOfSeconds(Date date, Integer seconds) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.SECOND, seconds);
        return calendar.getTime();
    }

    public static Date getDateFromStr(String source, String sourcePattern) {
        SimpleDateFormat format = new SimpleDateFormat(sourcePattern);
        try {
            Date date = format.parse(source);
            return date;
        } catch (Exception e) {
            return null;
        }
    }

    public static Long str2LongTime(String date, String format) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            return sdf.parse(date).getTime();
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 获取指定时间年份的，第一秒
     *
     * @param dateTime
     * @return
     */
    public static Date getDateTimeYearFirstSecondTime(Date dateTime) {
        Calendar startTime = Calendar.getInstance();
        startTime.setTime(dateTime);
        int dateTimeYear = startTime.get(Calendar.YEAR);
        return getYearFirst(dateTimeYear);
    }

    /**
     * 获取指定时间年份的，最后一秒
     *
     * @param dateTime
     * @return
     */
    public static Date getDateTimeYearLastSecondTime(Date dateTime) {
        Calendar endTime = Calendar.getInstance();
        endTime.setTime(dateTime);
        int dateTimeYear = endTime.get(Calendar.YEAR);

        Date lastDate = getYearLast(dateTimeYear);
        return getDateFromStr(getFormatDate(lastDate) + " 23:59:59", "yyyy-MM-dd HH:mm:ss");
    }

    public static String getFormatDate(Date date) {
        try {
            SimpleDateFormat formater = new SimpleDateFormat("yyyy-MM-dd");
            String result = formater.format(date);
            return result;
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 获取本地长格式时间（14位）
     *
     * @return 本地长格式时间
     */
    public static String getLocalLongDate14() {
        String dateString = "";
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss",
            Locale.US);
        dateString = formatter.format(new Date());
        return dateString;
    }

    /**
     * 获取某年第一天日期
     *
     * @param year
     * @return
     */
    public static Date getYearFirst(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        return calendar.getTime();
    }


    /**
     * 获取某年最后一天日期
     *
     * @param year
     * @return
     */
    public static Date getYearLast(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        calendar.roll(Calendar.DAY_OF_YEAR, -1);
        return calendar.getTime();
    }

    /**
     * dateString must be yyyy-mm-dd hh:mi:ss
     *
     * @param dateString 日期字符串
     * @return Timestamp
     */
    public static Timestamp getTimestamp(String dateString) {
        return Timestamp.valueOf(dateString);
    }
}
