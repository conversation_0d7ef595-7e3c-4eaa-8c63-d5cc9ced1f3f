package com.iqiyi.vip.utils;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/23 14:22
 */
@Slf4j
public class FileUtils {

    public static List<String> readFile(File file) {
        try {
            List<String> lineList = Lists.newArrayList();
            FileReader fr = new FileReader(file);
            BufferedReader br = new BufferedReader(fr);
            String ls;
            log.info("读取中，文件的内容如下：");
            while ((ls = br.readLine()) != null) {
                lineList.add(ls);
                System.out.println(ls);
            }
            br.close();
            return lineList;
        } catch (IOException e) {
            log.error("", e);
            return null;
        }
    }
}
