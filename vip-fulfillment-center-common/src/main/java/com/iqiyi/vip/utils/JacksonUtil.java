package com.iqiyi.vip.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.exception.BizRuntimeException;

/**
 * <AUTHOR>
 * @date 2022/7/18 13:50
 */
@Slf4j
public class JacksonUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    static {
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
    }

    public static <T> T json2obj(String jsonStr, Class<T> clazz) throws Exception {
        return objectMapper.readValue(jsonStr, clazz);
    }

    public static <T> T json2objCheckNull(String jsonStr, Class<T> clazz) {
        try {
            if (StringUtils.isBlank(jsonStr)) {
                return null;
            }
            return objectMapper.readValue(jsonStr, clazz);
        } catch (Exception e) {
            log.error("[jsonStr:{}]", jsonStr, e);
            throw new BizRuntimeException(CodeEnum.ERROR_SYSTEM);
        }
    }

    public static <T> T json2objFailIgnore(String jsonStr, Class<T> clazz) {
        try {
            if (StringUtils.isBlank(jsonStr)) {
                return null;
            }
            return objectMapper.readValue(jsonStr, clazz);
        } catch (Exception e) {
            log.info("[jsonStr:{}]", jsonStr, e);
            return null;
        }
    }

    public static String writeValueAsString(Object value) throws Exception {
        return objectMapper.writeValueAsString(value);
    }

    public static String writeValueAsStringThrowException(Object value) {
        try {
            if (null == value) {
                return "";
            }
            return objectMapper.writeValueAsString(value);
        } catch (Exception e) {
            log.error("[value:{}]", value, e);
            throw new BizRuntimeException(CodeEnum.ERROR_SYSTEM);
        }
    }

    public static <T> T convertValue(Object fromValue, Class<T> toValueType) {
        try {
            return objectMapper.convertValue(fromValue, toValueType);
        } catch (Exception e) {
            log.error("[Exception][fromValue:{},toValueType:{}]", fromValue, toValueType, e);
            return null;
        }
    }

    public static <T> T convertValue(Object fromValue, TypeReference<?> toValueTypeRef) {
        try {
            return (T) objectMapper.convertValue(fromValue, toValueTypeRef);
        } catch (Exception e) {
            log.error("[Exception][fromValue:{}]", fromValue, e);
            return null;
        }
    }
}
