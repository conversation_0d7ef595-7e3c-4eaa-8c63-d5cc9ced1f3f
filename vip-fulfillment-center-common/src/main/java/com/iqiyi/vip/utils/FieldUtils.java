package com.iqiyi.vip.utils;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2023/9/19 16:04
 */
@Slf4j
public class FieldUtils {

    /**
     * 从json串里提取某些字段的值放到map里
     *
     * @param extractedFieldNameList 需要提取的字段
     * @param targetMap 目标map
     * @param originalJsonData 原始数据
     */
    public static void extracted(String extractedFieldNameList, Map<String, Object> targetMap, String originalJsonData) {
        try {
            if (StringUtils.isBlank(extractedFieldNameList)) {
                return;
            }
            String[] mapParamArray = extractedFieldNameList.split("\\|");
            for (String fieldNameConfig : mapParamArray) {
                if (StringUtils.isBlank(fieldNameConfig)) {
                    continue;
                }
                String[] strs = fieldNameConfig.split("=");
                //请求变量名
                String reqFieldName = strs.length > 1 ? strs[1] : strs[0];
                String fieldName = strs[0];
                String[] subField = fieldName.split("\\.");
                JSONObject jsonObject = JSONObject.parseObject(originalJsonData);
                Object fieldValue;
                if (subField.length == 1) {
                    fieldValue = jsonObject.get(fieldName);
                } else {
                    fieldValue = getFieldValue(jsonObject, 0, subField);
                }
                if (null == fieldValue) {
                    log.error("[filedValue null][mapParam:{},originalJsonData:{}]", fieldName, originalJsonData);
                    continue;
                }

                targetMap.put(reqFieldName, String.valueOf(fieldValue));
            }
        } catch (Exception e) {
            log.error("[extractedFieldNameList:{},originalJsonData:{},targetMap:{}]", extractedFieldNameList, originalJsonData, targetMap, e);
        }
    }

    private static Object getFieldValue(JSONObject jsonObject, int index, String[] subField) {
        if (null == jsonObject) {
            return null;
        }
        if (index == subField.length - 1) {
            return jsonObject.get(subField[index]);
        }
        jsonObject = jsonObject.getJSONObject(subField[index]);
        return getFieldValue(jsonObject, index + 1, subField);
    }

    private static Pattern NUMBER_PATTERN = Pattern.compile("[\\d]");

    /**
     * 数据脱敏
     */
    public static String desensitizeNum(String desensitizeStr) {
        try {
            return NUMBER_PATTERN.matcher(desensitizeStr).replaceAll("*");
        } catch (Exception e) {
            return desensitizeStr;
        }
    }

    /**
     * 判断是否手机号
     */
    public static boolean isMobile(String mobile) {
        if (StringUtils.isBlank(mobile)) {
            return false;
        }
        String regex = "^((13[0-9])|(14[5,7,9])|(15([0-3]|[5-9]))|(16[5,6])|(17[0-8])|(18[0-9])|(19[1、5、8、9]))\\d{8}$";
        if (mobile.length() != 11) {
            return false;
        } else {
            Pattern p = Pattern.compile(regex);
            Matcher m = p.matcher(mobile);
            return m.matches();
        }
    }
}
