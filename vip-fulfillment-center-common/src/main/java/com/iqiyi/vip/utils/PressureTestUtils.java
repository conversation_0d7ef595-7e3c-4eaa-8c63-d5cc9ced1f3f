package com.iqiyi.vip.utils;

import lombok.extern.slf4j.Slf4j;

/**
 * 全链路压测数据判断
 *
 * <AUTHOR>
 * @date 2023/10/19 13:35
 */
@Slf4j
public class PressureTestUtils {

    private static final String MODE_TYPE_PRESS_TEST = "pressureTest";

    /**
     * true 需要处理 false 不需要处理
     */
    public static boolean isPressureOrder(String refer) {
        return MODE_TYPE_PRESS_TEST.equalsIgnoreCase(OrderUtils.getModeType(refer));
    }
}
