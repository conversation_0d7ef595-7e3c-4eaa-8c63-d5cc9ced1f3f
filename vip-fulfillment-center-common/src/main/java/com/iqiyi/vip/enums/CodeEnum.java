package com.iqiyi.vip.enums;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * 响应码枚举
 *
 * <AUTHOR>
 * @date 2021-05-25
 */
public enum CodeEnum {

    //Http调用错误码以H开头定义
    HTTP_ERROR("H00001", "系统内部调用异常，请稍后再试"),
    HTTP_ERROR_CONNECT_TIME_OUT("H00002", "ConnectTimeoutException"),
    HTTP_ERROR_SOCKET_TIME_OUT("H00003", "SocketTimeoutException"),
    HTTP_ERROR_UNKNOWN_HOST("H00004", "UnknownHostException"),
    HTTP_ERROR_CONNECTION_REFUSED("H00005", "Connection refused"),
    HTTP_PARTNER_RIGHT_ERROR("H00001", "http第三方权益服务异常"),

    SUCCESS("A00000", "success"),
    SUC_CODE_REPEAT("A00001", "重复请求"),
    SUC_CANCEL_ORDER("A00002", "取消订单成功"),
    ERROR_ORDER_IS_RECEIVED("A00006","订单已领过,且权益已开通"),

    ERROR_PARAM("Q00301", "参数错误,缺少必填参数"),
    ERROR_NOT_LOG("CM000006", "未登录，请登录"),
    ERROR_UNBIND_MOBILE("CM00021", "本活动需要爱奇艺账号绑定手机号后才可参与，请先前往“爱奇艺APP-我的-设置”完成绑定"),
    ERROR_SIGN("Q00302", "签名错误"),
    RESPONSE_LOAD_TOO_HIGH("Q00303", "当前活动火爆，请稍后重试～"),
    ERROR_SYSTEM("Q00332", "系统异常"),
    SENTINEL_LIMIT("Q00449", "当前活动太火爆了，请稍后重试"),
    ERROR_SKU_INVALID("Q00401", "活动已结束，请查看其他活动"),
    ERROR_SKU_NULL("Q00404", "商品不存在"),
    NOT_PAID_ORDER("Q01201", "非支付完成订单，忽略"),
    ERROR_IS_REFUND("Q00213", "订单已退，不能补送"),
    NOT_REFUND_ORDER("Q01202", "非退单，忽略"),
    NO_PAID_ORDER("Q01203", "无对应正单，忽略"),
    PAID_ORDER_QUERY_FAIL("Q01204", "未查到对应正单"),
    ERROR_FULFILL_NOT_MATCH("Q00605", "非本系统履约订单"),
    ERROR_TIMES_LIMIT("Q00206", "开通次数超出限制请更换账号"),
    ERROR_RETREAT_LOWER_RIGHT("Q00207", "回收低等级权益失败"),
    ERROR_INVENTORY_EXCEED("Q00219", "商品已售罄"),
    ERROR_SKU_TIMES_LIMIT("Q00220", "商品超出限购次数"),
    ERROR_ACT_TIMES_LIMIT("Q00221", "活动超出限购次数"),
    ERROR_STOCK_DEDUCT("E00010", "库存扣减失败"),
    ERROR_STOCK_REVERT("E00011", "回滚库存失败"),
    ERROR_CANCEL_ORDER("E00012", "取消订单失败"),

    ERROR_SYS_CONFIG("E00000", "系统配置异常，请检查核对"),
    ERROR_SEND_TYPE("Q00125", "系统配置异常,未知的发放类型"),
    ERROR_USER_NO_RIGHT_RECORD("Q01001", "用户没有领取记录"),
    ERROR_NO_RIGHT("Q00102", "没有领取资格"),
    ERR_CONCURRENT("Q00005", "并发请求，请重试"),

    ERROR_CAN_NOT_REFUND("E00002", "不可回收权益"),
    ERROR_RECEIVE_NOT_EXISTS("Q00223", "记录不存在"),
    ACCOUNT_NOT_SUPPORT("E00503", "不支持使用此账号领取"),

    ERROR_ORDER_NON("E00404","订单不存在"),
    ERROR_ORDER_REFUND("E00406","订单已退"),
    ERROR_ORDER_RECEIVE_EXPIRE("E00407","订单已过领取时间"),
    ERROR_BENEFIT_CODE("E00505", "福利无法开通"),
    ERROR_USER_IDENTITY("Q00148","你不是受邀用户，请更换爱奇艺账号"),
    ERROR_REFUND_AMOUNT("E00506","退单数量大于原订单数量"),
    ERROR_ASYNC_DEAL("E00507","数据接收成功，异步处理中"),
    ERROR_FREE_PAY("E00508","免费下单失败，请稍后再试"),
    ;
    private String code;
    private String message;

    CodeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static CodeEnum of(String code) {
        for (CodeEnum codeEnum : CodeEnum.values()) {
            if (codeEnum.getCode().equals(code)) {
                return codeEnum;
            }
        }
        return null;
    }

    /**
     * 生成记录开通权益需要重试的code
     */
    public static List<CodeEnum> openRightsNeedRetryCodeEnumList() {
        List<CodeEnum> retryCodeEnumList = Lists.newArrayList();
        retryCodeEnumList.add(ERROR_SYSTEM);
        retryCodeEnumList.add(ERR_CONCURRENT);
        retryCodeEnumList.add(ERROR_SEND_TYPE);
        retryCodeEnumList.add(HTTP_ERROR);
        retryCodeEnumList.add(ERROR_SKU_NULL);
        retryCodeEnumList.add(ERROR_INVENTORY_EXCEED);
        retryCodeEnumList.add(ERROR_TIMES_LIMIT);
        retryCodeEnumList.add(ERROR_RETREAT_LOWER_RIGHT);
        retryCodeEnumList.add(ERROR_CANCEL_ORDER);
        return retryCodeEnumList;
    }

    public static List<CodeEnum> terminateRightsNeedRetryCodeEnumList() {
        List<CodeEnum> retryCodeEnumList = Lists.newArrayList();
        retryCodeEnumList.add(ERROR_SYSTEM);
        retryCodeEnumList.add(ERR_CONCURRENT);
        retryCodeEnumList.add(HTTP_ERROR);
        retryCodeEnumList.add(ERROR_SKU_NULL);
        return retryCodeEnumList;
    }

    public static List<CodeEnum> checkOrderCanTerminateCanRefund() {
        List<CodeEnum> retryCodeEnumList = Lists.newArrayList();
        retryCodeEnumList.add(ERROR_SYSTEM);
        retryCodeEnumList.add(ERROR_RECEIVE_NOT_EXISTS);
        return retryCodeEnumList;
    }
}
