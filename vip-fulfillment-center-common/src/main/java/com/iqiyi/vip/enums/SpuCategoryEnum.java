package com.iqiyi.vip.enums;

import lombok.Getter;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/8/31 14:24
 */
@Getter
public enum SpuCategoryEnum {

    VIP("vip", "会员品类"),
    MARKETING("marketing", "打包购类目"),
    PACKAGE("package", "套餐类"),
    VOD("vod", "单点类"),
    PARTNER("partner", "合作方类");

    private String categoryId;

    private String categoryName;

    SpuCategoryEnum(String categoryId, String categoryName) {
        this.categoryId = categoryId;
        this.categoryName = categoryName;
    }

    public static SpuCategoryEnum ofValue(String categoryId) {
        if (StringUtils.isBlank(categoryId)) {
            return null;
        }
        for (SpuCategoryEnum spuCategoryEnum : values()) {
            if (spuCategoryEnum.categoryId.equals(categoryId)) {
                return spuCategoryEnum;
            }
        }
        return null;
    }
}
