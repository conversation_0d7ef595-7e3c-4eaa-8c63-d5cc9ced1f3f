package com.iqiyi.vip.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 会员商品类目与gitCode的映射枚举
 *
 * <AUTHOR>
 * @date 2025-08-20
 */
@Getter
public enum VipGiftCodeMappingEnum {

    VIP_MAPPING(SpuCategoryEnum.VIP, GiftCodeEnum.VIP_GIFT_CODE, "会员品类映射"),
    VOD_MAPPING(SpuCategoryEnum.VOD, GiftCodeEnum.VOD_GIFT_CODE, "单点类映射"),
    PACKAGE_MAPPING(SpuCategoryEnum.PACKAGE, GiftCodeEnum.PACKAGE_GIFT_CODE, "套餐类映射");

    private final SpuCategoryEnum spuCategory;
    private final GiftCodeEnum giftCode;
    private final String description;

    private static final Map<String, String> CATEGORY_TO_GIFT_CODE_MAP = new HashMap<>();

    static {
        for (VipGiftCodeMappingEnum mapping : values()) {
            CATEGORY_TO_GIFT_CODE_MAP.put(mapping.spuCategory.getCategoryId(), mapping.giftCode.getGiftCode());
        }
    }

    VipGiftCodeMappingEnum(SpuCategoryEnum spuCategory, GiftCodeEnum giftCode, String description) {
        this.spuCategory = spuCategory;
        this.giftCode = giftCode;
        this.description = description;
    }

    /**
     * 根据商品类目ID获取对应的giftCode
     *
     * @param categoryId 商品类目ID
     * @return giftCode，如果没有找到则返回null
     */
    public static String getGiftCodeByCategoryId(String categoryId) {
        if (StringUtils.isBlank(categoryId)) {
            return null;
        }
        return CATEGORY_TO_GIFT_CODE_MAP.get(categoryId);
    }

    /**
     * 检查是否包含指定的giftCode
     *
     * @param giftCode giftCode
     * @return true包含，false不包含
     */
    public static boolean containsGiftCode(String giftCode) {
        if (StringUtils.isBlank(giftCode)) {
            return false;
        }
        return CATEGORY_TO_GIFT_CODE_MAP.containsValue(giftCode);
    }

}
