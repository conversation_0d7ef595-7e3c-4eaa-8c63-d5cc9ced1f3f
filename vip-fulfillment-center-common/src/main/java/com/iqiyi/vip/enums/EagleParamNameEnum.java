package com.iqiyi.vip.enums;

import lombok.Getter;

/**
 * 鹰眼指标名称枚举
 *
 * <AUTHOR>
 * @date 2023/12/14 14:13
 */
@Getter
public enum EagleParamNameEnum {

    BIZ_CODE_COUNTER("biz_code_counter", "业务码计数器"),
    ORDER_OPEN_FAIL_EAGLE_MONITOR("order_open_fail_eagle_monitor", "订单权益开通失败鹰眼监控"),
    ASYNC_TASK_STAT_MONITOR("task_execution_stats", "异步任务执行统计监控");

    private String name;
    private String desc;

    EagleParamNameEnum(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }
}
