package com.iqiyi.vip.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2020/2/26 16:30
 */
@Getter
public enum SendStatusEnum {

    UN_SEND(1, "未调用"),
    SEND_SUC(2, "调用成功"),
    SEND_FAIL(3, "调用失败,不需重试"),
    SEND_FAIL_RETRY(4, "调用失败,需重试"),
    NO_NEED_SEND(5, "无需通知"),
    NEED_CANCEL(6, "需要取消")
    ;

    private int status;
    private String desc;

    SendStatusEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static boolean isSyncSuccess(Integer status) {
        if (null == status) {
            return false;
        }
        return SEND_SUC.getStatus() == status;
    }

    public static boolean needRetry(Integer status) {
        return null == status || SEND_FAIL_RETRY.getStatus() == status;
    }

    public static SendStatusEnum of(int status) {
        for (SendStatusEnum statusEnum : SendStatusEnum
            .values()) {
            if (statusEnum.status == status) {
                return statusEnum;
            }
        }
        return null;
    }
}
