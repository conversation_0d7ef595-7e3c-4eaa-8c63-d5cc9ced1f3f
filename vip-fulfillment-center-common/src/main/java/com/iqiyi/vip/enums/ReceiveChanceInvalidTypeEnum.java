package com.iqiyi.vip.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/10/12 18:35
 */
public enum ReceiveChanceInvalidTypeEnum {
    UNKNOW(-1, "未知"),
    EXPIRED(0, "已过期"),
    REFUND(1, "已退单"),
    ;

    private int status;
    @Getter
    private String desc;

    ReceiveChanceInvalidTypeEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    @JsonCreator
    public static ReceiveChanceInvalidTypeEnum of(int status) {
        for (ReceiveChanceInvalidTypeEnum receiveStatusEnum : ReceiveChanceInvalidTypeEnum
            .values()) {
            if (receiveStatusEnum.getStatus() == status) {
                return receiveStatusEnum;
            }
        }
        return null;
    }

    @JsonValue
    public int getStatus() {
        return status;
    }
}
