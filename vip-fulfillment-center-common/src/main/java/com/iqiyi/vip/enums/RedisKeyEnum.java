package com.iqiyi.vip.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/9/12 19:05
 */
@Getter
public enum RedisKeyEnum {

    OPEN_RIGHTS("fulfill_openRights_uid_%d_skuId_%s_orderCode_%s"),
    SKU_PROMOTION("vip_fulfill_sku2Promotion_%s"),
    PACK_SKU_PROMOTION("vip_fulfill_packSku2Promotion_%s"),
    ASSET_RMS_SKU_PROMOTION("AssetRmsSkuIdList"),
    BATCH_QUERY_SKU("vip_fulfill_batchQuerySkuInfoList_%s_valid_%s"),
    OPEN_PARTNER_RIGHTS("partner_openRights_uid_%d_orderCode_%s"),
    ;

    private String key;

    RedisKeyEnum(String key) {
        this.key = key;
    }

    public String getRedisKey(Object... args) {
        return String.format(key, args);
    }
}
