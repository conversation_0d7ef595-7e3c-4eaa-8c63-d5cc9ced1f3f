package com.iqiyi.vip.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 资产页展示时的领取状态tab ReceiveChanceStatusEnum,可传多个（英文逗号分隔）,默认返回所有记录
 *
 * <AUTHOR>
 * @version 1.0 2021年2月4日
 */
public enum ReceiveChanceStatusEnum {

    UN_KNOWN(-1, "未知"),
    PENDING_RECEIVE(0, "待领取"),
    RECEIVED(1, "已领取"),
    INVALID(2, "已失效"),
    ;

    private int status;
    @Getter
    private String desc;

    ReceiveChanceStatusEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    @JsonCreator
    public static ReceiveChanceStatusEnum of(int status) {
        for (ReceiveChanceStatusEnum receiveStatusEnum : ReceiveChanceStatusEnum
            .values()) {
            if (receiveStatusEnum.getStatus() == status) {
                return receiveStatusEnum;
            }
        }
        return null;
    }

    @JsonValue
    public int getStatus() {
        return status;
    }
}
