package com.iqiyi.vip.enums;

/**
 * 商品类型 枚举值
 *
 * @author: lin<PERSON><PERSON><PERSON>
 * @createTime: 2025/01/16
 */
public enum SkuIdentifierEnum {
    REGULAR_PACKAGE(1, "常规连包"),
    SESAME_PURCHASE(2, "芝麻购"),
    FIRST_PERIOD_DISCOUNT(3, "首X期优惠"),
    SPECIFIC_PRICE_SPECIFIC_DAYS_CARD(4, "X元N天卡"),
    PURE_SIGN(5, "纯签约"),
    FREE_EXCHANGE_UPGRADE(6, "兑换升级");

    private final int value;
    private final String desc;


    public Integer getValue() {
        return value;
    }

    // 获取枚举值的描述
    public String getDesc() {
        return desc;
    }

    SkuIdentifierEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

}
