package com.iqiyi.vip.enums;

import lombok.Getter;

/**
 * 退单是否回收权益
 *
 * <AUTHOR>
 * @date 2023/9/26 14:06
 */
@Getter
public enum RefundPartnerType {

    NOT_REFUND(0, "不回收权益"),
    REFUND_RIGHTS(1, "回收权益");

    private int type;
    private String desc;

    RefundPartnerType(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static int getRefundPartnerType(Integer refundPartnerType) {
        if (null == refundPartnerType) {
            //默认需要调用合作方接口退权益
            return REFUND_RIGHTS.type;
        }
        return NOT_REFUND.type == refundPartnerType ? NOT_REFUND.type : REFUND_RIGHTS.type;
    }
}
