package com.iqiyi.vip.enums;

import lombok.Getter;

/**
 * 商品切换账号类型
 *
 * <AUTHOR>
 * @date 2024/8/13 14:10
 */
@Getter
public enum SkuAccountTypeEnum {

    MOBILE(0, "手机号类型", YesOrNoEnum.NO, null),
    QQ(1, "QQ号", YesOrNoEnum.NO, null),
    QQ_MUSIC_AUTH(2, "QQ音乐授权", YesOrNoEnum.YES, ThirdAuthTypeEnum.QQ_MUSIC_AUTH),
    QQ_SUPER_VIP(6, "QQ超会", YesOrNoEnum.YES, ThirdAuthTypeEnum.QQ_SUPER_VIP_AUTH);

    private Integer type;
    private String desc;

    /**
     * 是否需要根据授权服务返回结果重置sku的changeAccount
     */
    private YesOrNoEnum resetChangeAccountByThirdAuth;

    private ThirdAuthTypeEnum thirdAuthTypeEnum;

    SkuAccountTypeEnum(int type, String desc, YesOrNoEnum resetChangeAccountByThirdAuth, ThirdAuthTypeEnum thirdAuthTypeEnum) {
        this.type = type;
        this.desc = desc;
        this.resetChangeAccountByThirdAuth = resetChangeAccountByThirdAuth;
        this.thirdAuthTypeEnum = thirdAuthTypeEnum;
    }

    public static SkuAccountTypeEnum getByType(Integer type) {
        if (null == type) {
            return null;
        }
        for (SkuAccountTypeEnum skuAccountTypeEnum : SkuAccountTypeEnum.values()) {
            if (skuAccountTypeEnum.getType().equals(type)) {
                return skuAccountTypeEnum;
            }
        }
        return null;
    }
}
