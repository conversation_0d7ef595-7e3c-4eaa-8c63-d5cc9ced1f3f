package com.iqiyi.vip.enums;

import lombok.Getter;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/4/11 16:31
 */
@Getter
public enum SpuEnum {

    THIRD_RIGHTS_SPU("spu_outside_goods", "第三方商品"),
    BENEFIT_SPU("spu_benefit", "福利"),
    SPU_SHOPPING_UNION("spu_shopping_union", "实物商品", GiftCodeEnum.SHOP_GIFT_CODE),
    COUPON_SPU("spu_voucher", "代金券商品", GiftCodeEnum.COUPON_GIFT_CODE),
    PACKAGE_SPU("spu_package", "打包购"),
    PARTNER_PACKAGE_SPU("spu_union_purchase", "对外合作打包购"),
    ;

    private String spuId;
    private String spuName;
    private GiftCodeEnum giftCodeEnum;

    SpuEnum(String spuId, String spuName) {
        this.spuId = spuId;
        this.spuName = spuName;
    }

    SpuEnum(String spuId, String spuName, GiftCodeEnum giftCodeEnum) {
        this.spuId = spuId;
        this.spuName = spuName;
        this.giftCodeEnum = giftCodeEnum;
    }

    /**
     * 是否是打包购spu
     */
    public static boolean isPackageSpu(String spuId) {
        if (StringUtils.isBlank(spuId)) {
            return false;
        }
        return PACKAGE_SPU.getSpuId().equals(spuId) || PARTNER_PACKAGE_SPU.getSpuId().equals(spuId);
    }

}
