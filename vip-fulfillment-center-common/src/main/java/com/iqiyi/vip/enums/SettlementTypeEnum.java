package com.iqiyi.vip.enums;

public enum SettlementTypeEnum {

    FIX_PRICE(0, "固定结算价"),
    PERCENT(1, "百分比");

    private Integer type;

    private String msg;


    SettlementTypeEnum(Integer type, String msg) {
        this.type = type;
        this.msg = msg;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static SettlementTypeEnum of(Integer type) {
        if (null == type) {
            return null;
        }
        for (SettlementTypeEnum settlementTypeEnum : SettlementTypeEnum.values()) {
            if (settlementTypeEnum.getType().equals(type)) {
                return settlementTypeEnum;
            }
        }
        return null;
    }
}
