package com.iqiyi.vip.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/8/31 14:46
 */
public enum RightsRecordSendStatusEnum {

    /**
     * send status
     */
    UN_SEND(1, "未调用"),
    SEND_SUC(2, "调用成功"),
    SEND_FAIL(3, "调用失败,不需重试"),
    SEND_FAIL_RETRY(4, "调用失败,需重试"),
    NO_NEED_SEND(5, "无需通知"),
    ;

    @Getter
    private int status;
    @Getter
    private String desc;

    RightsRecordSendStatusEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static boolean isFail(Integer status) {
        if (null == status) {
            return true;
        }
        return SEND_FAIL.getStatus() == status || SEND_FAIL_RETRY.getStatus() == status;
    }
}
