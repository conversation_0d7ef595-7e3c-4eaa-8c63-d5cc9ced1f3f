package com.iqiyi.vip.enums;

import lombok.Getter;

/**
 * sku的giftType属性
 *
 * <AUTHOR>
 * @date 2023/10/27 17:24
 */
@Getter
public enum GiftTypeEnum {

    DIRECT_CHARGE(0, "权益直充"),

    /**
     * 对接虚拟赠品系统的接口发码
     */
    VIRTUAL_CARD(1, "兑换码"),


    IQ_SHOP_COUPON(3, "爱奇艺商城代金券"),

    /**
     * 按理不会生单，即便生单也不需要履约，因为页面会直接跳转到站外获取权益
     */
    PAGE_REDIRECT(4, "页面跳转类");


    private int type;
    private String desc;

    GiftTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
