package com.iqiyi.vip.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2020/2/27 16:16
 */
public enum AccountTypeEnum {

    UID(0, "uid"),
    MOBILE(1, "手机号"),
    THIRD_AUTH_TOKEN(2, "合作方授权token"),
    INPUT_USER_ACCOUNT(3, "传入型账号");

    @Getter
    private int type;
    @Getter
    private String desc;

    AccountTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static AccountTypeEnum of(Integer type) {
        if (null == type) {
            return null;
        }
        for (AccountTypeEnum accountTypeEnum : AccountTypeEnum.values()) {
            if (accountTypeEnum.getType() == type) {
                return accountTypeEnum;
            }
        }
        return null;
    }
}
