package com.iqiyi.vip.enums;

import lombok.Getter;
import org.apache.commons.lang.StringUtils;

/**
 * 代金券状态
 * http://wiki.qiyi.domain/pages/viewpage.action?pageId=24173489
 *
 * <AUTHOR>
 * @date 2023/5/12 14:57
 */
@Getter
public enum CouponStatusEnum {
    UN_ACTIVE("0", "未激活"),
    VALID_USE("1", "可使用"),
    FREEZE("2", "冻结"),
    USED("3", "已使用"),
    LOCK("4", "锁定"),
    DESTROYED("5", "已销毁");

    private String status;
    private String desc;

    CouponStatusEnum(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    /**
     * 已冻结|已使用|已锁定|已销毁 不可退
     *
     * @param value 值
     * @return 是否可退
     */
    public static boolean canRefundStatus(String value) {
        if (FREEZE.status.equals(value) ||
                USED.status.equals(value) ||
                LOCK.status.equals(value) ||
                DESTROYED.status.equals(value)) {
            return false;
        } else {
            return true;
        }
    }

    public static String ofDesc(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        for (CouponStatusEnum couponStatusEnum : values()) {
            if (couponStatusEnum.status.equals(value)) {
                return couponStatusEnum.desc;
            }
        }
        return null;
    }
}
