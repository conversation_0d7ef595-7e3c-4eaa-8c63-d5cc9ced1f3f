package com.iqiyi.vip.enums;

import lombok.Getter;

/**
 * 资产页领取状态枚举
 *
 * <AUTHOR> on 2019/1/1
 */
public enum ReceiveStatusEnum {

    /**
     *
     */
    EXPIRED(-1, "未领取、已过期"),
    PENDING_RECEIVE(0, "待领取"),
    RECEIVED(1, "已领取"),
    ;

    @Getter
    private int status;
    @Getter
    private String desc;

    ReceiveStatusEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static ReceiveStatusEnum of(int status) {
        for (ReceiveStatusEnum receiveStatusEnum : ReceiveStatusEnum
            .values()) {
            if (receiveStatusEnum.getStatus() == status) {
                return receiveStatusEnum;
            }
        }
        return null;
    }

    public static String ofDesc(int status) {
        for (ReceiveStatusEnum receiveStatusEnum : ReceiveStatusEnum
            .values()) {
            if (receiveStatusEnum.getStatus() == status) {
                return receiveStatusEnum.getDesc();
            }
        }
        return null;
    }
}
