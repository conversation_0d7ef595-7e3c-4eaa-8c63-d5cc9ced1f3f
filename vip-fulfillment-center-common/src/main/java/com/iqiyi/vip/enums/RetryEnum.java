package com.iqiyi.vip.enums;

import lombok.Getter;
import org.apache.commons.lang3.BooleanUtils;

/**
 * <AUTHOR>
 * @date 2023/8/30 17:38
 */
@Getter
public enum RetryEnum {

    YES(1, "重试"),
    NO(0, "无需重试");

    private int status;
    private String desc;

    RetryEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static boolean needRetry(Integer retryStatus) {
        if (null == retryStatus) {
            return BooleanUtils.toBoolean(YES.status);
        }
        for (RetryEnum retryEnum : values()) {
            if (retryEnum.getStatus() == retryStatus) {
                return BooleanUtils.toBoolean(retryEnum.status);
            }
        }
        return BooleanUtils.toBoolean(YES.status);
    }

    public boolean dealSuc() {
        //无需重试，即处理成功
        return this.status == NO.getStatus();
    }
}
