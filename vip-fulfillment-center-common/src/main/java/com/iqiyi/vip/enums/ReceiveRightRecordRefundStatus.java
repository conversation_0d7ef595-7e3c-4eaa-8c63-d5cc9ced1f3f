package com.iqiyi.vip.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/9/26 13:37
 */
@Getter
public enum ReceiveRightRecordRefundStatus {

    UN_REFUND(0, "未退单"),
    REFUND_SUC(1, "退单成功，权益回收成功"),
    REFUND_FAIL(2, "退单成功，权益回收失败，请重试"),
    REFUND_OFFLINE_SUC(3, "退单成功，权益未回收");//合作方未提供退单接口 or 已过最晚退单时间 等重试也无法回收权益的情况

    private int status;
    private String desc;

    ReceiveRightRecordRefundStatus(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static boolean isRefundSuc(Integer refundStatus) {
        if (null == refundStatus) {
            return false;
        }
        return REFUND_SUC.status == refundStatus || REFUND_OFFLINE_SUC.status == refundStatus;
    }

    /**
     * 是否已退
     */
    public static boolean isAlreadyRefund(Integer status) {
        if (null == status) {
            return false;
        }
        return UN_REFUND.getStatus() != status;
    }

    public static ReceiveRightRecordRefundStatus of(int status) {
        for (ReceiveRightRecordRefundStatus statusEnum : ReceiveRightRecordRefundStatus.values()) {
            if (statusEnum.getStatus() == status) {
                return statusEnum;
            }
        }
        return null;
    }

    public static String ofDesc(Integer status) {
        if (null == status) {
            return "";
        }
        for (ReceiveRightRecordRefundStatus statusEnum : ReceiveRightRecordRefundStatus.values()) {
            if (statusEnum.getStatus() == status) {
                return statusEnum.getDesc();
            }
        }
        return null;
    }
}
