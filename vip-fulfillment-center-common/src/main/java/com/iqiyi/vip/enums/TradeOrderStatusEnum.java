package com.iqiyi.vip.enums;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;

/**
 * 会员交易订单状态
 *
 * <AUTHOR> on 2018/3/7.
 */
public enum TradeOrderStatusEnum {

    PAID(1, "已支付"),
    PRE_PAID(10, "预支付，未开通权益"),
    PRE_PAID_REFUND(12, "预支付退单"),
    REFUND(6, "退单");

    //无论是否开通过权益，预付费的退单状态都是12，如果收回了权益订单的notifyResult字段的值是A00000

    @Getter
    private int status;
    @Getter
    private String desc;

    TradeOrderStatusEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    /**
     * 订单正单状态
     */
    public static List<Integer> paidStatusList() {
        List<Integer> paidStatusList = Lists.newArrayList();
        paidStatusList.add(PAID.status);
        paidStatusList.add(PRE_PAID.status);
        return paidStatusList;
    }

    public static boolean isPaidOrder(Integer status) {
        if (null == status) {
            return false;
        }
        return paidStatusList().contains(status);
    }

    /**
     * 订单退单状态
     */
    public static List<Integer> refundStatusList() {
        List<Integer> refundStatusList = Lists.newArrayList();
        refundStatusList.add(REFUND.status);
        refundStatusList.add(PRE_PAID_REFUND.status);
        return refundStatusList;
    }

    public static boolean isRefundStatus(Integer status) {
        if (null == status) {
            return false;
        }
        return refundStatusList().contains(status);
    }
}
