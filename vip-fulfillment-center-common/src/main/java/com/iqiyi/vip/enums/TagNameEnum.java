package com.iqiyi.vip.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/9/12 14:18
 */
@Getter
public enum TagNameEnum {

    PENDING_RECEIVE("availableExternalInterestsList", "待领取"),
    RECEIVED("acecptedExternalInterestsList", "已领取");

    private String tagName;
    private String desc;

    TagNameEnum(String tagName, String desc) {
        this.tagName = tagName;
        this.desc = desc;
    }
}
