package com.iqiyi.vip.enums;

import lombok.Getter;
import org.apache.commons.lang.StringUtils;

/**
 * 领取资格排序方式类型 枚举类
 *
 * <AUTHOR>
 * @date 2023/10/12 18:30
 */
@Getter
public enum ReceiveChanceOrderEnum {


    RECEIVE_TIME_ASC("rta", "领取时间正序"),
    RECEIVE_TIME_DESC("rtd", "领取时间倒序"),

    ORDER_TIME_ASC("ota", "订单时间正序"),
    ORDER_TIME_DESC("otd", "订单时间倒序"),

    INVALID_TIME_ASC("ita", "失效时间正序"),
    INVALID_TIME_DESC("itd", "失效时间倒序"),
    ;

    private String type;

    private String desc;

    ReceiveChanceOrderEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static ReceiveChanceOrderEnum of(String type) {
        for (ReceiveChanceOrderEnum receiveOrderEnum : ReceiveChanceOrderEnum.values()) {
            if (StringUtils.equals(receiveOrderEnum.getType(), type)) {
                return receiveOrderEnum;
            }
        }
        return null;
    }
}
