package com.iqiyi.vip.enums;

import lombok.Getter;

/**
 * giftCode枚举
 *
 * <AUTHOR>
 * @date 2023/5/15 16:29
 */
@Getter
public enum GiftCodeEnum {

    VIP_GIFT_CODE("vipCall", "会员品类商品调用固定giftCode"),
    VOD_GIFT_CODE("vodCall", "单点类商品调用固定giftCode"),
    PACKAGE_GIFT_CODE("packageCall", "套餐类商品调用固定giftCode"),
    COUPON_GIFT_CODE("couponCall", "代金券类商品调用固定giftCode"),
    SHOP_GIFT_CODE("shopCall", "实物商品调用固定giftCode");

    private String giftCode;
    private String desc;

    GiftCodeEnum(String giftCode, String desc) {
        this.giftCode = giftCode;
        this.desc = desc;
    }

}
