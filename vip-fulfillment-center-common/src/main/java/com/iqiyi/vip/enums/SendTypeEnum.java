package com.iqiyi.vip.enums;

import lombok.Getter;

/**
 * 发放类型
 */
@Getter
public enum SendTypeEnum {

    /**
     * 发送类型
     */
    DIRECT_SEND(0, "自动发放型"),
    MANUAL_RECEIVE(1, "手动领取型"),
    DIRECT_FAIL_THEN_MANUAL_RECEIVE(2, "自动发放失败后转为手动领取型");

    private int type;
    private String desc;

    SendTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static SendTypeEnum of(Integer type) {
        if (null == type) {
            //若商品无发放方式，默认按照直接发放型处理
            return DIRECT_SEND;
        }
        for (SendTypeEnum sendTypeEnum : SendTypeEnum.values()) {
            if (sendTypeEnum.type == type) {
                return sendTypeEnum;
            }
        }
        return null;
    }

    public static String ofDesc(Integer type) {
        if (null == type) {
            return null;
        }
        for (SendTypeEnum sendTypeEnum : SendTypeEnum.values()) {
            if (sendTypeEnum.type == type) {
                return sendTypeEnum.getDesc();
            }
        }
        return null;
    }
}
