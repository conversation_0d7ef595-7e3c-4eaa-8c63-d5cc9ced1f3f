package com.iqiyi.vip.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * VMC接口返回的错误码
 *
 * <AUTHOR>
 * @date 2024/10/29 10:04
 */
@Getter
public enum VmcCodeEnum {

    SUCCESS("A00000", "success"),
    ILLEGAL_ORDER("A00001", "非法订单"),
    SIGN_ERROR("Q00301", "签名错误"),
    PARAM_ERROR("Q00352", "参数错误"),
    SYSTEM_ERROR("Q00332", "系统错误"),
    REPEAT_ORDER("Q00302", "并发重复订单"),
    RETRY_ERROR("Q00300", "各种重试提示信息"),
    LIMIT_ERROR("300", "限流"),
    CONTENT_ID_ERROR("Q00402", "不合法的contentId"),
    PAID_TIME_ERROR("Q00403", "不合法的已扣除时间"),
    REFUND_TYPE_ERROR("Q00404", "不合法的退单类型"),
    ORDER_PAID_STATUS_ERROR("Q00405", "不合法的付费状态"),
    ORDER_STATUS_ERROR("Q00406", "不合法的订单状态"),
    REFUND_ORDER_NO_ERROR("Q00407", "退单没有原单号"),
    ORDER_TYPE_ERROR("Q00408", "订购类型必须为预付费"),
    REFUND_ORDER_ERROR("Q00409", "退单接口不支持正单"),
    PAID_TIME_OVER_ERROR("Q00410", "已扣除时间超过原单权益时间"),
    TRANSFER_TIME_OVER_ERROR("Q00411", "权益转赠时间超过当前时间"),
    TRANSFER_DURATION_OVER_ERROR("Q00412", "权益转赠时长不能超过用户权益时长");

    private String code;
    private String message;

    VmcCodeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public static CodeEnum convertToCodeEnum(String code) {
        if (StringUtils.isBlank(code)) {
            return CodeEnum.ERROR_SYSTEM;
        }
        if (SUCCESS.code.equals(code)) {
            return CodeEnum.SUCCESS;
        }
        return CodeEnum.ERROR_SYSTEM;
    }
}
