package com.iqiyi.vip.enums;

import lombok.Getter;

public enum ReceiveDeadlineTypeEnum {
    /**
     * 领取限制
     */
    NO_LIMIT(0, "不限制"),
    RELATIVE_LIMIT(1, "相对时间限制"),
    ABSOLUTE_LIMIT(2, "绝对时间限制");

    @Getter
    private int type;
    @Getter
    private String desc;

    ReceiveDeadlineTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static Integer getReceiveDeadlineType(Integer receiveDeadlineRelativeDays) {
        if (null != receiveDeadlineRelativeDays && receiveDeadlineRelativeDays > 0) {
            return RELATIVE_LIMIT.getType();
        }
        return ABSOLUTE_LIMIT.getType();
    }

    public static ReceiveDeadlineTypeEnum getReceiveDeadlineTypeEnum(Integer receiveDeadlineRelativeDays) {
        if (null != receiveDeadlineRelativeDays && receiveDeadlineRelativeDays > 0) {
            return RELATIVE_LIMIT;
        }
        return ABSOLUTE_LIMIT;
    }

    public static ReceiveDeadlineTypeEnum of(Integer type) {
        if (null == type) {
            return null;
        }
        for (ReceiveDeadlineTypeEnum receiveDeadlineTypeEnum : ReceiveDeadlineTypeEnum.values()) {
            if (receiveDeadlineTypeEnum.type == type) {
                return receiveDeadlineTypeEnum;
            }
        }
        return null;
    }
}
