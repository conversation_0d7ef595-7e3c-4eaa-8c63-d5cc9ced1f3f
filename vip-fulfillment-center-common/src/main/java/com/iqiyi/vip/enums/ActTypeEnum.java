package com.iqiyi.vip.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/8/2 21:15
 */
@Getter
public enum ActTypeEnum {

    SKU(8, "sku订单筛选条件组"),
    CASHIER(0, "收银台买赠", 12),
    ADDITIONAL(2, "加价购", 10);

    private int type;
    private String desc;
    /**
     * 活动中心的type
     */
    private Integer activityCenterActType;

    ActTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    ActTypeEnum(int type, String desc, Integer activityCenterActType) {
        this.type = type;
        this.desc = desc;
        this.activityCenterActType = activityCenterActType;
    }

    public static Integer getTypeByActCenterType(Integer activityCenterActType) {
        if (null == activityCenterActType) {
            return null;
        }
        for (ActTypeEnum actTypeEnum : values()) {
            if (null != actTypeEnum.getActivityCenterActType() && activityCenterActType.equals(actTypeEnum.getActivityCenterActType())) {
                return actTypeEnum.getType();
            }
        }
        return null;
    }
}
