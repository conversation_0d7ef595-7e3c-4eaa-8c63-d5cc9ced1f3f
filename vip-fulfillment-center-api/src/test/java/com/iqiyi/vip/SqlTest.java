package com.iqiyi.vip;

import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.junit.Test;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/7/29 18:12
 */
public class SqlTest {

    @org.junit.Test
    public void genCreateTableSql() {
        String originalSql = "CREATE TABLE `rights_record` (\n"
            + "  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',\n"
            + "  `order_code` varchar(64) DEFAULT '' COMMENT '会员交易系统订单号',\n"
            + "  `refund_code` varchar(64) DEFAULT '' COMMENT '会员交易系统该order_code对应的退单号',\n"
            + "  `present_order_code` varchar(64) DEFAULT NULL COMMENT '赠送方订单号',\n"
            + "  `uid` bigint(20) DEFAULT NULL COMMENT '用户ID',\n"
            + "  `mobile` varchar(64) DEFAULT NULL COMMENT '手机号',\n"
            + "  `encrypt_mobile` varchar(128) DEFAULT NULL COMMENT '加密手机号',\n"
            + "  `account` varchar(64) DEFAULT NULL COMMENT '通知给合作方的用户账户',\n"
            + "  `encrypt_account` varchar(256) DEFAULT NULL COMMENT '加密通知给合作方的用户账户',\n"
            + "  `promotion_code` varchar(32) DEFAULT NULL COMMENT '售卖对象code',\n"
            + "  `sku_id` varchar(64) DEFAULT NULL COMMENT '商品skuId',\n"
            + "  `act_code` varchar(64) DEFAULT NULL COMMENT '订单活动编码',\n"
            + "  `activity_code` varchar(32) DEFAULT NULL COMMENT '活动code',\n"
            + "  `rule_code` varchar(32) DEFAULT NULL COMMENT '规则code',\n"
            + "  `condition_group_id` bigint(19) DEFAULT NULL COMMENT '筛选条件组id',\n"
            + "  `present_no` varchar(32) DEFAULT NULL COMMENT '赠送方编码',\n"
            + "  `act_type` int(11) DEFAULT NULL COMMENT '活动类型 0:收银台买赠 1:联名购 2:加价购 3:营销活动类',\n"
            + "  `partner_no` varchar(255) DEFAULT NULL COMMENT '合作方编号',\n"
            + "  `receive_type` int(11) DEFAULT NULL COMMENT '领取类型 0:直接发放 1:手动领取',\n"
            + "  `send_type` int(11) DEFAULT NULL COMMENT '发放类型 0:回调接口 1:发放激活码',\n"
            + "  `send_status` int(11) DEFAULT NULL COMMENT '同步状态(1:未同步,2:同步成功,3:同步失败,不需重试,4:同步失败,需重试,5:无需通知)',\n"
            + "  `receive_status` int(11) DEFAULT NULL COMMENT '领取状态 0:未领取 1:已领取',\n"
            + "  `refund_status` int(11) DEFAULT NULL COMMENT '退权益状态 0:未退  1:退单成功  2:退单失败  3:线下退单成功',\n"
            + "  `call_back_status` int(11) DEFAULT NULL COMMENT '回调交易系统 0:未调用 1:调用成功',\n"
            + "  `partner_vip_used` int(11) DEFAULT '0' COMMENT '合作方会员使用状态，0:未使用，1:已使用 （仅回调了接口的合作方会使用）',\n"
            + "  `settlement_price` bigint(13) DEFAULT '0' COMMENT '第三方结算价(默认为0)',\n"
            + "  `settlement_percent` int(10) DEFAULT NULL COMMENT '结算比例 百分比分子数',\n"
            + "  `refund_settlement_price` bigint(13) DEFAULT '0' COMMENT '退单结算价(默认为0)',\n"
            + "  `order_time` timestamp NULL DEFAULT NULL COMMENT '购买时间',\n"
            + "  `send_time` timestamp NULL DEFAULT NULL COMMENT '发送时间',\n"
            + "  `receive_deadline_time` datetime DEFAULT NULL COMMENT '领取截止时间',\n"
            + "  `receive_time` timestamp NULL DEFAULT NULL COMMENT '领取时间',\n"
            + "  `refund_time` timestamp NULL DEFAULT NULL COMMENT '退权益时间',\n"
            + "  `send_res` varchar(1024) DEFAULT NULL COMMENT '合作方返回结果',\n"
            + "  `refund_res` varchar(1024) DEFAULT NULL COMMENT '退单返回结果',\n"
            + "  `iq_refund_time` timestamp NULL DEFAULT NULL COMMENT '爱奇艺退权益时间',\n"
            + "  `type` int(11) DEFAULT '1' COMMENT '0:测试 1:线上',\n"
            + "  `refund_msg` varchar(512) DEFAULT NULL COMMENT '退单描述',\n"
            + "  `card_code` varchar(128) DEFAULT NULL COMMENT '激活码（当发激活码类型时，该字段有值）',\n"
            + "  `remark` varchar(512) DEFAULT NULL COMMENT '扩展信息',\n"
            + "  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n"
            + "  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',\n"
            + "  `amount` int(10) DEFAULT NULL COMMENT '赠送的权益数量',\n"
            + "  `refund_amount` int(10) DEFAULT NULL COMMENT '退费退的权益数量',\n"
            + "  `trade_code` varchar(50) DEFAULT NULL COMMENT '业务流水号 ',\n"
            + "  `coupon_code` varchar(128) DEFAULT NULL COMMENT '券码',\n"
            + "  PRIMARY KEY (`id`),\n"
            + "  UNIQUE KEY `u_order_promotion_code` (`order_code`,`promotion_code`) USING BTREE,\n"
            + "  KEY `I_order_time` (`order_time`) COMMENT '支付时间索引',\n"
            + "  KEY `I_uid` (`uid`) USING BTREE COMMENT 'uid索引',\n"
            + "  KEY `I_update_time` (`update_time`) USING BTREE COMMENT 'updateTime索引',\n"
            + "  KEY `idx_promotion_code` (`promotion_code`) USING BTREE\n"
            + ") ENGINE=InnoDB  DEFAULT CHARSET=utf8 COMMENT='权益记录表';";

        testGenSql(originalSql);
    }

    @org.junit.Test
    public void genDropTableSql() {
        String originalSql = "DROP TABLE IF EXISTS `rights_record`;";
        testGenSql(originalSql);
    }

    @org.junit.Test
    public void genTruncateTableSql() {
        String originalSql = "TRUNCATE `rights_record`;";
        testGenSql(originalSql);
    }

    @org.junit.Test
    public void genSelectUnionTableSql() {
//        String orderCode = "202407301717457000450054110001";
//        String originalSql = "select * from `partner_rights_receive_record` where order_code = '"+ orderCode +"' union all";
//        testGenSql(originalSql);

        String originalSql = "select * from `rights_record`  union all";
        testGenSql(originalSql);
    }

    @org.junit.Test
    public void genCountUnionTableSql() {
        StringBuffer allSql = new StringBuffer();
        for (int i = 0; i < 100; i++) {
            String logicTableName = "rights_record";
            String newTableName = String.format("%s_%02d", logicTableName, i);
            String sql = "SELECT '" + newTableName + "',COUNT(*) FROM " + newTableName + " union all ";
            allSql.append(sql).append("\n\n");
        }
        System.out.println(allSql);
    }

    @org.junit.Test
    public void count() {
        String originalSql = "select count(*) AS record_count from `partner_rights_receive_record`  union all";
        testGenSql(originalSql);
    }

    public void testGenSql(String originalSql) {
        StringBuffer allSql = new StringBuffer();
        for (int i = 0; i < 100; i++) {
            String logicTableName = "rights_record";
            String newSql = originalSql.replaceAll(logicTableName, String.format("%s_%02d", logicTableName, i));
            allSql.append(newSql).append("\n\n");
        }
        System.out.println(allSql);
    }

    @Test
    public void queryWhichDataBaseTable() {
        String sql1 = "DELETE FROM rights_record WHERE order_code = '20230414133010194158003501' and uid = 2315108355 and receive_status = 0 and sku_id is null;";
        String sql2 = "DELETE FROM rights_record WHERE order_code = '20221231221050302534002925' and uid = 1320749357 and receive_status = 0 and sku_id is null;";
        String sql3 = "DELETE FROM rights_record WHERE order_code = '20230106211536674307002978' and uid = 1260985743 and receive_status = 0 and sku_id is null;";
        String sql4 = "DELETE FROM rights_record WHERE  order_code = '20230121120302101449003116' and uid = 2245268309 and receive_status = 0 and sku_id is null;";
        String sql5 = "DELETE FROM rights_record WHERE order_code = '20230104223858444923002929' and uid = 1237270179 and receive_status = 0 and sku_id is null;";
        String sql6 = "DELETE FROM rights_record WHERE order_code = '20230120153451707879003115' and uid = 2245268309 and receive_status = 0 and sku_id is null;";
        List<String> sqlList = Lists.newArrayList();
        sqlList.add(sql1);
        sqlList.add(sql2);
        sqlList.add(sql3);
        sqlList.add(sql4);
        sqlList.add(sql5);
        sqlList.add(sql6);
        queryWhichDataBaseTable(sqlList);
    }

    public void queryWhichDataBaseTable(List<String> inputSqlList) {
        Map<String, List<String>> dataBase2SqlList = Maps.newHashMap();
        for (String inputSql : inputSqlList) {
            String dataBaseName2Sql = queryWhichDataBaseTable(inputSql);
            String dataBase = dataBaseName2Sql.split("库")[0];
            String sql = dataBaseName2Sql.split("库")[1];

            List<String> sqlList = dataBase2SqlList.get(dataBase);
            if (CollectionUtils.isEmpty(sqlList)) {
                sqlList = Lists.newArrayList();
            }
            sqlList.add(sql);
            dataBase2SqlList.put(dataBase, sqlList);
        }
        for (Map.Entry<String, List<String>> entry : dataBase2SqlList.entrySet()) {
            System.out.println(entry.getKey() + "库：");
            for (String sql : entry.getValue()) {
                System.out.println(sql);
            }
            System.out.println("");
        }
    }

    public String queryWhichDataBaseTable(String sql) {
        Long uid = Long.valueOf(sql.split("uid = '")[1].split("'")[0]);
        String dataBaseName = uid % 16 + "库";
//        System.out.println(dataBaseName);

        Long tableSuffix = uid / 100 % 100;
        String sqlTable = String.format("%s_%02d", "rights_record", tableSuffix);
//        System.out.println(sqlTable);

        String finalSql = sql.replaceAll("rights_record", sqlTable);
//        System.out.println(dataBaseName + "  " + finalSql);
        return dataBaseName + finalSql;
    }

    @org.junit.Test
    public void genAlterSql() {
        String originalSql = "ALTER TABLE `rights_record`\n"
            + "MODIFY COLUMN `remark` varchar(2048) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '扩展信息' AFTER `card_code`;";
        testGenSql(originalSql);
    }

}
