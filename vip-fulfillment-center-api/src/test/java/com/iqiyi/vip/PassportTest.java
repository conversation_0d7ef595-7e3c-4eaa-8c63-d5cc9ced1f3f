package com.iqiyi.vip;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;

import com.iqiyi.vip.domain.passport.service.PassportService;

/**
 * <AUTHOR>
 * @date 2023/11/29 15:07
 */
@Slf4j
public class PassportTest extends BaseTest {

    @Resource
    private PassportService passportService;

    @Test
    public void testQueryPhone() {
        Long uid = 1155359239L;
        String phone = passportService.queryBindPhone(uid);
        log.info("[phone:{}]", phone);
    }
}
