package com.iqiyi.vip;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;

import com.iqiyi.vip.domain.rms.repository.RmsRepository;
import com.iqiyi.vip.domain.rms.service.RmsService;

/**
 * <AUTHOR>
 * @date 2023/10/20 17:26
 */
@Slf4j
public class RmsTest extends BaseTest {

    @Resource
    private RmsRepository rmsRepository;
    @Resource
    private RmsService rmsService;

    @Test
    public void testQueryRms() {
//        String skuIds = rmsRepository.queryAssetRmsSkuIdList();
//        log.info("skuIds:{}", skuIds);

        rmsService.queryAssetRMSSkuIdList();
    }
}
