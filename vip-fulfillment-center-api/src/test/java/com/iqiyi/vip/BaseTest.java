package com.iqiyi.vip;

import lombok.extern.slf4j.Slf4j;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.actuate.metrics.AutoConfigureMetrics;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2023/8/3 10:41
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@AutoConfigureMetrics
@Slf4j
public class BaseTest {

    static {
        System.setProperty("spring.profiles.active", "test");
        System.setProperty("env", "test");
        System.setProperty("app.id", "vip-fulfillment-center");
        System.setProperty("HOST", "127.0.0.1");
        System.setProperty("PORT_8080", "8080");
    }

}
