package com.iqiyi.vip;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.actuate.metrics.AutoConfigureMetrics;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Map;

import com.qiyi.vip.trade.dataservice.client.dto.OrderDto;
import com.iqiyi.vip.domain.order.repository.OrderRepository;
import com.iqiyi.vip.domain.refund.RefundRepository;
import com.iqiyi.vip.domain.refund.entity.RetreatResult;
import com.iqiyi.vip.domain.sku.entity.Sku;
import com.iqiyi.vip.domain.sku.repository.SkuRepository;
import com.iqiyi.vip.dto.http.HttpResDTO;
import com.iqiyi.vip.dto.rights.OrderDeliverReq;
import com.iqiyi.vip.dto.sku.SkuByAttrQry;
import com.iqiyi.vip.dto.sku.SkuByAttrQry.CommonAttrQueryParam;
import com.iqiyi.vip.utils.DateUtils;

/**
 * @author: linpeihui
 * @createTime: 2025/03/03
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@AutoConfigureMetrics
@Slf4j
public class ProdQsmTest extends BaseTest {
    static {
        System.setProperty("spring.profiles.active", "test");
        System.setProperty("env", "test");
        System.setProperty("eureka.client.register-with-eureka", "false");
        System.setProperty("app.id", "vip-fulfillment-center");
        System.setProperty("hystrix.command.default.execution.timeout.enabled", "false");
        System.setProperty("HOST", "127.0.0.1");
        System.setProperty("PORT_8080", "8080");
    }

    @Resource
    private OrderRepository orderRepository;
    @Resource
    private RefundRepository refundRepository;
    @Resource
    private SkuRepository skuRepository;

    @Test
    public void testDeliver() {
        OrderDeliverReq req = new OrderDeliverReq();
        Map refer = Maps.newHashMap();
        refer.put("beforeDeadline", "2029-05-15 23:59:59");
        refer.put("beforePaidSign", 1);
        req.setRefer(refer);
        req.setOrderCode("202503031730412248880144640003");
        req.setStartTime(DateUtils.getTimestamp("2029-05-15 23:59:59"));
        req.setDeadline(DateUtils.getTimestamp("2029-06-15 23:59:59"));
        req.setRenewalsFlag(1);
        HttpResDTO resDTO = orderRepository.deliver(req);
        Assert.assertTrue(resDTO.suc());
    }

    @Test
    public void testQueryByOrderCode() {
        OrderDto orderDto = orderRepository.queryByOrderCode("202503031730412248880144640003");
        Assert.assertNotNull(orderDto);
    }

    @Test
    public void testQueryByTradeCode() {
        OrderDto orderDto = orderRepository.queryByTradeCode("4200002557202503032509568958");
        Assert.assertNotNull(orderDto);
    }

    @Test
    public void testCancel() {
        Boolean result = orderRepository.cancel("202503040926180508580147390001");
        Assert.assertTrue(result);
    }

    @Test
    public void testRefund() {
        RetreatResult result = refundRepository.retreatLowerRight("123");
        Assert.assertTrue(result.needCancel());
    }

    @Test
    public void testAdminSkuQuery() {
        SkuByAttrQry req = new SkuByAttrQry();
        CommonAttrQueryParam skuId = new CommonAttrQueryParam();
        skuId.setValue("sku_532590139460024332");
        req.setSkuId(skuId);
        HttpResDTO<Sku> result = skuRepository.queryByAttribute(req);
        Assert.assertNotNull(result.getDataList());
    }

}
