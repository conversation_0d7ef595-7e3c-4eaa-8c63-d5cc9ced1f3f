package com.iqiyi.vip;

import com.iqiyi.vip.domain.sku.entity.Sku;
import com.iqiyi.vip.domain.sku.repository.SkuRepository;
import com.iqiyi.vip.domain.sku.service.SkuService;
import com.iqiyi.vip.dto.sku.*;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.utils.AssertUtils;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/11/28 17:20
 */
@Slf4j
public class SkuTest extends BaseTest {

    @Resource
    private SkuRepository skuRepository;

    @Resource
    private SkuService skuService;

    @Test
    public void testQuery() {
        SkuBatchQuery skuQuery = new SkuBatchQuery();
        skuQuery.setSkuIds("sku_230649089890657346,sku_214441746177923166");
        skuQuery.setOnlyQueryValid(false);
        skuRepository.batchQuery(skuQuery);
    }

    @Test
    public void testBeforeBuyCheck() {
        SkuBeforeBuyCheckReq req = new SkuBeforeBuyCheckReq();
        req.setUid(1480441855L);
        req.setCheckBindPhone(0);
        SkuCheckReq skuCheckReq = new SkuCheckReq();
        skuCheckReq.setSkuId("sku_407898914460738562");
        skuCheckReq.setSkuAmount(2);
        req.setSkuCheckList(Lists.newArrayList(skuCheckReq));
        SkuBeforeBuyCheckRes res = new SkuBeforeBuyCheckRes(skuService.beforeBuyCheck(req));
        log.info("res:{}", res);
    }

    @Test
    public void testQueryFromLocalCache() {

        SkuQuery skuQuery = new SkuQuery();
        skuQuery.setSkuId("sku_230649089890657346");
        skuQuery.setOnlyQueryValid(false);
        Sku sku = skuRepository.queryFromCache(skuQuery);
        AssertUtils.notNull(sku, CodeEnum.ERROR_PARAM);

        Sku sku2 = skuRepository.queryFromCache(skuQuery);
        AssertUtils.notNull(sku2, CodeEnum.ERROR_PARAM);

        SkuQuery skuQuery3 = new SkuQuery();
        skuQuery3.setSkuId("sku_214441746177923166");
        skuQuery3.setOnlyQueryValid(false);
        Sku sku3 = skuRepository.queryFromCache(skuQuery3);
        AssertUtils.notNull(sku3, CodeEnum.ERROR_PARAM);
    }

}
