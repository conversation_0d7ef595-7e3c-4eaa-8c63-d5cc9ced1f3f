package com.iqiyi.vip;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;

import com.iqiyi.vip.domain.outgateway.repository.OutGatewayRepository;

/**
 * <AUTHOR>
 * @date 2023/12/19 13:33
 */
@Slf4j
public class OutGatewayTest extends BaseTest {

    @Resource
    private OutGatewayRepository outGatewayRepository;

    @Test
    public void testQueryUrl() {
        String outUrl = "http://g.vip.qiyi.domain/4sKGKHJrAvFaAt8ly6NTr4/partner/iqiyi/directRecharge?x-token=3674d09968919b63";
        String ol = outGatewayRepository.getOriginalUrl(outUrl);
        log.info("ol:{}", ol);
    }

}
