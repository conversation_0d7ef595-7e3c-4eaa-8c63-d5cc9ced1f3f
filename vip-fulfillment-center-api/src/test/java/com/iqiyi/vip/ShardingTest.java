package com.iqiyi.vip;

import com.google.common.collect.Maps;
import org.assertj.core.util.Lists;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import java.util.zip.CRC32;

/**
 * <AUTHOR>
 * @date 2024/8/14 9:52
 */
public class ShardingTest {

    @org.junit.Test
    public void shardingData() {
//        Long startUid = 0L;
        Long startUid = Long.valueOf(99999999L);
        Long endUid = Long.valueOf(999999999L);
//        Long endUid = Long.valueOf(Long.MAX_VALUE);
//        Long endUid = Long.valueOf(9999L);
        Map<String, Integer> key2Count = Maps.newHashMap();
        for (long uid = startUid; uid < endUid; uid++) {
            String key = dataBaseSuffix(uid) + "_" + tableSuffixDiv100MOD100(uid);
            Integer count = key2Count.get(key);
            if (null == count) {
                count = 0;
            }
//            System.out.println("uid:" + uid + "存储在" + key + "中");
            key2Count.put(key, count + 1);
        }

        System.out.println("uid:" + startUid + "~" + endUid + "存储分布(按存储数量由大到小输出)：");

        // 使用Stream API按照value的降序对Map的entry进行排序
        List<Entry<String, Integer>> sortedEntries = key2Count.entrySet()
            .stream()
            .sorted(Entry.<String, Integer>comparingByValue().reversed())
            .collect(Collectors.toList());

        // 输出排序后的结果
        for (Entry<String, Integer> entry : sortedEntries) {
            System.out.println(entry.getKey() + "存储的uid数据量 " + entry.getValue());
        }

        List<String> allDataBaseTable = Lists.newArrayList();
        for (int dataBase = 0; dataBase < 16; dataBase++) {
            String databaseName = String.format("%s%d", "ds", dataBase);
            for (int table = 0; table < 100; table++) {
                String tableName = String.format("%s_%02d", "rights_record", table);
                allDataBaseTable.add(databaseName + "_" + tableName);
            }
        }

        System.out.println("\n\n其中以下数据源表是空的：");
        for (String dataBaseTable : allDataBaseTable) {
            if (!key2Count.containsKey(dataBaseTable)) {
                System.out.println(dataBaseTable);
            }
        }

        System.out.println(key2Count.get("ds8_rights_record_87"));
    }

    public static String tableSuffix(Long uid) {
        CRC32 crc32 = new CRC32();
        crc32.update(String.valueOf(uid).getBytes(StandardCharsets.UTF_8));
        Long tableSuffix = crc32.getValue() % 100;
        return String.format("%s_%02d", "rights_record", tableSuffix);
    }

    public static String tableSuffixUidMod100(Long uid) {
        Long tableSuffix = uid % 100;
        return String.format("%s_%02d", "rights_record", tableSuffix);
    }

    public static String tableSuffix6UidMod100(Long uid) {
        Long tableSuffix = uid / 6 % 100;
        return String.format("%s_%02d", "rights_record", tableSuffix);
    }

    public static String tableSuffix34(Long uid) {
        String str = String.valueOf(uid);
        int length = str.length();
        String tableSuffix;
        // 验证字符串长度是否足够
        if (length >= 4) {
            // substring的起始索引是length - 4，结束索引是length - 2，因为substring不包括结束索引处的字符
            tableSuffix = str.substring(length - 4, length - 2);
        } else if (length >= 2) {
            tableSuffix = str.substring(length - 2, length);
        } else {
            tableSuffix = str;
        }
        return String.format("%s_%02d", "rights_record", Long.valueOf(tableSuffix));
    }

    public static String tableSuffixDiv100MOD100(Long uid) {
        Long tableSuffix = uid / 100 % 100;
        return String.format("%s_%02d", "rights_record", tableSuffix);
    }

    public static String dataBaseSuffix(Long uid) {
        Long dataSourceSuffix = uid % 16;
        return String.format("%s%d", "ds", dataSourceSuffix);
    }
}
