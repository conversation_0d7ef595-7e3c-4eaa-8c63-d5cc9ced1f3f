package com.iqiyi.vip;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

import com.iqiyi.vip.domain.inventory.repository.InventoryRepository;
import com.iqiyi.vip.dto.stock.DeductStockReq;
import com.iqiyi.vip.dto.stock.QueryStockReq;
import com.iqiyi.vip.dto.stock.StockQueryRes;

/**
 * <AUTHOR>
 * @date 2023/11/27 18:36
 */
@Slf4j
public class StockTest extends BaseTest {

    @Resource
    private InventoryRepository inventoryRepository;


    @Test
    public void testDeduct() {
        DeductStockReq deductStockReq = new DeductStockReq();
        deductStockReq.setOrderId("tll_20231127_" + System.currentTimeMillis());
        deductStockReq.setInventoryNum(1);
        deductStockReq.setSkuId("sku_407582881711596545");
        inventoryRepository.deductStock(deductStockReq);
    }

    @Test
    public void testQuery() {
        QueryStockReq req1 = new QueryStockReq();
        req1.setSkuId("sku_393066595916910671");

        QueryStockReq req2 = new QueryStockReq();
        req2.setSkuId("sku_399218705679192064");
        List<StockQueryRes> list = inventoryRepository.queryStockBatch(Lists.newArrayList(req1, req2));
        log.info("[list:{}]", JSON.toJSONString(list));
    }

    @Test
    public void testDeductStock() {
        DeductStockReq deductStockReq = new DeductStockReq();
        deductStockReq.setOrderId("tll_20231127_" + System.currentTimeMillis());
        deductStockReq.setSkuId("sku_399218705679192064");
        inventoryRepository.deductStock(deductStockReq);
    }
}
