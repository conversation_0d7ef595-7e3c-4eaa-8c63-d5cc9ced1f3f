package com.iqiyi.vip;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.assertj.core.util.Lists;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import com.iqiyi.vip.domain.rights.entity.ReceiveRightRecord;
import com.iqiyi.vip.dto.rights.BatchDeliverOrderReq;
import com.iqiyi.vip.dto.rights.RecordByOrderCodeSkuIdQry;
import com.iqiyi.vip.dto.sku.SkuAttrQueryParam;
import com.iqiyi.vip.dto.sku.SkuByAttrQry;
import com.iqiyi.vip.dto.sku.SkuByAttrQry.CommonAttrQueryParam;
import com.iqiyi.vip.utils.DateUtils;

/**
 * <AUTHOR>
 * @date 2023/10/16 9:58
 */
@Slf4j
public class Test {

    @org.junit.Test
    public void test() {
        List<RecordByOrderCodeSkuIdQry> qryList = Lists.newArrayList();
        RecordByOrderCodeSkuIdQry qry = new RecordByOrderCodeSkuIdQry();
        qry.setUid(4133384322293760L);
        qry.setSkuId("sku_353486704699711514");
        qry.setOrderCode("20220719152614_202410301151");
        qryList.add(qry);

        BatchDeliverOrderReq req = new BatchDeliverOrderReq();
        req.setQryList(qryList);
        req.setOrderCodes("20241030052203500179006463");
        System.out.println(JSON.toJSONString(req));
    }

    @org.junit.Test
    public void testCompare() {
        List<ReceiveRightRecord> list = new ArrayList<>();

        ReceiveRightRecord r1 = new ReceiveRightRecord();
        r1.setId(3L);
        r1.setSkuId("aa");
        r1.setReceiveDeadlineTime(DateUtils.getDateFromStr("2023-11-10 00:00:00","yyyy-MM-dd HH:mm:ss"));
        list.add(r1);

        ReceiveRightRecord r2 = new ReceiveRightRecord();
        r2.setId(7L);
        r2.setSkuId("aa");
        r2.setReceiveDeadlineTime(DateUtils.getDateFromStr("2022-02-02 00:00:00","yyyy-MM-dd HH:mm:ss"));
        list.add(r2);

        ReceiveRightRecord r3 = new ReceiveRightRecord();
        r3.setId(99L);
        r3.setSkuId("aa");
        r3.setReceiveDeadlineTime(DateUtils.getDateFromStr("2023-03-03 00:00:00","yyyy-MM-dd HH:mm:ss"));
        list.add(r3);

        List<ReceiveRightRecord> list1 = list.stream()
            .collect(Collectors.groupingBy(ReceiveRightRecord::getSkuId))
            .values()
            .stream()
            .flatMap(e -> Arrays.asList(e.stream()
                    .sorted(Comparator.comparing(ReceiveRightRecord::getReceiveDeadlineTime))
                    .findFirst()
                    .get())
                .stream())
            .collect(Collectors.toList());

        log.info("list1:{}", list1);
    }

    @org.junit.Test
    public void testSKuAttr(){
        SkuByAttrQry skuByAttrQry = new SkuByAttrQry();
        skuByAttrQry.setSkuId(new CommonAttrQueryParam("sku_513349804882713665", true));
        skuByAttrQry.setSpuId(new CommonAttrQueryParam("spu_outside_goods", true));

        List<SkuAttrQueryParam> queryParamList = Lists.newArrayList();
        SkuAttrQueryParam queryParam = new SkuAttrQueryParam();
        queryParam.setAttrEnName("constraintSendType");
        queryParam.setAttrValue("0");
        queryParam.setAttrType(0);
        queryParam.setPrecise(true);
        queryParamList.add(queryParam);

        SkuAttrQueryParam queryParam2 = new SkuAttrQueryParam();
        queryParam2.setAttrEnName("chargeType");
        queryParam2.setAttrValue("3");
        queryParam2.setAttrType(0);
        queryParam2.setPrecise(true);
        queryParamList.add(queryParam2);

        skuByAttrQry.setQueryParamList(queryParamList);
        skuByAttrQry.setPageNo(1);
        skuByAttrQry.setPageSize(20);
        System.out.println(JSON.toJSONString(skuByAttrQry));
    }
}
