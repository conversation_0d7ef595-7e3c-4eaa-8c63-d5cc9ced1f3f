spring:
  application:
    name: vip-fulfillment-center-api-online
  shardingsphere:
    props:
      sql:
        show: false
    dataSource:
      names: tidb,old-ds,fulfill-ds0,fulfill-ds1,fulfill-ds2,fulfill-ds3,fulfill-ds4,fulfill-ds5,fulfill-ds6,fulfill-ds7,fulfill-ds8,fulfill-ds9,fulfill-ds10,fulfill-ds11,fulfill-ds12,fulfill-ds13,fulfill-ds14,fulfill-ds15
      tidb:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbcUrl: ************************************************************************************************************************************************************************************************************************
        username: partner_rights
        password: vOGQeFQDs@bTFLG5
        initial-size: 5
        max-active: 20
        max-wait: 60000
        min-evictable-idle-time-millis: 1800000
        min-idle: 5
        pool-prepared-statements: true
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        validation-query: SELECT 1
      old-ds:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbcUrl: *************************************************************************************************************************************************************************************************************************
        username: partner_rights
        password: eMc!8!9RKX
        initial-size: 5
        max-active: 20
        max-wait: 60000
        min-evictable-idle-time-millis: 1800000
        min-idle: 5
        pool-prepared-statements: true
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        validation-query: SELECT 1
      fulfill-ds0:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbcUrl: ************************************************************************************************************************************************************************************************************************************
        username: vip_fulfillment
        password: eMc!8!9RKX
        initial-size: 5
        max-active: 20
        max-wait: 60000
        min-evictable-idle-time-millis: 1800000
        min-idle: 5
        pool-prepared-statements: true
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        validation-query: SELECT 1
      fulfill-ds1:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbcUrl: ************************************************************************************************************************************************************************************************************************************
        username: vip_fulfillment
        password: eMc!8!9RKX
        initial-size: 5
        max-active: 20
        max-wait: 60000
        min-evictable-idle-time-millis: 1800000
        min-idle: 5
        pool-prepared-statements: true
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        validation-query: SELECT 1
      fulfill-ds2:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbcUrl: ************************************************************************************************************************************************************************************************************************************
        username: vip_fulfillment
        password: eMc!8!9RKX
        initial-size: 5
        max-active: 20
        max-wait: 60000
        min-evictable-idle-time-millis: 1800000
        min-idle: 5
        pool-prepared-statements: true
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        validation-query: SELECT 1
      fulfill-ds3:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbcUrl: ************************************************************************************************************************************************************************************************************************************
        username: vip_fulfillment
        password: eMc!8!9RKX
        initial-size: 5
        max-active: 20
        max-wait: 60000
        min-evictable-idle-time-millis: 1800000
        min-idle: 5
        pool-prepared-statements: true
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        validation-query: SELECT 1
      fulfill-ds4:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbcUrl: ************************************************************************************************************************************************************************************************************************************
        username: vip_fulfillment
        password: eMc!8!9RKX
        initial-size: 5
        max-active: 20
        max-wait: 60000
        min-evictable-idle-time-millis: 1800000
        min-idle: 5
        pool-prepared-statements: true
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        validation-query: SELECT 1
      fulfill-ds5:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbcUrl: ************************************************************************************************************************************************************************************************************************************
        username: vip_fulfillment
        password: eMc!8!9RKX
        initial-size: 5
        max-active: 20
        max-wait: 60000
        min-evictable-idle-time-millis: 1800000
        min-idle: 5
        pool-prepared-statements: true
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        validation-query: SELECT 1
      fulfill-ds6:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbcUrl: ************************************************************************************************************************************************************************************************************************************
        username: vip_fulfillment
        password: eMc!8!9RKX
        initial-size: 5
        max-active: 20
        max-wait: 60000
        min-evictable-idle-time-millis: 1800000
        min-idle: 5
        pool-prepared-statements: true
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        validation-query: SELECT 1
      fulfill-ds7:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbcUrl: ************************************************************************************************************************************************************************************************************************************
        username: vip_fulfillment
        password: eMc!8!9RKX
        initial-size: 5
        max-active: 20
        max-wait: 60000
        min-evictable-idle-time-millis: 1800000
        min-idle: 5
        pool-prepared-statements: true
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        validation-query: SELECT 1
      fulfill-ds8:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbcUrl: ************************************************************************************************************************************************************************************************************************************
        username: vip_fulfillment
        password: eMc!8!9RKX
        initial-size: 5
        max-active: 20
        max-wait: 60000
        min-evictable-idle-time-millis: 1800000
        min-idle: 5
        pool-prepared-statements: true
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        validation-query: SELECT 1
      fulfill-ds9:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbcUrl: ************************************************************************************************************************************************************************************************************************************
        username: vip_fulfillment
        password: eMc!8!9RKX
        initial-size: 5
        max-active: 20
        max-wait: 60000
        min-evictable-idle-time-millis: 1800000
        min-idle: 5
        pool-prepared-statements: true
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        validation-query: SELECT 1
      fulfill-ds10:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbcUrl: **************************************************************************************************************************************************************************************************************************************
        username: vip_fulfillment
        password: eMc!8!9RKX
        initial-size: 5
        max-active: 20
        max-wait: 60000
        min-evictable-idle-time-millis: 1800000
        min-idle: 5
        pool-prepared-statements: true
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        validation-query: SELECT 1
      fulfill-ds11:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbcUrl: **************************************************************************************************************************************************************************************************************************************
        username: vip_fulfillment
        password: eMc!8!9RKX
        initial-size: 5
        max-active: 20
        max-wait: 60000
        min-evictable-idle-time-millis: 1800000
        min-idle: 5
        pool-prepared-statements: true
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        validation-query: SELECT 1
      fulfill-ds12:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbcUrl: **************************************************************************************************************************************************************************************************************************************
        username: vip_fulfillment
        password: eMc!8!9RKX
        initial-size: 5
        max-active: 20
        max-wait: 60000
        min-evictable-idle-time-millis: 1800000
        min-idle: 5
        pool-prepared-statements: true
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        validation-query: SELECT 1
      fulfill-ds13:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbcUrl: **************************************************************************************************************************************************************************************************************************************
        username: vip_fulfillment
        password: eMc!8!9RKX
        initial-size: 5
        max-active: 20
        max-wait: 60000
        min-evictable-idle-time-millis: 1800000
        min-idle: 5
        pool-prepared-statements: true
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        validation-query: SELECT 1
      fulfill-ds14:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbcUrl: **************************************************************************************************************************************************************************************************************************************
        username: vip_fulfillment
        password: eMc!8!9RKX
        initial-size: 5
        max-active: 20
        max-wait: 60000
        min-evictable-idle-time-millis: 1800000
        min-idle: 5
        pool-prepared-statements: true
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        validation-query: SELECT 1
      fulfill-ds15:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbcUrl: **************************************************************************************************************************************************************************************************************************************
        username: vip_fulfillment
        password: eMc!8!9RKX
        initial-size: 5
        max-active: 20
        max-wait: 60000
        min-evictable-idle-time-millis: 1800000
        min-idle: 5
        pool-prepared-statements: true
        test-on-borrow: false
        test-on-return: false
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        validation-query: SELECT 1
    sharding:
      default-data-source-name: old-ds
      tables:
        partner_rights_receive_record_tidb:
          actual-data-nodes: tidb.partner_rights_receive_record
        rights_record:
          actual-data-nodes: fulfill-ds$->{0..15}.rights_record_0$->{0..9},fulfill-ds$->{0..15}.rights_record_$->{10..99}
          database-strategy:
            standard:
              sharding-column: uid
              precise-algorithm-class-name: com.iqiyi.vip.config.DataBaseShardingAlgorithm
          table-strategy:
            standard:
              sharding-column: uid
              precise-algorithm-class-name: com.iqiyi.vip.config.RightsRecordShardingAlgorithm

  cache:
    caffeine:
      spec: initialCapacity=100,maximumSize=1000,expireAfterWrite=1s
  redis:
    host: vippartnerrights.w.qiyi.redis
    port: 6874
    password: S1XlN07Qjbo2
    timeout: 1000
    testOnBorrow: true
    testOnReturn: true
    jedis:
      pool:
        max-active: 20
        max-idle: 50
        max-wait: 1000
        min-idle: 20

server:
  port: 8080


# qae eureka config
eureka:
  instance:
    hostname: ${HOST}
    port: ${PORT_8080}
    ip-address: ${HOST}
    non-secure-port: 8080
    instance-id: ${HOST}:${spring.application.name}:${eureka.instance.non-secure-port}
    prefer-ip-address: false
    lease-renewal-interval-in-seconds: 3
    lease-expiration-duration-in-seconds: 5
    initial-status: up
    metadata-map:
      zone: zone-pre
  client:
    availability-zones:
      region-bj: zone-bj,zone-zyx,zone-zjy
      region-hz: zone-hz
    healthcheck:
      enabled: false
    region: region-bj
    register-with-eureka: true
    service-url:
      zone-bj: http://pre-eureka.vip.qiyi.domain:8080/eureka
      zone-hz: http://pre-eureka.vip.qiyi.domain:8080/eureka
      zone-zjy: http://pre-eureka.vip.qiyi.domain:8080/eureka
      zone-zyx: http://pre-eureka.vip.qiyi.domain:8080/eureka
    registryFetchIntervalSeconds: 5
  server:
    enable-self-preservation: false
hystrix:
  metrics:
    enabled: false

ribbon:
  eureka:
    enabled: true
  eager-load:
    enabled: false
    clients: vip-info-server-online
  restclient:
    enabled: true
  ConnectTimeout: 3000
  ReadTimeout: 5000

vip:
  kms:
    access-key: 8C13781FBCCF6E6504B796F9621F7C3E
    decrypt-type: sm4-decrypt-withlv
    def-sk: 6FD9C40BA4EDBA7508E0B1FD8EE9B6D0E41B976493E118E19199AAE5E8FF826C737EBF41404BC7D729D2D56807744384
    encrypt-type: sm4-encrypt-withlv
    key-id: 30F0F049207A033F
    request:
      env: prod
    secret-key: 8A5D315F585C49C94F8CFD109278D2F2

async:
  task:
    execute: false
    execute.cluster: false

rocketmq:
  name-server: vip-partner-notification-rocketmq-online011-bdwg.qiyi.virtual:9876;vip-partner-notification-rocketmq-online013-bdwg.qiyi.virtual:9876
  producers[0]:
    topic: vip_partner_rights_draw_remind
    group: PG-vip-fulfillment-center_rights_remind
    token: ***********-9995-4f84-a028-a22cfd1593e8
  producers[1]:
    topic: FULFILLMENT_NOTIFY
    group: PG-vip-fulfillment-center_rights_notify
    token: PT-6e7320bf-5aad-4889-b5bf-6675a62a8996

sku:
  url:
    prefix: http://vip-commodity-center-online/vip-commodity
  http:
    url:
      prefix: http://vcc.vip.qiyi.domain/vip-commodity

query:
  order:
    signKey: eb7f978928f595036cefccbb1bad4696
    channel: partner_notify
    qsmUrl: http://pre-viptrade-dataservice.qsm.qiyi.middle

coupon:
  domain:
    url: http://coupons.qiyi.domain
  sign:
    key: 03c4e128fcb81a71e3b124a2d03663f6

partner:
  right:
    api:
      eureka:
        host: http://vip-partner-rights-api-online
      secretkey: a15f7aaa4f36768e
      sys: vip-fulfillment-center

myAsset:
  sku:
    rmsUrl: http://g.vip.qiyi.domain/4jlIWHWlmX0jcFZMfeu2lN/ext/rms/resources/165726177856441.json?x-token=sjSvtqM2YUlw8hcN

stock:
    api:
      signKey: yPSD8tq454qUH8uIgappYZDJkkD09TD

benefit:
  eureka:
    host: http://vip-benefit-api-online/internal/benefit

third:
  auth:
    api:
      eureka:
        host: http://VIP-THIRD-AUTH-API-ONLINE/thirdauth/
    sourceId: 10
    signKey: 632e70c924ca278a6718a5c953761b2c

refund:
  eureka:
    host: http://VIPTRADE-REFUNDSERVICE-API
  qsm:
    host: http://pre-viptrade-refundservice-api.qsm.qiyi.middle
  sign:
    key: NiFUGtBgeWZA7pZD

orderSys:
  qsm:
    host: http://pre-order-system.qsm.qiyi.middle

ruleEngine:
  eureka:
    url: http://vip-rule-engine-api-online/rule/api/v1/checkLimit
  http:
    url: http://act.vip.qiyi.domain/rule/api/v1/checkLimit
  key: fRRJK8HDFcBjrra2

freePay:
  key: 1df3ec6fa2

signKey: xY7z9pQ2rT4f