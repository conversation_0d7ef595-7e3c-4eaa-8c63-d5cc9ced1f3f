logging:
    config: classpath:logback-spring.xml

spring:
  profiles:
    active: test
  jackson:
    serialization:
      write-dates-as-timestamps: true
  cache:
    type: caffeine
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

mybatis:
  mapper-locations: classpath:mapper/*.xml,classpath:mapper/extend/*.xml,classpath:mapper/tidb/*.xml

rocketmq:
  producer:
    enabled: true

management:
  server:
    port: 8099
  endpoints:
    web:
      exposure:
        include: health,prometheus
v-eagle:
  request:
    publish-tp: true

log:
  home: /qke/log

vip:
  kms:
    cache: hutool-fifo
    cache-capacity: 200
    cache-timeout: 60000
    close-parameter-tampering: true
    refresh-time: 1800
    table-for-encrypt:
      rights_record:
        close-def-encrypt-decrypt: false
        table-column: encrypt_mobile,encrypt_account,coupon_code
    valid-period: 300

async:
  task:
    execute: false
    execute.cluster: false
    table:
      name: fulfillment_center_async_task

#优雅上下线相关配置
v-load:
  balance:
    props:
      least-server-count: 0 # 默认：3 机器数量大于3才会支持负载
      self-preservation-rate: 0.2 # 默认：0.6 机器数量下掉剩余60%的时候开启包含，不再允许下线机器
v:
  spring:
    cloud:
      service-registry:
        auto-registration:
          enabled: true # 服务是否向Eureka自动注册，该配置项将替换掉原生spring.cloud.service-registry.auto-registration.enabled配置项
          graceful-start-enabled: true # 优雅上线开关
          delay-registry-millis: 10000 # 延迟注册时间
          execute-warm-up-logic-times: 10 # 预热逻辑执行次数
        graceful-shutdown:
          enabled: true # 优雅下线开关，打开以下配置则生效
          exclude-path-patterns: /health,/*error*,/index # 优雅下线拦截器不拦截请求
          include-path-patterns: /** # 优雅下线拦截器拦截请求
          wait-timeout-millis: 20000 # 停机等待超时时间
eureka:
  client:
    registryFetchIntervalSeconds: 5 # eureka client刷新本地缓存时间,默认30s

# jetcache缓存配置
jetcache:
  penetrationProtect: true
  areaInCacheName: false
  statIntervalMinutes: 3
  hidePackages: com.alibaba
  local:
    default:
      type: caffeine
      keyConvertor: fastjson2
      limit: 10000
      expireAfterWriteInMillis: 1800000