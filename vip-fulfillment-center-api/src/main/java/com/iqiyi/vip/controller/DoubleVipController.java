package com.iqiyi.vip.controller;

import com.iqiyi.vip.aspect.SignatureRequired;
import com.iqiyi.vip.domain.doublevip.entity.DoubleVipReq;
import com.iqiyi.vip.domain.doublevip.service.DoubleVipService;
import com.qiyi.vip.commons.web.dto.WebResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/6/10 11:13
 */
@Controller
public class DoubleVipController {

    @Resource
    private DoubleVipService doubleVipService;

    @RequestMapping("/double/vip")
    @ResponseBody
    @SignatureRequired
    public WebResult giveDoubleVip(DoubleVipReq req) {
        return doubleVipService.giveDoubleVip(req);
    }

}
