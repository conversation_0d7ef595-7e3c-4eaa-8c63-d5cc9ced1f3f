package com.iqiyi.vip.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import com.iqiyi.vip.aspect.SignCheck;
import com.iqiyi.vip.aspect.WebLog;
import com.iqiyi.vip.domain.order.service.OrderService;
import com.iqiyi.vip.domain.rights.service.RightsService;
import com.iqiyi.vip.dto.base.BaseListResponse;
import com.iqiyi.vip.dto.base.BaseResponse;
import com.iqiyi.vip.dto.order.CheckOrderCanTerminateRightsReq;
import com.iqiyi.vip.dto.rights.CheckSkuCanReceiveReq;
import com.iqiyi.vip.dto.rights.CollectUserInfoReq;
import com.iqiyi.vip.dto.rights.ReceiveByOrderCodeReq;
import com.iqiyi.vip.dto.rights.ReceiveBySkuIdReq;
import com.iqiyi.vip.dto.rights.NotifyRefundedReq;
import com.iqiyi.vip.dto.rights.UserRightsCountListByPackSkuIdRes;
import com.iqiyi.vip.dto.rights.UserRightsCountQryByPackSkuId;
import com.iqiyi.vip.dto.rights.UserRightsListQry;
import com.iqiyi.vip.enums.SignTypeEnum;

/**
 * 权益相关接口
 *
 * <AUTHOR>
 * @date 2023/9/25 10:12
 */
@Api("权益接口")
@Slf4j
@RestController
@RequestMapping(value = "rights")
public class RightsController {

    @Resource
    private RightsService rightsService;
    @Resource
    private OrderService orderService;

    @WebLog
    @ApiOperation(value = "查询用户是否有可领取的权益", httpMethod = "GET")
    @RequestMapping(value = "/checkCanReceiveBySkuId")
    public BaseResponse checkCanReceiveBySkuId(CheckSkuCanReceiveReq req) {
        return BaseResponse.createSuccessResponse(rightsService.checkSkuCanReceiveBatch(req));
    }

    @WebLog
    @ApiOperation(value = "根据skuId领取", httpMethod = "GET")
    @RequestMapping(value = "/receiveBySkuId")
    public BaseResponse receiveBySkuId(ReceiveBySkuIdReq req) {
        return rightsService.receiveBySkuId(req);
    }

    @WebLog
    @ApiOperation(value = "根据订单号领取", httpMethod = "GET")
    @RequestMapping(value = "/receiveByOrderCode")
    public BaseResponse receiveByOrderCode(ReceiveByOrderCodeReq req) {
        return rightsService.receiveByOrderCode(req);
    }

    @WebLog
    @ApiOperation(value = "验证订单能否回收权益", httpMethod = "POST")
    @RequestMapping(value = "/checkOrderCanTerminateRights")
    public BaseResponse checkOrderCanTerminateRights(@RequestBody CheckOrderCanTerminateRightsReq req) {
        return orderService.checkOrderCanTerminateRights(req);
    }

    @WebLog
    @ApiOperation(value = "资产页-用户权益记录", httpMethod = "GET")
    @RequestMapping(value = "/userRightsList")
    public BaseListResponse userRightsList(UserRightsListQry qry) {
        return rightsService.userRightsList(qry);
    }

    @WebLog
    @ApiOperation(value = "资产页-用户权益记录数", httpMethod = "GET")
    @RequestMapping(value = "/userRightsCount")
    public BaseListResponse userRightsCount(UserRightsListQry qry) {
        return rightsService.userRightsCount(qry);
    }

    @WebLog
    @SignCheck(type = SignTypeEnum.MD5)
    @ApiOperation(value = "用户有权益的记录数", httpMethod = "GET")
    @RequestMapping("/userRightsCountByPackSkuId")
    public BaseResponse userRightsCountByPackSkuId(UserRightsCountQryByPackSkuId qry) {
        return BaseResponse.createSuccessResponse(new UserRightsCountListByPackSkuIdRes(rightsService.userRightsCountByPackSkuId(qry)));
    }

    @WebLog
    @ApiOperation(value = "用户个保信息采集", httpMethod = "GET")
    @RequestMapping(value = "/queryCollectData")
    public BaseResponse queryCollectData(CollectUserInfoReq qry) {
        return BaseResponse.createSuccessResponse(rightsService.collectUserInfo(qry));
    }

    /**
     * 用户在合作方侧进行退单，合作方给用户退单并完成权益回收后，将信息同步给爱奇艺
     */
    @WebLog
    @ApiOperation(value = "合作方通知权益已经回收完成", httpMethod = "GET")
    @RequestMapping(value = "/notifyRefunded")
    public BaseResponse notifyRefunded(NotifyRefundedReq req) {
        rightsService.notifyRefunded(req);
        return BaseResponse.createSuccessResponse();
    }
}
