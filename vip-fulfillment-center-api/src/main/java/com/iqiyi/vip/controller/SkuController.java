package com.iqiyi.vip.controller;

import com.iqiyi.vip.aspect.WebLog;
import com.iqiyi.vip.domain.sku.service.SkuService;
import com.iqiyi.vip.dto.base.BaseResponse;
import com.iqiyi.vip.dto.sku.SkuBeforeBuyCheckReq;
import com.iqiyi.vip.dto.sku.SkuBeforeBuyCheckRes;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 商品相关接口
 *
 * <AUTHOR>
 * @date 2023/11/29 11:32
 */
@Api("商品接口")
@Slf4j
@RestController
@RequestMapping(value = "sku")
public class SkuController {

    @Resource
    private SkuService skuService;

    @WebLog(simplify = false)
    @ApiOperation(value = "商品购买前校验（加价购使用）", httpMethod = "POST")
    @RequestMapping(value = "/beforeBuyCheck")
    public BaseResponse beforeBuyCheck(SkuBeforeBuyCheckReq req) {
        return BaseResponse.createSuccessResponse(new SkuBeforeBuyCheckRes(skuService.beforeBuyCheck(req)));
    }

    @WebLog(simplify = false)
    @ApiOperation(value = "商品购买前校验（下单使用）", httpMethod = "POST")
    @RequestMapping(value = "/fastCheck")
    public BaseResponse fastCheck(SkuBeforeBuyCheckReq req) {
        return BaseResponse.createSuccessResponse(skuService.fastCheck(req));
    }

}
