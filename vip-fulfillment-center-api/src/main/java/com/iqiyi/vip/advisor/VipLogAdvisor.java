package com.iqiyi.vip.advisor;

import org.aopalliance.aop.Advice;
import org.springframework.aop.Pointcut;
import org.springframework.aop.aspectj.AspectJExpressionPointcut;
import org.springframework.aop.support.AbstractPointcutAdvisor;
import org.springframework.stereotype.Component;

import com.iqiyi.vip.log.AccessLogInterceptor;

/**
 * <AUTHOR>
 * @date 2019/8/19 14:02
 */
@Component
public class VipLogAdvisor extends AbstractPointcutAdvisor {

    @Override
    public Pointcut getPointcut() {
        AspectJExpressionPointcut cut = new AspectJExpressionPointcut();
        //自定义切点表达式，拦截哪些Controller请求
        cut.setExpression("execution(public * com.iqiyi.vip.controller.*.*(..))");
        return cut;
    }

    @Override
    public Advice getAdvice() {
        return new AccessLogInterceptor();
    }
}

