package com.iqiyi.vip;

import com.alibaba.csp.sentinel.init.InitExecutor;
import com.alicp.jetcache.anno.config.EnableMethodCache;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.metrics.jdbc.DataSourcePoolMetricsAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import springfox.documentation.oas.annotations.EnableOpenApi;

import com.iqiyi.solar.config.client.spring.annotation.EnableCloudConfig;


/**
 * <AUTHOR>
 */
@EnableCloudConfig
@EnableOpenApi
@EnableCaching
@EnableMethodCache(basePackages = "com.iqiyi")
@SpringBootApplication(exclude = {DataSourcePoolMetricsAutoConfiguration.class})
public class ApiApplication {

    public static void main(String[] args) {
        triggerSentinelInit();
        SpringApplication s = new SpringApplication(ApiApplication.class);
        s.setAllowCircularReferences(Boolean.TRUE);
        s.run(args);
    }

    private static void triggerSentinelInit() {
        new Thread(() -> InitExecutor.doInit()).start();
    }

}
