package com.iqiyi.vip.domain.fulfillment.repository;

import java.util.List;

import com.iqiyi.vip.domain.fulfillment.entity.FulfillmentConfig;

/**
 * <AUTHOR>
 * @date 2023/8/1 17:46
 */
public interface FulfillmentRepository {

    void save(FulfillmentConfig fulfillmentConfig);

    FulfillmentConfig query(String skuId, String actCode);

    List<FulfillmentConfig> queryBySkuId(String skuId);

    int updateInvalidBySkuId(String skuId);
}
