package com.iqiyi.vip.domain.rights.repository;

import java.util.List;

import com.iqiyi.vip.dto.base.BaseResponse;
import com.iqiyi.vip.dto.rights.AskPartnerCanBuyByUrlIdDetailRes;
import com.iqiyi.vip.dto.rights.AskPartnerCanBuyByUrlIdReq;
import com.iqiyi.vip.dto.rights.OpenPartnerRightsReq;
import com.iqiyi.vip.dto.rights.PartnerRightsResponse;
import com.iqiyi.vip.dto.rights.TerminateRightsReq;

/**
 * <AUTHOR>
 * @date 2023/9/11 16:47
 */
public interface RightsRepository {

    BaseResponse<PartnerRightsResponse> openRights(OpenPartnerRightsReq req);

    BaseResponse<PartnerRightsResponse> terminateRights(TerminateRightsReq req);

    List<AskPartnerCanBuyByUrlIdDetailRes> batchAskPartnerCanBuyByUrlId(AskPartnerCanBuyByUrlIdReq req);
}
