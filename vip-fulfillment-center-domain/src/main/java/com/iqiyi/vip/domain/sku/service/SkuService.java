package com.iqiyi.vip.domain.sku.service;

import com.iqiyi.vip.domain.sku.entity.Sku;
import com.iqiyi.vip.dto.sku.FastCheckRes;
import com.iqiyi.vip.dto.sku.SkuBeforeBuyCheckDetailRes;
import com.iqiyi.vip.dto.sku.SkuBeforeBuyCheckReq;
import com.iqiyi.vip.dto.sku.SkuPromotionRes;
import com.iqiyi.vip.enums.YesOrNoEnum;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/9/28 11:50
 */
public interface SkuService {

    YesOrNoEnum canOnlineRefund(Sku sku);

    Map<String, SkuPromotionRes> skuId2PromotionMapFromCache(String skuIds);

    Map<String, List<String>> queryPackSkuId2PromotionCodeFromCache(String packSkuIds);

    List<SkuBeforeBuyCheckDetailRes> beforeBuyCheck(SkuBeforeBuyCheckReq req);

    FastCheckRes fastCheck(SkuBeforeBuyCheckReq req);

    /**
     * 重置能否切换账号属性
     */
    void resetChangeAccount(Long uid, Map<String, Sku> skuId2SkuMap);
}
