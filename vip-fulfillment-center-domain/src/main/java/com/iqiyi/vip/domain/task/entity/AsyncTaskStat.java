package com.iqiyi.vip.domain.task.entity;

import lombok.Data;

/**
 * 异步任务统计数据实体类
 */
@Data
public class AsyncTaskStat {

    /**
     * 任务类名
     */
    private String className;

    /**
     * 任务执行次数
     */
    private Integer exeCount;

    /**
     * 积压的数量（按className、exeCount、skuId聚合后的相加数量）
     */
    private Integer num;

    /**
     * 商品SKU标识
     */
    private String skuId;

    /**
     * 用户标识（聚合后保留的示例值）
     */
    private Long uid;

    /**
     * 订单编码（聚合后保留的示例值）
     */
    private String orderCode;

    /**
     * 履约调用的下游地址
     */
    private String uri;

    /**
     * 履约状态
     */
    private Integer sendStatus;

    /**
     * 下游的响应数据
     */
    private String response;
}