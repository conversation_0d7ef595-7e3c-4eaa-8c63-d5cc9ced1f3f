package com.iqiyi.vip.domain.sku.repository;

import com.iqiyi.vip.domain.sku.entity.Sku;
import com.iqiyi.vip.dto.http.HttpResDTO;
import com.iqiyi.vip.dto.sku.SkuBatchQuery;
import com.iqiyi.vip.dto.sku.SkuByAttrQry;
import com.iqiyi.vip.dto.sku.SkuQuery;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/8/1 13:58
 */
public interface SkuRepository {

    Sku queryFromCache(SkuQuery skuQuery);

    Sku query(SkuQuery skuQuery);

    Map<String, Sku> batchQueryFromCache(SkuBatchQuery skuQuery);

    Map<String, Sku> batchQuery(SkuBatchQuery skuQuery);

    HttpResDTO<Sku> queryByAttribute(SkuByAttrQry skuByAttrQry);
}
