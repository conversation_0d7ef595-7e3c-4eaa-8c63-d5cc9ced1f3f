package com.iqiyi.vip.domain.rights.factory;

import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.iqiyi.vip.domain.rights.entity.ReceiveRightRecord;
import com.iqiyi.vip.dto.rights.CollectUserInfoScenesRes;
import com.iqiyi.vip.utils.FieldUtils;

/**
 * 用户个保信息
 *
 * <AUTHOR>
 * @date 2024/7/10 18:32
 */
@Slf4j
public class CollectUserInfoFactory {

    public static CollectUserInfoScenesRes buildCollectUserInfoScenesRes(Map<String, List<ReceiveRightRecord>> account2RightsMap) {
        CollectUserInfoScenesRes scenes = new CollectUserInfoScenesRes();
        scenes.setCollector("本APP");
        scenes.setSceneId("1-4");
        scenes.setType("1");
        Set<String> mobiles = statisticsMobiles(account2RightsMap);
        scenes.setCount(String.valueOf(mobiles.size()));
        scenes.setItems(new LinkedList<>(mobiles));
        return scenes;
    }

    /**
     * 统计手机号
     */
    public static Set<String> statisticsMobiles(Map<String, List<ReceiveRightRecord>> account2RightsMap) {
        Set<String> mobiles = Sets.newHashSet();
        for (Map.Entry<String, List<ReceiveRightRecord>> entry : account2RightsMap.entrySet()) {
            String mobile = entry.getKey();
            if (FieldUtils.isMobile(mobile)) {
                mobiles.add(mobile);
            }
        }
        return mobiles;
    }
}
