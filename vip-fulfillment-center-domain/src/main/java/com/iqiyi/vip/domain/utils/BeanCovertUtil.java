package com.iqiyi.vip.domain.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cglib.beans.BeanMap;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * bean转换工具
 *
 * <AUTHOR>
 * @date 2019/6/12 13:49
 */
@Slf4j
public class BeanCovertUtil {

    public static Map<String, String> transBean2MapStr(Object obj) {
        if (obj == null) {
            return null;
        }
        return BeanMap.create(obj);
    }

    public static Map<String, Object> transBean2Map(Object obj) {
        if (obj == null) {
            return null;
        } else {
            HashMap map = new HashMap();

            try {
                BeanInfo beanInfo = Introspector.getBeanInfo(obj.getClass());
                PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
                PropertyDescriptor[] var5 = propertyDescriptors;
                int var6 = propertyDescriptors.length;

                for (int var7 = 0; var7 < var6; ++var7) {
                    PropertyDescriptor property = var5[var7];
                    String key = property.getName();
                    if (!key.equals("class")) {
                        Method getter = property.getReadMethod();
                        Object value = getter.invoke(obj);
                        if (null != value) {
                            map.put(key, value);
                        }
                    }
                }

                return map;
            } catch (Exception e) {
                log.error("", e);
                return null;
            }
        }
    }
}
