package com.iqiyi.vip.domain.refund;

import com.iqiyi.vip.domain.refund.entity.RetreatResult;
import com.iqiyi.vip.dto.http.HttpResDTO;

/**
 * @author: linpeihui
 * @createTime: 2025/01/15
 */
public interface RefundRepository {

    /**
     * 回收低等级的会员权益
     * 退款接口wiki:http://atlas.qiyi.domain/system/interface/detail?interfaceId=22e2b11de94d4ac79b2b9418f7a4d871&branch=feature-right-exchange-VIPDEV-19070&version=currentVersion&name=%E6%A0%B9%E6%8D%AE%E5%85%91%E6%8D%A2%E5%8D%87%E7%BA%A7%E8%AE%A2%E5%8D%95%E5%9B%9E%E6%94%B6%E7%94%A8%E6%88%B7%E4%BD%8E%E9%98%B6%E6%9D%83%E7%9B%8A
     *
     * @param upgradedOrderCode 升级后的订单号
     * @return
     */
    RetreatResult retreatLowerRight(String upgradedOrderCode);
}
