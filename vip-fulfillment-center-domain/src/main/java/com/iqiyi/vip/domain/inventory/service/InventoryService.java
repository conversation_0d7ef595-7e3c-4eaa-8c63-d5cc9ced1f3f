package com.iqiyi.vip.domain.inventory.service;

import java.util.List;

import com.iqiyi.vip.domain.rights.entity.TerminateOrderAggregate;
import com.iqiyi.vip.domain.sku.entity.SkuBeforeBuyCheckAggregate;
import com.iqiyi.vip.dto.order.OrderPaidMsg;
import com.iqiyi.vip.enums.RetryEnum;

/**
 * 库存中心库存
 *
 * <AUTHOR>
 * @date 2023/9/8 15:46
 */
public interface InventoryService {

    void deductStockFailRetry(OrderPaidMsg orderPaidMsg);

    RetryEnum deductStock(OrderPaidMsg orderPaidMsg);

    void revertStock(TerminateOrderAggregate terminateOrderAggregate);

    void checkSkuStock(List<SkuBeforeBuyCheckAggregate> skuBeforeBuyCheckAggregateList);

}
