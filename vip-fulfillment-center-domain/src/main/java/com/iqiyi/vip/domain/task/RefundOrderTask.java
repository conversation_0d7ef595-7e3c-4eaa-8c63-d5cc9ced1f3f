package com.iqiyi.vip.domain.task;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.iqiyi.vip.context.ApplicationContextUtil;
import com.iqiyi.vip.domain.fulfillment.service.FulfillmentEventService;
import com.iqiyi.vip.dto.base.BaseResponse;
import com.iqiyi.vip.dto.order.RefundOrderInfo;
import com.iqiyi.vip.enums.TaskTypeEnum;
import com.iqiyi.vip.enums.YesOrNoEnum;
import com.iqiyi.vip.threadpool.AbstractTask;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/9/27 15:37
 */
@Data
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class RefundOrderTask extends AbstractTask {

    private RefundOrderInfo orderInfo;

    @Override
    protected boolean execute() {
        try {
            log.info("[start][orderInfo:{}]", orderInfo);
            FulfillmentEventService fulfillmentEventService = (FulfillmentEventService) ApplicationContextUtil
                .getBean("fulfillmentEventService");
            BaseResponse response = fulfillmentEventService.refundOrder(orderInfo);

            boolean dealSuccess = true;
            if (null == response || YesOrNoEnum.YES.getValue().equals(response.getRetry())) {
                dealSuccess = false;
            }
            log.info("[end][orderInfo:{}]", orderInfo);
            return dealSuccess;
        } catch (Exception e) {
            log.error("Exception, orderInfo:{}", orderInfo, e);
            return false;
        }
    }

    @SneakyThrows
    @Override
    public void deserialize(String data) throws IllegalArgumentException {
        ObjectMapper objectMapper = new ObjectMapper();
        orderInfo = objectMapper.readValue(data, RefundOrderInfo.class);
    }

    @SneakyThrows
    @Override
    public String serialize() {
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.writeValueAsString(orderInfo);
    }

    @Override
    public int getDefaultPoolType() {
        return TaskTypeEnum.TERMINATE_ORDER.getType();
    }
}
