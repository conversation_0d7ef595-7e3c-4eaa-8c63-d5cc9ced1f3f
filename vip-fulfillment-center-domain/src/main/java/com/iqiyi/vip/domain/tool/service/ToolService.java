package com.iqiyi.vip.domain.tool.service;

import java.util.List;

import com.iqiyi.vip.domain.sku.entity.Sku;
import com.iqiyi.vip.dto.base.BaseResponse;
import com.iqiyi.vip.dto.rights.BatchDeliverOrderReq;
import com.iqiyi.vip.dto.rights.BatchFulfillOrderReq;
import com.iqiyi.vip.dto.rights.BatchNotifyVmcReq;
import com.iqiyi.vip.dto.rights.BatchRefundOrderReq;
import com.iqiyi.vip.dto.rights.PartnerRightsResponse;
import com.iqiyi.vip.dto.sku.SkuByAttrQry;

/**
 * 一些工具
 *
 * <AUTHOR>
 * @date 2024/11/1 18:22
 */
public interface ToolService {

    List<Sku> batchQuerySkuByAttributeOneTime(SkuByAttrQry req);

    void batchSaveFulfillConfigBySkuAttrOneTime(SkuByAttrQry req);

    void batchSaveFulfillConfigBySkuAttr(SkuByAttrQry req);

    void batchSaveFulfillConfig(String skuIds);

    void batchNotifyVmc(BatchNotifyVmcReq req);

    List<BaseResponse<PartnerRightsResponse>> batchFulfillOrder(BatchFulfillOrderReq req);

    List<BaseResponse<PartnerRightsResponse>> batchRefundOrder(BatchRefundOrderReq req);

    void batchDeliverOrder(BatchDeliverOrderReq req);
}
