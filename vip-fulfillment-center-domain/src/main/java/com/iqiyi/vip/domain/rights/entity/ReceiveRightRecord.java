package com.iqiyi.vip.domain.rights.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/8/31 11:20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReceiveRightRecord {

    private Long id;

    private String orderCode;

    private String refundCode;

    private String presentOrderCode;

    private Long uid;

    private String encryptMobile;

    private String encryptAccount;

    private String promotionCode;

    private String skuId;

    private String actCode;

    private String activityCode;

    private String ruleCode;

    private Long conditionGroupId;

    private String presentNo;

    private Integer actType;

    private String partnerNo;

    private Integer receiveType;

    private Integer sendType;

    private Integer sendStatus;

    private Integer receiveStatus;

    private Integer refundStatus;

    private Integer callBackStatus;

    private Integer partnerVipUsed;

    private Long settlementPrice;

    private Long refundSettlementPrice;

    private Date orderTime;

    private Date sendTime;

    private Date receiveDeadlineTime;

    private Date receiveTime;

    private Date refundTime;

    private String sendRes;

    private String refundRes;

    private Date iqRefundTime;

    private Integer type;

    private String refundMsg;

    private String cardCode;

    private String remark;

    private Date createTime;

    private Date updateTime;

    private Integer amount;

    private Integer refundAmount;

    /**
     * 流水号（必须非空）
     */
    private String tradeCode;

    private Integer settlementPercent;

    private String couponCode;

}
