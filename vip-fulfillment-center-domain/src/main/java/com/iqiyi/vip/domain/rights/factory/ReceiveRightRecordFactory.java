package com.iqiyi.vip.domain.rights.factory;

import java.util.Date;

import com.iqiyi.vip.domain.jd.service.JdService;
import com.iqiyi.vip.domain.order.service.OrderService;
import com.iqiyi.vip.domain.rights.entity.FulfillOrderAggregate;
import com.iqiyi.vip.domain.rights.entity.OpenOrderRights;
import com.iqiyi.vip.domain.rights.entity.ReceiveRightRecord;
import com.iqiyi.vip.domain.rights.entity.TerminateOrderAggregate;
import com.iqiyi.vip.domain.rights.repository.ReceiveRecordRepository;
import com.iqiyi.vip.domain.rights.service.RightsService;
import com.iqiyi.vip.dto.exact.RightsExactMsg;
import com.iqiyi.vip.dto.exact.RightsExactTaskMsg;
import com.iqiyi.vip.dto.order.PaidOrderInfo;
import com.iqiyi.vip.dto.rights.ReceiveRecordByOrderCodeQry;
import com.iqiyi.vip.dto.rights.RecordByOrderCodeSkuIdQry;
import com.iqiyi.vip.enums.CallBackStatusEnum;
import com.iqiyi.vip.enums.ExactRightsRemindTypeEnum;
import com.iqiyi.vip.enums.ReceiveRightRecordRefundStatus;
import com.iqiyi.vip.enums.ReceiveStatusEnum;
import com.iqiyi.vip.enums.SendStatusEnum;
import com.iqiyi.vip.utils.RigthsTradeCodeUtils;

/**
 * <AUTHOR>
 * @date 2023/9/8 15:39
 */
public class ReceiveRightRecordFactory {

    public static ReceiveRecordByOrderCodeQry buildQuery(FulfillOrderAggregate fulfillOrderAggregate) {
        return ReceiveRecordByOrderCodeQry.builder()
            .orderCode(fulfillOrderAggregate.getOrderInfo().getOrderCode())
            .promotionCode(fulfillOrderAggregate.getPromotionCode())
            .uid(fulfillOrderAggregate.getUid())
            .build();
    }

    public static ReceiveRecordByOrderCodeQry buildQuery(OpenOrderRights openRights) {
        return ReceiveRecordByOrderCodeQry.builder()
            .orderCode(openRights.getOrderInfo().getOrderCode())
            .promotionCode(openRights.getPromotionCode())
            .uid(openRights.getUid())
            .build();
    }

    public static RecordByOrderCodeSkuIdQry buildQuery(PaidOrderInfo paidOrderInfo) {
        return RecordByOrderCodeSkuIdQry.builder()
            .orderCode(paidOrderInfo.getOrderCode())
            .uid(paidOrderInfo.getUid())
            .skuId(paidOrderInfo.getSkuId())
            .build();
    }

    public static ReceiveRecordByOrderCodeQry buildQuery(TerminateOrderAggregate terminateOrderAggregate) {
        return ReceiveRecordByOrderCodeQry.builder()
            .uid(terminateOrderAggregate.getRefundOrderInfo().getUid())
            .orderCode(terminateOrderAggregate.getRefundOrderInfo().getPaidOrderCode())
            .promotionCode(terminateOrderAggregate.getPromotionCode()).build();
    }

    public static ReceiveRecordByOrderCodeQry buildByMsg(RightsExactTaskMsg msg) {
        return ReceiveRecordByOrderCodeQry.builder()
            .orderCode(msg.getOrderCode())
            .promotionCode(msg.getPromotionCode())
            .uid(Long.valueOf(msg.getUid()))
            .build();
    }

    public static RightsExactTaskMsg buildRightsExactTaskMsg(FulfillOrderAggregate fulfillOrderAggregate) {
        RightsExactTaskMsg msg = RightsExactTaskMsg.builder().orderCode(fulfillOrderAggregate.getOrderInfo().getOrderCode())
            .uid(String.valueOf(fulfillOrderAggregate.getUid()))
            .promotionCode(fulfillOrderAggregate.getPromotionCode())
            .remindType(ExactRightsRemindTypeEnum.REMIND_DRAW.getType())
            .build();
        return msg;
    }

    public static RightsExactTaskMsg buildRightsExactDrawSucTaskMsg(ReceiveRightRecord receiveRightRecord) {
        RightsExactTaskMsg msg = RightsExactTaskMsg.builder().orderCode(receiveRightRecord.getOrderCode())
            .uid(String.valueOf(receiveRightRecord.getUid()))
            .promotionCode(receiveRightRecord.getPromotionCode())
            .remindType(ExactRightsRemindTypeEnum.DRAW_SUC.getType())
            .build();
        return msg;
    }

    public static RightsExactMsg buildRightsExactMsg(RightsExactTaskMsg taskMsg, OrderService orderService) {
        RightsExactMsg msg = RightsExactMsg.builder().orderCode(taskMsg.getOrderCode())
            .receiveCode(taskMsg.getPromotionCode())
            .uid(String.valueOf(taskMsg.getUid()))
            .platformCode(orderService.queryOrderPlatformCode(taskMsg.getOrderCode()))
            .remindType(taskMsg.getRemindType())
            .build();
        return msg;
    }

    public static ReceiveRightRecord create(FulfillOrderAggregate fulfillOrderAggregate, ReceiveRecordRepository receiveRecordRepository, RightsService rightsService, JdService jdService) {
        ReceiveRightRecord receiveRightRecord = createPending(fulfillOrderAggregate, jdService);
        receiveRecordRepository.insertRecord(receiveRightRecord);
        if (fulfillOrderAggregate.getSku().needNotifyTag()) {
            rightsService.doExtAfterInsertRightsRecord(receiveRightRecord);
        }
        return receiveRightRecord;
    }

    public static ReceiveRightRecord createPending(FulfillOrderAggregate fulfillOrderAggregate, JdService jdService) {
        ReceiveRightRecord rightRecord = new ReceiveRightRecord();
        rightRecord.setOrderCode(fulfillOrderAggregate.getOrderInfo().getOrderCode());
        rightRecord.setPresentOrderCode(fulfillOrderAggregate.getOrderInfo().getOrderCode());
        rightRecord.setUid(fulfillOrderAggregate.getUid());
        rightRecord.setPromotionCode(fulfillOrderAggregate.getPromotionCode());
        rightRecord.setRuleCode(rightRecord.getPromotionCode());
        rightRecord.setSkuId(fulfillOrderAggregate.getSku().getSkuId());
        rightRecord.setActCode(fulfillOrderAggregate.getActCode());
        rightRecord.setPresentNo("vip-fulfillment-center");
        rightRecord.setActType(fulfillOrderAggregate.getActType());
        rightRecord.setReceiveType(fulfillOrderAggregate.getConstraint().getSendTypeEnum().getType());
        rightRecord.setSendStatus(SendStatusEnum.UN_SEND.getStatus());
        rightRecord.setReceiveStatus(ReceiveStatusEnum.PENDING_RECEIVE.getStatus());
        rightRecord.setRefundStatus(ReceiveRightRecordRefundStatus.UN_REFUND.getStatus());
        rightRecord.setOrderTime(fulfillOrderAggregate.getOrderInfo().getPayTime());
        //设置必填流水号
        rightRecord.setTradeCode(RigthsTradeCodeUtils.getRightsTradeCode("Rights", ""));
        rightRecord.setReceiveDeadlineTime(fulfillOrderAggregate.getConstraint()
            .getReceiveDeadline(fulfillOrderAggregate.getOrderInfo().getPayTime()));
        //todo 京东这个amount会在领取逻辑里使用 + 客服后台查询也用！！！
        rightRecord.setAmount(jdService.isJdGiftCode(fulfillOrderAggregate.getGift().getCode()) ? jdService.jdRecordAmount() : 1);
        Date curTime = new Date();
        rightRecord.setCreateTime(curTime);
        rightRecord.setUpdateTime(curTime);
        rightRecord.setCallBackStatus(CallBackStatusEnum.UN_CALL.getStatus());
        return rightRecord;
    }

    public static ReceiveStatusEnum getReceiveStatusMsg(ReceiveRightRecord receiveRightRecord) {
        if (receiveRightRecord.getReceiveStatus() == null) {
            return null;
        }
        int receiveStatus = receiveRightRecord.getReceiveStatus();
        if (ReceiveStatusEnum.PENDING_RECEIVE.getStatus() == receiveRightRecord.getReceiveStatus()
            && null != receiveRightRecord.getReceiveDeadlineTime() && new Date().after(receiveRightRecord.getReceiveDeadlineTime())) {
            receiveStatus = ReceiveStatusEnum.EXPIRED.getStatus();
        }
        return ReceiveStatusEnum.of(receiveStatus);
    }

}