package com.iqiyi.vip.domain.rights.entity;

import lombok.Data;

import com.iqiyi.vip.domain.constraint.entity.RefundConstraint;
import com.iqiyi.vip.domain.sku.entity.Sku;
import com.iqiyi.vip.dto.order.RefundOrderInfo;

/**
 * 订单解约聚合根
 *
 * <AUTHOR>
 * @date 2023/9/26 13:53
 */
@Data
public class TerminateOrderAggregate {

    private RefundOrderInfo refundOrderInfo;
    private RefundConstraint refundConstraint;
    private String promotionCode;
    private Sku sku;

}
