package com.iqiyi.vip.domain.task;

import com.alibaba.fastjson.JSONObject;
import com.iqiyi.vip.context.ApplicationContextUtil;
import com.iqiyi.vip.domain.order.repository.OrderRepository;
import com.iqiyi.vip.enums.TaskTypeEnum;
import com.iqiyi.vip.threadpool.AbstractTask;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/4/1 18:33
 */
@Data
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class CancelOrderTask extends AbstractTask {

    private String orderCode;

    @Override
    protected boolean execute() {
        try{
            log.info("CancelOrderTask execute orderCode:{}", orderCode);
            OrderRepository orderRepository = (OrderRepository) ApplicationContextUtil.getBean("orderRepository");
            return orderRepository.cancel(orderCode);
        }catch (Exception e){
            log.error("CancelOrderTask error", e);
            return false;
        }
    }

    @Override
    public void deserialize(String data) throws IllegalArgumentException {
        JSONObject json = JSONObject.parseObject(data);
        orderCode = json.getString("orderCode");
    }

    @Override
    public String serialize() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("orderCode", orderCode);
        return jsonObject.toJSONString();
    }

    @Override
    public int getDefaultPoolType() {
        return TaskTypeEnum.VIP_CANCEL_ORDER.getType();
    }
}
