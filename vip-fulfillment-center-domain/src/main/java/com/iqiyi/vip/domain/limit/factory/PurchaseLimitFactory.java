package com.iqiyi.vip.domain.limit.factory;

import com.iqiyi.vip.domain.limit.entity.PurchaseLimit;
import com.iqiyi.vip.domain.sku.entity.Sku;
import com.iqiyi.vip.domain.sku.entity.SkuSpecAttributes;
import com.iqiyi.vip.enums.PurchaseLimitTimeUnitEnum;

/**
 * <AUTHOR>
 * @date 2023/9/8 15:28
 */
public class PurchaseLimitFactory {

    public static PurchaseLimit buildPurchaseLimit(Sku sku) {
        SkuSpecAttributes specAttributes = sku.getSpecAttributes();
        if (null == specAttributes) {
            return new PurchaseLimit();
        }
        PurchaseLimit purchaseLimit = new PurchaseLimit();
        purchaseLimit.setLimitMaxCount(sku.getSpecAttributes().getConstraintLimitCountUser());
        purchaseLimit.setLimitTimeType(PurchaseLimitTimeUnitEnum.NO_LIMIT.getType());
        return purchaseLimit;
    }
}
