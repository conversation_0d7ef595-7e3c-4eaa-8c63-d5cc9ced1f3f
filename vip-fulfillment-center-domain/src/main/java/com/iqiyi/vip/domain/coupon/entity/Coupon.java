package com.iqiyi.vip.domain.coupon.entity;

import lombok.Data;

import com.iqiyi.vip.utils.DateUtils;

/**
 * <AUTHOR>
 * @date 2023/9/28 15:30
 */
@Data
public class Coupon {

    private String coupon_code;
    /**
     * 状态
     */
    private String status;
    private String batch_no;
    /**
     * 有效期开始日期,格式:yyyy-MM-dd HH:mm:ss
     */
    private String start_time;
    /**
     * 有效期结束日期,格式:yyyy-MM-dd HH:mm:ss
     */
    private String end_time;
    private String bind_time;
    private String used_time;
    private String amount;
    private Long user_id;
    private String consumer_id;
    private String order_code;
    private String settlement;

    /**
     * 判断此代金券是否可退
     *
     * @return true/false
     */
    public boolean canRefund() {
        return StatusEnum.canRefund(status) && System.currentTimeMillis() < DateUtils.str2LongTime(end_time, "yyyy-MM-dd HH:mm:ss");
    }

    enum StatusEnum {
        /**
         * 未激活
         */
        UN_ACTIVATED("0"),
        /**
         * 可使用
         */
        CAN_USE("1"),
        /**
         * 已冻结
         */
        FREEZE("2"),
        /**
         * 已使用
         */
        HAVE_USED("3"),
        /**
         * 锁定中
         */
        LOCKED("4"),
        /**
         * 已销毁
         */
        HAVE_DESTROY("5");

        private String value;

        /**
         * 已冻结|已使用|已锁定|已销毁 不可退
         *
         * @param value 值
         * @return 是否可退
         */
        public static boolean canRefund(String value) {
            if (FREEZE.value.equals(value) ||
                HAVE_USED.value.equals(value) ||
                LOCKED.value.equals(value) ||
                HAVE_DESTROY.value.equals(value)) {
                return false;
            } else {
                return true;
            }
        }

        StatusEnum(String value) {
            this.value = value;
        }
    }
}
