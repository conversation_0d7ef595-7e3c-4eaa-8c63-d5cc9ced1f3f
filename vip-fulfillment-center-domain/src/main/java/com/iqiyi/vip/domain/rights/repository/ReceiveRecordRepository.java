package com.iqiyi.vip.domain.rights.repository;

import java.util.List;

import com.iqiyi.vip.domain.rights.entity.ReceiveRightRecord;
import com.iqiyi.vip.dto.rights.ReceiveRecordByOrderCodeQry;
import com.iqiyi.vip.dto.rights.ReceiveRightRecordQryCon;
import com.iqiyi.vip.dto.rights.RecordByOrderCodeSkuIdQry;

/**
 * <AUTHOR>
 * @date 2023/8/31 10:53
 */
public interface ReceiveRecordRepository {

    int insertRecord(ReceiveRightRecord receiveRightRecord);

    Integer updateByOrderCodeSelective(ReceiveRightRecord updateInfo);

    Integer updateCallBackStatus(ReceiveRightRecord updateInfo);

    List<ReceiveRightRecord> queryByCon(ReceiveRightRecordQryCon con);

    List<ReceiveRightRecord> queryByConFromMySql(ReceiveRightRecordQryCon con);

    ReceiveRightRecord queryByOrderCode(ReceiveRecordByOrderCodeQry qry);

    ReceiveRightRecord queryByOrderCode(RecordByOrderCodeSkuIdQry qry);

    /**
     * 查询可领的订单
     */
    List<ReceiveRightRecord> queryCanReceiveOrderList(ReceiveRecordByOrderCodeQry receiveRecordByorderCodeQry);

}
