/**
 * 
 */
package com.iqiyi.vip.domain.task;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.iqiyi.vip.context.ApplicationContextUtil;
import com.iqiyi.vip.domain.fulfillment.service.FulfillmentMqService;
import com.iqiyi.vip.domain.rights.entity.OpenOrderRights;
import com.iqiyi.vip.domain.rights.entity.ReceiveRightRecord;
import com.iqiyi.vip.dto.rights.PartnerRightsResponse;
import com.iqiyi.vip.enums.TaskTypeEnum;
import com.iqiyi.vip.threadpool.AbstractTask;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 权益开通消息发送任务
 */
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class FulfillmentGrantMsgTask extends AbstractTask {
	
	private ReceiveRightRecord receiveRightRecord;
	
	private OpenOrderRights openRights;
	
	private PartnerRightsResponse openPartnerRightsRes;

	@Override
	public int getDefaultPoolType() {
        return TaskTypeEnum.RIGHTS_OPENED_MSG.getType();
	}

	@Override
	protected boolean execute() {
        try {
            log.info("[start][record: {}, open: {}, res: {}]", receiveRightRecord, openRights, openPartnerRightsRes);

            FulfillmentMqService fulfillmentMqService = (FulfillmentMqService) ApplicationContextUtil.getBean("fulfillmentMqService");
            fulfillmentMqService.sendOpenRightsMsg(receiveRightRecord, openRights, openPartnerRightsRes);
			
            log.info("[end][record: {}, open: {}, res: {}]", receiveRightRecord, openRights, openPartnerRightsRes);
            return true;
        } catch (Exception e) {
            log.error("[exception][record: {}, open: {}, res: {}]", receiveRightRecord, openRights, openPartnerRightsRes, e);
            return false;
        }
	}

	@Override
	public void deserialize(String data) throws IllegalArgumentException {
        Gson gson = new Gson();
        JsonObject json = gson.fromJson(data, JsonObject.class);

        openPartnerRightsRes = gson.fromJson(json.get("openPartnerRightsRes").getAsString(), PartnerRightsResponse.class);
        receiveRightRecord = gson.fromJson(json.get("receiveRightRecord").getAsString(), ReceiveRightRecord.class);
        openRights = gson.fromJson(json.get("openRights").getAsString(), OpenOrderRights.class);
	}

	@Override
	public String serialize() {
		Gson gson = new Gson();
		JsonObject json = new JsonObject();
		json.addProperty("openPartnerRightsRes", gson.toJson(openPartnerRightsRes));
		json.addProperty("receiveRightRecord", gson.toJson(receiveRightRecord));
		json.addProperty("openRights", gson.toJson(openRights));
		return json.toString();
	}
}
