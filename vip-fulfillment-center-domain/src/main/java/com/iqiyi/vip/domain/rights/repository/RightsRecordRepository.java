package com.iqiyi.vip.domain.rights.repository;

import java.util.List;
import java.util.Map;

import com.iqiyi.vip.domain.rights.entity.ReceiveRightRecord;
import com.iqiyi.vip.dto.rights.ReceiveRecordByOrderCodeQry;
import com.iqiyi.vip.dto.rights.ReceiveRightRecordQryCon;
import com.iqiyi.vip.dto.rights.RecordByOrderCodeSkuIdQry;

/**
 * <AUTHOR>
 * @date 2024/7/31 10:16
 */
public interface RightsRecordRepository {

    int insertRecord(ReceiveRightRecord receiveRightRecord);

    Integer updateByOrderCodeSelective(ReceiveRightRecord updateInfo);

    Integer updateCallBackStatus(ReceiveRightRecord updateInfo);

    List<ReceiveRightRecord> queryByCon(ReceiveRightRecordQryCon con);

    List<ReceiveRightRecord> queryByConFromMySql(ReceiveRightRecordQryCon con);

    ReceiveRightRecord queryByOrderCode(ReceiveRecordByOrderCodeQry qry);

    ReceiveRightRecord queryByOrderCode(RecordByOrderCodeSkuIdQry qry);

    Map<String, List<ReceiveRightRecord>> promotionCode2RightsMap(Long uid);

    /**
     * 查询可领的订单
     */
    List<ReceiveRightRecord> queryCanReceiveOrderList(ReceiveRecordByOrderCodeQry receiveRecordByorderCodeQry);
}
