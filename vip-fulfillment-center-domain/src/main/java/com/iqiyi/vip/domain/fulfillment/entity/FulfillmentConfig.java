package com.iqiyi.vip.domain.fulfillment.entity;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/7/31 16:24
 */
@Data
public class FulfillmentConfig {

    private String skuId;

    private String spuId;

    private String catalogId;

    private String name;

    private String actCode;

    private Integer sendType;

    private Integer receiveDeadlineType;

    private Integer receiveDeadlineRelativeDays;

    private Date receiveDeadlineAbsolute;

    private Integer refundPartnerType;

    private Date refundPartnerDeadline;

    private Integer actType;

    private Integer dealModule;

    private Integer version;

    private String operator;
}
