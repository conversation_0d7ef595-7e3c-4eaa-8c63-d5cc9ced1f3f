package com.iqiyi.vip.domain.fulfillment.service;

import com.iqiyi.vip.dto.act.ActMsg;
import com.iqiyi.vip.dto.base.BaseResponse;
import com.iqiyi.vip.dto.order.PaidOrderInfo;
import com.iqiyi.vip.dto.order.RefundOrderInfo;
import com.iqiyi.vip.dto.rights.PartnerRightsResponse;
import com.iqiyi.vip.enums.DealModuleEnum;

/**
 * <AUTHOR>
 * @date 2023/8/1 18:22
 */
public interface FulfillmentEventService {

    /**
     * 录入履约配置
     */
    boolean save(String skuId, ActMsg actMsg);

    /****** start 订单履约  ****/
    void fulfillOrderFailRetry(PaidOrderInfo orderInfo, DealModuleEnum dealModule);

    BaseResponse<PartnerRightsResponse> fulfillOrder(PaidOrderInfo orderInfo, DealModuleEnum dealModule);

    BaseResponse<PartnerRightsResponse> fulfillOrder(String orderCode);
    /****** end 订单履约  ****/


    /****** start 订单解约  ****/
    void refundOrderFailRetry(RefundOrderInfo refundOrderInfo);

    BaseResponse<PartnerRightsResponse> refundOrder(RefundOrderInfo refundOrderInfo);

    BaseResponse<PartnerRightsResponse> refundOrder(String orderCode);
    /****** end 订单解约  ****/
}
