package com.iqiyi.vip.domain.inventory.repository;

import java.util.List;

import com.iqiyi.vip.dto.stock.DeductStockReq;
import com.iqiyi.vip.dto.stock.QueryStockReq;
import com.iqiyi.vip.dto.stock.RevertStockReq;
import com.iqiyi.vip.dto.stock.StockQueryRes;

/**
 * <AUTHOR>
 * @date 2023/9/8 16:48
 */
public interface InventoryRepository {

    /**
     * 库存中心减库存
     */
    boolean deductStock(DeductStockReq deductStockReq);

    /**
     * 库存中心回滚库存
     */
    boolean revertStock(RevertStockReq req);

    /**
     * 库存中心批量查库存
     */
    List<StockQueryRes> queryStockBatch(List<QueryStockReq> req);
}
