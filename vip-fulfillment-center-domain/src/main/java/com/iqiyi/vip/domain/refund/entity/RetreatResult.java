package com.iqiyi.vip.domain.refund.entity;

import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * @author: lin<PERSON><PERSON><PERSON>
 * @createTime: 2025/01/16
 */
@Data
public class RetreatResult {
    public static final String SUCCESS = "SUCCESS";
    public static final String RETRY = "RETRY";
    public static final String CANCEL = "CANCEL";


    private String status;

    public boolean isSuccess() {
        return StringUtils.isNotBlank(this.status) && SUCCESS.equals(this.status);
    }

    public boolean needRetry() {
        return StringUtils.isNotBlank(this.status) && RETRY.equals(this.status);
    }

    public boolean needCancel() {
        return StringUtils.isNotBlank(this.status) && CANCEL.equals(this.status);
    }
}
