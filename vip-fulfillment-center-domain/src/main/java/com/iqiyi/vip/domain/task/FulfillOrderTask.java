package com.iqiyi.vip.domain.task;

import com.iqiyi.vip.context.ApplicationContextUtil;
import com.iqiyi.vip.domain.fulfillment.service.FulfillmentEventService;
import com.iqiyi.vip.dto.base.BaseResponse;
import com.iqiyi.vip.dto.order.PaidOrderInfo;
import com.iqiyi.vip.enums.TaskTypeEnum;
import com.iqiyi.vip.enums.YesOrNoEnum;
import com.iqiyi.vip.threadpool.AbstractTask;
import com.iqiyi.vip.utils.JacksonUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * 履约重试task
 *
 * <AUTHOR>
 * @date 2023/9/18 14:43
 */
@Data
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class FulfillOrderTask extends AbstractTask {

    private PaidOrderInfo orderInfo;

    @Override
    protected boolean execute() {
        try {
            log.info("[start][orderInfo:{}]", orderInfo);
            FulfillmentEventService fulfillmentEventService = (FulfillmentEventService) ApplicationContextUtil
                .getBean("fulfillmentEventService");
            BaseResponse response = fulfillmentEventService.fulfillOrder(orderInfo, null);

            boolean dealSuccess = true;
            if (null == response || YesOrNoEnum.YES.getValue().equals(response.getRetry())) {
                dealSuccess = false;
            }
            log.info("[end][orderInfo:{},dealSuccess:{}]", orderInfo, dealSuccess);
            return dealSuccess;
        } catch (Exception e) {
            log.error("FulfillmentTask, orderInfo:{}", orderInfo, e);
            return false;
        }
    }

    @SneakyThrows
    @Override
    public void deserialize(String s) throws IllegalArgumentException {
        orderInfo = JacksonUtil.json2obj(s, PaidOrderInfo.class);
    }

    @SneakyThrows
    @Override
    public String serialize() {
        return JacksonUtil.writeValueAsString(orderInfo);
    }

    @Override
    public int getDefaultPoolType() {
        return TaskTypeEnum.FULFILL_ORDER.getType();
    }
}
