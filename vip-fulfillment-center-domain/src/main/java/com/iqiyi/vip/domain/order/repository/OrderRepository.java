package com.iqiyi.vip.domain.order.repository;

import com.iqiyi.vip.domain.order.entity.FreePayResult;
import com.qiyi.vip.trade.dataservice.client.dto.OrderDto;
import com.iqiyi.vip.dto.http.HttpResDTO;
import com.iqiyi.vip.dto.rights.OrderDeliverReq;

/**
 * <AUTHOR>
 * @date 2023/9/12 21:17
 */
public interface OrderRepository {

    HttpResDTO deliver(OrderDeliverReq req);

    Boolean cancel(String orderCode);

    OrderDto queryByOrderCode(String orderCode);

    OrderDto queryByTradeCode(String tradeCode);

    FreePayResult freePay(String skuId, OrderDto order, String traceId);
}
