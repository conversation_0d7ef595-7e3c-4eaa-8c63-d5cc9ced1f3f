package com.iqiyi.vip.domain.constraint.factory;

import org.apache.commons.lang.StringUtils;

import java.util.Date;

import com.iqiyi.vip.domain.constraint.entity.Constraint;
import com.iqiyi.vip.domain.constraint.entity.RefundConstraint;
import com.iqiyi.vip.domain.fulfillment.entity.FulfillmentConfig;
import com.iqiyi.vip.domain.sku.entity.Sku;
import com.iqiyi.vip.domain.sku.entity.SkuSpecAttributes;
import com.iqiyi.vip.enums.ReceiveDeadlineTypeEnum;
import com.iqiyi.vip.enums.SendTypeEnum;
import com.iqiyi.vip.enums.RefundPartnerType;
import com.iqiyi.vip.utils.DateUtils;

/**
 * <AUTHOR>
 * @date 2023/9/8 15:16
 */
public class ConstraintFactory {

    /**
     * 履约约束
     */
    public static Constraint buildConstraint(Sku sku, FulfillmentConfig fulfillmentConfig) {
        SkuSpecAttributes specAttributes = sku.getSpecAttributes();
        if (null == specAttributes) {
            return null;
        }

        Constraint constraint = new Constraint();
        constraint.setSendTypeEnum(SendTypeEnum.of(specAttributes.getConstraintSendType()));
        constraint.setReceiveDeadlineTypeEnum(ReceiveDeadlineTypeEnum.getReceiveDeadlineTypeEnum(specAttributes.getReceiveDeadlineRelativeDays()));
        constraint.setReceiveDeadlineRelativeDays(specAttributes.getReceiveDeadlineRelativeDays());
        constraint.setReceiveDeadlineAbsolute(DateUtils.long2Date(specAttributes.getReceiveDeadlineAbsolute()));

        resetByFulfillmentConfig(fulfillmentConfig, constraint);
        return constraint;
    }

    public static void resetByFulfillmentConfig(FulfillmentConfig fulfillmentConfig, Constraint constraint) {
        if (fulfillmentConfig == null || StringUtils.isBlank(fulfillmentConfig.getActCode())) {
            return;
        }
        constraint.setSendTypeEnum(SendTypeEnum.of(fulfillmentConfig.getSendType()));
        constraint.setReceiveDeadlineTypeEnum(ReceiveDeadlineTypeEnum.getReceiveDeadlineTypeEnum(fulfillmentConfig.getReceiveDeadlineRelativeDays()));
        constraint.setReceiveDeadlineRelativeDays(fulfillmentConfig.getReceiveDeadlineRelativeDays());
        constraint.setReceiveDeadlineAbsolute(fulfillmentConfig.getReceiveDeadlineAbsolute());
    }

    /**
     * 解约约束
     */
    public static RefundConstraint buildRefundConstraint(Sku sku, FulfillmentConfig fulfillmentConfig) {
        SkuSpecAttributes specAttributes = sku.getSpecAttributes();
        if (null == specAttributes) {
            return null;
        }
        RefundConstraint refundConstraint = new RefundConstraint();
        refundConstraint.setRefundPartnerType(RefundPartnerType.getRefundPartnerType(specAttributes.getRefundPartnerType()));
        //若没配领取截止时间，默认一个固定的
        refundConstraint.setRefundPartnerDeadline(getRefundPartnerDeadline(DateUtils.long2Date(specAttributes.getRefundPartnerDeadline())));
        resetByFulfillmentConfig(fulfillmentConfig, refundConstraint);
        return refundConstraint;
    }

    public static void resetByFulfillmentConfig(FulfillmentConfig fulfillmentConfig, RefundConstraint refundConstraint) {
        if (null == fulfillmentConfig) {
            return;
        }
        if (StringUtils.isBlank(fulfillmentConfig.getActCode())) {
            return;
        }
        refundConstraint.setRefundPartnerType(RefundPartnerType.getRefundPartnerType(fulfillmentConfig.getRefundPartnerType()));
        refundConstraint.setRefundPartnerDeadline(getRefundPartnerDeadline(fulfillmentConfig.getRefundPartnerDeadline()));
    }

    public static Date getRefundPartnerDeadline(Date refundPartnerDeadlineConfig) {
        if (null == refundPartnerDeadlineConfig) {
            return DateUtils.getDateFromStr("2050-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss");
        }
        return refundPartnerDeadlineConfig;
    }
}
