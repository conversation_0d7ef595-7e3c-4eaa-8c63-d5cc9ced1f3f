package com.iqiyi.vip.domain.order.service;

import com.iqiyi.vip.domain.order.entity.FreePayResult;
import com.qiyi.vip.trade.dataservice.client.dto.OrderDto;
import com.iqiyi.vip.dto.base.BaseResponse;
import com.iqiyi.vip.dto.order.CheckOrderCanTerminateRightsReq;
import com.iqiyi.vip.dto.rights.ReceiveRecordByOrderCodeQry;
import com.iqiyi.vip.dto.rights.RecordByOrderCodeSkuIdQry;

/**
 * <AUTHOR>
 * @date 2023/9/12 21:02
 */
public interface OrderService {

    boolean paidOrderCallBack(ReceiveRecordByOrderCodeQry query);

    boolean paidOrderCallBackFailRetry(ReceiveRecordByOrderCodeQry query);

    boolean paidOrderCallBack(RecordByOrderCodeSkuIdQry query);

    boolean refundOrderCallBack(ReceiveRecordByOrderCodeQry query);

    String queryOrderPlatformCode(String orderCode);

    OrderDto queryByOrderCode(String orderCode);

    OrderDto queryByTradeCode(String tradeCode);

    BaseResponse checkOrderCanTerminateRights(CheckOrderCanTerminateRightsReq req);

    FreePayResult freePay(String skuId, OrderDto order, String traceId);
}
