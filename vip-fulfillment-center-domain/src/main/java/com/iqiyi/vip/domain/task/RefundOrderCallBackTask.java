package com.iqiyi.vip.domain.task;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.iqiyi.vip.context.ApplicationContextUtil;
import com.iqiyi.vip.domain.order.service.OrderService;
import com.iqiyi.vip.dto.rights.ReceiveRecordByOrderCodeQry;
import com.iqiyi.vip.enums.TaskTypeEnum;
import com.iqiyi.vip.threadpool.AbstractTask;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * 退单回调交易，通知权益已开通
 *
 * <AUTHOR>
 * @date 2023/9/20 10:13
 */
@Data
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class RefundOrderCallBackTask extends AbstractTask {

    private ReceiveRecordByOrderCodeQry query;

    @Override
    protected boolean execute() {
        try {
            log.info("[start][query:{}]", query);
            OrderService orderService = (OrderService) ApplicationContextUtil.getBean("orderService");
            boolean callSuccess = orderService.refundOrderCallBack(query);
            log.info("[end][query:{},result:{}]", query, callSuccess);
            return callSuccess;
        } catch (Exception e) {
            log.error("OrderCallBackTask, query:{}", query, e);
            return false;
        }
    }

    @SneakyThrows
    @Override
    public void deserialize(String s) throws IllegalArgumentException {
        ObjectMapper objectMapper = new ObjectMapper();
        query = objectMapper.readValue(s, ReceiveRecordByOrderCodeQry.class);
    }

    @SneakyThrows
    @Override
    public String serialize() {
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.writeValueAsString(query);
    }

    @Override
    public int getDefaultPoolType() {
        return TaskTypeEnum.REFUND_ORDER_CALL_BACK.getType();
    }
}
