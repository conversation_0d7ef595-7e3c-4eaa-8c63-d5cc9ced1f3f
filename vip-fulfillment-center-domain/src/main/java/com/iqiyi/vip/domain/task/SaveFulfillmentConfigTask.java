package com.iqiyi.vip.domain.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.iqiyi.vip.context.ApplicationContextUtil;
import com.iqiyi.vip.domain.fulfillment.service.FulfillmentEventService;
import com.iqiyi.vip.dto.act.ActMsg;
import com.iqiyi.vip.enums.TaskTypeEnum;
import com.iqiyi.vip.threadpool.AbstractTask;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/8/1 18:07
 */
@Data
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class SaveFulfillmentConfigTask extends AbstractTask {

    private String skuId;
    private ActMsg actMsg;

    @Override
    protected boolean execute() {
        try {
            log.info("[start][skuId:{},actMsg:{}]", skuId, actMsg);
            FulfillmentEventService fulfillmentEventService = (FulfillmentEventService) ApplicationContextUtil
                .getBean("fulfillmentEventService");
            boolean dealRes = fulfillmentEventService.save(skuId, actMsg);
            log.info("[end][skuId:{}]", skuId);
            return dealRes;
        } catch (Exception e) {
            log.error("[Exception] skuId:{},actMsg:{}", skuId, actMsg, e);
            return false;
        }
    }

    @SneakyThrows
    @Override
    public void deserialize(String data) throws IllegalArgumentException {
        SaveFulfillmentConfigTask taskInfo = JSON.parseObject(data, SaveFulfillmentConfigTask.class);
        this.skuId = taskInfo.getSkuId();
        this.actMsg = taskInfo.getActMsg();
    }

    @SneakyThrows
    @Override
    public String serialize() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("skuId", skuId);
        jsonObject.put("actMsg", actMsg);
        return jsonObject.toJSONString();
    }

    @Override
    public int getDefaultPoolType() {
        return TaskTypeEnum.SAVE_FULFILLMENT_CONFIG.getType();
    }
}
