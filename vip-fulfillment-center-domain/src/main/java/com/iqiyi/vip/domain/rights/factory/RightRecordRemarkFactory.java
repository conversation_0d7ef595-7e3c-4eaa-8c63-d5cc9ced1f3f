package com.iqiyi.vip.domain.rights.factory;

import com.google.common.collect.Maps;
import com.iqiyi.vip.domain.rights.entity.ReceiveRightRecord;
import com.iqiyi.vip.dto.rights.OpenVmcRightsRes;
import com.iqiyi.vip.dto.rights.OpenVmcRightsRes.OpenVipExtend;
import com.iqiyi.vip.dto.rights.OrderDeliverReq;
import com.iqiyi.vip.dto.rights.RightRecordRemarkDTO;
import com.iqiyi.vip.utils.DateUtils;
import com.iqiyi.vip.utils.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * remark工厂
 *
 * <AUTHOR>
 * @date 2024/10/29 17:56
 */
@Slf4j
public class RightRecordRemarkFactory {

    public static RightRecordRemarkDTO buildRemark(OpenVmcRightsRes openVmcRightsRes) {
        RightRecordRemarkDTO rightRecordRemarkDTO = new RightRecordRemarkDTO();
        rightRecordRemarkDTO.setContentId(openVmcRightsRes.getContentId());
        rightRecordRemarkDTO.setStartTime(openVmcRightsRes.getStartTime());
        rightRecordRemarkDTO.setEndTime(openVmcRightsRes.getEndTime());
        rightRecordRemarkDTO.setRenewFlag(openVmcRightsRes.getRenewFlag());
        OpenVipExtend extend = openVmcRightsRes.getExtend();
        if (null != extend) {
            rightRecordRemarkDTO.setBeforeDeadline(extend.getBeforeDeadline());
            rightRecordRemarkDTO.setBeforePaidSign(extend.getBeforePaidSign());
        }
        return rightRecordRemarkDTO;
    }

    /**
     * 构建回调交易请求参数
     */
    public static OrderDeliverReq buildOrderDeliverReq(ReceiveRightRecord rightRecord, String remark) {
        OrderDeliverReq req = new OrderDeliverReq();
        req.setOrderCode(rightRecord.getOrderCode());
        if (StringUtils.isBlank(remark)) {
            return req;
        }
        RightRecordRemarkDTO rightRecordRemarkDTO = JacksonUtil.json2objFailIgnore(remark, RightRecordRemarkDTO.class);
        if (null == rightRecordRemarkDTO) {
            return req;
        }
        req.setContentId(rightRecordRemarkDTO.getContentId());
        req.setStartTime(DateUtils.getTimestamp(rightRecordRemarkDTO.getStartTime()));
        req.setDeadline(DateUtils.getTimestamp(rightRecordRemarkDTO.getEndTime()));
        req.setRenewalsFlag(rightRecordRemarkDTO.getRenewFlag());

        Map refer = Maps.newHashMap();
        refer.put("beforeDeadline", rightRecordRemarkDTO.getBeforeDeadline());
        refer.put("beforePaidSign", rightRecordRemarkDTO.getBeforePaidSign());
        //TODO 只有实物类商品才需要回写以下信息
        if (StringUtils.isNotBlank(rightRecord.getCouponCode())) {
            refer.put("businessProperty.mallOrderCode", rightRecord.getCouponCode());
        }
        if (StringUtils.isNotBlank(rightRecord.getEncryptAccount())) {
            refer.put("businessProperty.addressCode", rightRecord.getEncryptAccount());
        }
        req.setRefer(refer);
        return req;
    }
}
