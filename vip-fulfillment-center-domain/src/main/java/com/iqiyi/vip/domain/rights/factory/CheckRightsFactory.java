package com.iqiyi.vip.domain.rights.factory;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;

import com.iqiyi.vip.domain.rights.entity.ReceiveRightRecord;
import com.iqiyi.vip.domain.rights.repository.ReceiveRecordRepository;
import com.iqiyi.vip.domain.sku.entity.Sku;
import com.iqiyi.vip.dto.rights.CheckSubSkuCanReceiveResVO;
import com.iqiyi.vip.dto.rights.ReceiveRecordByOrderCodeQry;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.SpuEnum;
import com.iqiyi.vip.enums.YesOrNoEnum;

/**
 * <AUTHOR>
 * @date 2024/8/14 16:00
 */
@Slf4j
public class CheckRightsFactory {

    public static CheckSubSkuCanReceiveResVO buildCheckSubSkuCanReceiveResVO(Long uid, Sku sku, ReceiveRecordRepository receiveRecordRepository) {
        CheckSubSkuCanReceiveResVO checkSubSkuCanReceiveResVO = buildCheckSubSkuCanReceiveResVO(sku);
        if (null == sku) {
            return checkSubSkuCanReceiveResVO;
        }
        ReceiveRecordByOrderCodeQry receiveRecordByorderCodeQry = ReceiveRecordByOrderCodeQry.builder()
            .uid(uid)
            .promotionCode(sku.getRightRecordPromotionCode())
            .build();
        List<ReceiveRightRecord> canReceiveRights = receiveRecordRepository.queryCanReceiveOrderList(receiveRecordByorderCodeQry);
        checkSubSkuCanReceiveResVO.setCheckCodeMsgEnum(CollectionUtils.isEmpty(canReceiveRights) ? CodeEnum.ERROR_NO_RIGHT : CodeEnum.SUCCESS);
        return checkSubSkuCanReceiveResVO;
    }

    public static CheckSubSkuCanReceiveResVO buildCheckSubSkuCanReceiveResVOResetPackSku(String reqSkuId, Sku sku, Map<String, CheckSubSkuCanReceiveResVO> skuId2CheckResMap) {
        if (null != sku && SpuEnum.isPackageSpu(sku.getSpuId())) {
            //打包购商品返回结果需要根据子商品信息重置
            CodeEnum packSkuCheckCodeMsgEnum = CodeEnum.ERROR_NO_RIGHT;
            Integer packSkuChangeAccount = sku.getSpecAttributes().getChangeAccount();
            Integer accountType = sku.getSpecAttributes().getAccountType();
            List<CheckSubSkuCanReceiveResVO> subSkuCanReceiveList = Lists.newArrayList();
            for (Sku subSku : sku.getSubSkuList()) {
                CheckSubSkuCanReceiveResVO subSkuCanReceiveResVO = skuId2CheckResMap.get(subSku.getSkuId());
                subSkuCanReceiveList.add(subSkuCanReceiveResVO);
                //有一个子商品的权益可以领该打包购商品就是可领的
                if (null != subSkuCanReceiveResVO && CodeEnum.SUCCESS.getCode().equals(subSkuCanReceiveResVO.getCheckCode())) {
                    packSkuCheckCodeMsgEnum = CodeEnum.SUCCESS;
                }
                //有一个子商品是可以切换账号的，打包购商品就是可以切换的
                if (YesOrNoEnum.YES.getValue().equals(subSkuCanReceiveResVO.getChangeAccount())) {
                    packSkuChangeAccount = subSkuCanReceiveResVO.getChangeAccount();
                }
                if (null != subSkuCanReceiveResVO.getAccountType()) {
                    accountType = subSkuCanReceiveResVO.getAccountType();
                }
            }

            //构建打包购商品返回结果
            CheckSubSkuCanReceiveResVO packSkuCheckRes = buildCheckSubSkuCanReceiveResVO(sku);
            packSkuCheckRes.setChangeAccount(null == packSkuChangeAccount ? YesOrNoEnum.NO.getValue() : packSkuChangeAccount);
            packSkuCheckRes.setAccountType(accountType);
            packSkuCheckRes.setCheckCodeMsgEnum(packSkuCheckCodeMsgEnum);
            packSkuCheckRes.setSubSkuCheckList(subSkuCanReceiveList);
            return packSkuCheckRes;
        } else {
            return skuId2CheckResMap.get(reqSkuId);
        }
    }

    public static CheckSubSkuCanReceiveResVO buildCheckSubSkuCanReceiveResVO(Sku sku) {
        CheckSubSkuCanReceiveResVO checkSubSkuCanReceiveResVO = new CheckSubSkuCanReceiveResVO();
        checkSubSkuCanReceiveResVO.setSkuId(sku.getSkuId());
        if (null == sku) {
            checkSubSkuCanReceiveResVO.setCheckCodeMsgEnum(CodeEnum.ERROR_SKU_NULL);
            return checkSubSkuCanReceiveResVO;
        }
        checkSubSkuCanReceiveResVO.setChangeAccount(sku.getSpecAttributes().getChangeAccount());
        checkSubSkuCanReceiveResVO.setAccountType(sku.getSpecAttributes().getAccountType());
        return checkSubSkuCanReceiveResVO;
    }
}
