package com.iqiyi.vip.domain.rights.service;

import java.util.List;

import com.iqiyi.vip.domain.order.entity.CheckOrderCanTerminateDetail;
import com.iqiyi.vip.domain.rights.entity.FulfillOrderAggregate;
import com.iqiyi.vip.domain.rights.entity.OpenOrderRights;
import com.iqiyi.vip.domain.rights.entity.ReceiveRightRecord;
import com.iqiyi.vip.domain.rights.entity.TerminateOrderAggregate;
import com.iqiyi.vip.domain.sku.entity.SkuBeforeBuyCheckAggregate;
import com.iqiyi.vip.dto.base.BaseListResponse;
import com.iqiyi.vip.dto.base.BaseResponse;
import com.iqiyi.vip.dto.order.CheckOrderCanTerminateDetailRes;
import com.iqiyi.vip.dto.rights.CheckSkuCanReceiveReq;
import com.iqiyi.vip.dto.rights.CheckSkuCanReceiveResVO;
import com.iqiyi.vip.dto.rights.CollectUserInfoReq;
import com.iqiyi.vip.dto.rights.CollectUserInfoRes;
import com.iqiyi.vip.dto.rights.KeFuUserRightsListQry;
import com.iqiyi.vip.dto.rights.KeFuUserRightsListRes;
import com.iqiyi.vip.dto.rights.PartnerRightsResponse;
import com.iqiyi.vip.dto.rights.ReceiveByOrderCodeReq;
import com.iqiyi.vip.dto.rights.ReceiveBySkuIdReq;
import com.iqiyi.vip.dto.rights.NotifyRefundedReq;
import com.iqiyi.vip.dto.rights.UserRightsCountByPackSkuIdRes;
import com.iqiyi.vip.dto.rights.UserRightsCountQryByPackSkuId;
import com.iqiyi.vip.dto.rights.UserRightsListQry;

/**
 * <AUTHOR>
 * @date 2023/9/11 11:22
 */
public interface RightsService {

    void doExtAfterInsertRightsRecord(ReceiveRightRecord receiveRightRecord);

    BaseResponse<PartnerRightsResponse> openRights(OpenOrderRights openRights);

    BaseResponse<PartnerRightsResponse> terminateRights(TerminateOrderAggregate terminateOrderAggregate, ReceiveRightRecord paidRightRecord);

    BaseResponse manualOpenRights(FulfillOrderAggregate fulfillOrderAggregate);

    BaseResponse openRightsFailThenManual(FulfillOrderAggregate fulfillOrderAggregate);

    /**
     * 查询是否有可领数据
     */
    CheckSkuCanReceiveResVO checkSkuCanReceiveBatch(CheckSkuCanReceiveReq req);

    /**
     * 领 按skuId
     */
    BaseResponse receiveBySkuId(ReceiveBySkuIdReq req);

    /**
     * 领 按订单号
     */
    BaseResponse receiveByOrderCode(ReceiveByOrderCodeReq req);

    BaseListResponse userRightsList(UserRightsListQry qry);

    List<KeFuUserRightsListRes> keFuUserRightsList(KeFuUserRightsListQry qry);

    BaseListResponse userRightsCount(UserRightsListQry qry);

    List<UserRightsCountByPackSkuIdRes> userRightsCountByPackSkuId(UserRightsCountQryByPackSkuId qry);

    CheckOrderCanTerminateDetailRes checkCanTerminateRights(CheckOrderCanTerminateDetail req);

    void batchAskPartnerCanBuy(Long uid, List<SkuBeforeBuyCheckAggregate> skuBeforeBuyCheckAggregateList);

    /**
     * 用户个保信息采集
     */
    List<CollectUserInfoRes> collectUserInfo(CollectUserInfoReq req);

    void notifyRefunded(NotifyRefundedReq req);
}
