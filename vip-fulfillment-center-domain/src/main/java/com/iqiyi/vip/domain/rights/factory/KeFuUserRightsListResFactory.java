package com.iqiyi.vip.domain.rights.factory;

import org.apache.commons.lang3.StringUtils;

import java.util.Map;

import com.iqiyi.vip.domain.rights.entity.ReceiveRightRecord;
import com.iqiyi.vip.domain.sku.entity.Sku;
import com.iqiyi.vip.dto.rights.KeFuUserRightsListRes;
import com.iqiyi.vip.enums.ReceiveRightRecordRefundStatus;
import com.iqiyi.vip.enums.ReceiveStatusEnum;
import com.iqiyi.vip.enums.SendTypeEnum;
import com.iqiyi.vip.enums.SpuCategoryEnum;
import com.iqiyi.vip.enums.YesOrNoEnum;
import com.iqiyi.vip.utils.FieldUtils;

/**
 * <AUTHOR>
 * @date 2024/7/4 16:11
 */
public class KeFuUserRightsListResFactory {

    public static KeFuUserRightsListRes buildNoRights(Long uid, String orderCode) {
        return KeFuUserRightsListRes.builder().uid(uid).orderCode(orderCode).sendRight(YesOrNoEnum.NO.getDesc()).build();
    }

    public static KeFuUserRightsListRes buildHasRights(ReceiveRightRecord receiveRightRecord, Map<String, Sku> skuId2SkuMap) {
        String skuName = "";
        if (StringUtils.isNotBlank(receiveRightRecord.getSkuId())) {
            Sku sku = skuId2SkuMap.get(receiveRightRecord.getSkuId());
            if (null != sku) {
                skuName = sku.getSkuName();
            }
            //之前的代扣订单，【第三方权益】这里都是空的，客服退费SOP会去识别第三方权益是否有领取，如果领取了就会像截图上这样，显示不满足退费，会有些影响客服判断（我们的流程会根据用户订单是否符合退费条件，提供挽留方案）
            if (SpuCategoryEnum.VIP.getCategoryId().equals(sku.getCatalogId())) {
                return KeFuUserRightsListResFactory.buildNoRights(receiveRightRecord.getUid(), receiveRightRecord.getOrderCode());
            }
        }
        String account = receiveRightRecord.getEncryptAccount();
        if (StringUtils.isNotBlank(account) && StringUtils.isNumeric(account)) {
            account = account.replaceAll("(\\d{3})\\d{6}(\\d{2})", "$1******$2");
        }
        String sendRes = FieldUtils.desensitizeNum(receiveRightRecord.getSendRes());
        if (StringUtils.isNotBlank(sendRes)) {
            sendRes = sendRes.replace("\"", "'");
        }
        ReceiveStatusEnum receiveStatusEnum = ReceiveRightRecordFactory.getReceiveStatusMsg(receiveRightRecord);

        String refundRes = FieldUtils.desensitizeNum(receiveRightRecord.getRefundRes());
        if (StringUtils.isNotBlank(refundRes)) {
            refundRes = refundRes.replace("\"", "'");
        }
        return KeFuUserRightsListRes.builder()
            .uid(receiveRightRecord.getUid())
            .orderCode(receiveRightRecord.getOrderCode())
            .sendRight(YesOrNoEnum.YES.getDesc())
            .skuId(receiveRightRecord.getSkuId())
            .skuName(skuName)
            .receiveStatus(null == receiveStatusEnum ? null : receiveStatusEnum.getDesc())
            .account(account)
            .receiveTime(receiveRightRecord.getReceiveTime())
            .receiveMsg(sendRes)
            .receiveType(SendTypeEnum.ofDesc(receiveRightRecord.getReceiveType()))
            .receiveDeadlineTime(receiveRightRecord.getReceiveDeadlineTime())
            .refundStatus(ReceiveRightRecordRefundStatus.ofDesc(receiveRightRecord.getRefundStatus()))
            .refundTime(receiveRightRecord.getRefundTime())
            .refundInfo(refundRes)
            .build();
    }
}
