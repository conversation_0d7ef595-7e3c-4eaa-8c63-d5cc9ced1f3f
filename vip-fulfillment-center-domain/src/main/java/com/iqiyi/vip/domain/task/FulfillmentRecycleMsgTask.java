/**
 *
 */
package com.iqiyi.vip.domain.task;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.iqiyi.vip.context.ApplicationContextUtil;
import com.iqiyi.vip.domain.fulfillment.service.FulfillmentMqService;
import com.iqiyi.vip.domain.rights.entity.ReceiveRightRecord;
import com.iqiyi.vip.dto.order.RefundOrderInfo;
import com.iqiyi.vip.dto.rights.PartnerRightsResponse;
import com.iqiyi.vip.enums.TaskTypeEnum;
import com.iqiyi.vip.threadpool.AbstractTask;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 权益取消消息发送任务
 */
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class FulfillmentRecycleMsgTask extends AbstractTask {

    private ReceiveRightRecord paidRightRecord;

    private RefundOrderInfo refundOrderInfo;

    private PartnerRightsResponse res;

    @Override
    public int getDefaultPoolType() {
        return TaskTypeEnum.RIGHTS_RECYCLE_MSG.getType();
    }

    @Override
    protected boolean execute() {
        try {
            log.info("[start][record: {}, refund: {}, res: {}]", paidRightRecord, refundOrderInfo, res);

            FulfillmentMqService fulfillmentMqService = (FulfillmentMqService) ApplicationContextUtil.getBean("fulfillmentMqService");
            fulfillmentMqService.sendRecycleRightsMsg(paidRightRecord, refundOrderInfo, res);

            log.info("[end][record: {}, refund: {}, res: {}]", paidRightRecord, refundOrderInfo, res);
            return true;
        } catch (Exception e) {
            log.error("[exception][record: {}, refund: {}, res: {}]", paidRightRecord, refundOrderInfo, res, e);
            return false;
        }
    }

    @Override
    public void deserialize(String data) throws IllegalArgumentException {
        Gson gson = new Gson();
        JsonObject json = gson.fromJson(data, JsonObject.class);

        res = gson.fromJson(json.get("res").getAsString(), PartnerRightsResponse.class);
        paidRightRecord = gson.fromJson(json.get("paidRightRecord").getAsString(), ReceiveRightRecord.class);
        refundOrderInfo = gson.fromJson(json.get("refundOrderInfo").getAsString(), RefundOrderInfo.class);
    }

    @Override
    public String serialize() {
        Gson gson = new Gson();
        JsonObject json = new JsonObject();
        json.addProperty("res", gson.toJson(res));
        json.addProperty("paidRightRecord", gson.toJson(paidRightRecord));
        json.addProperty("refundOrderInfo", gson.toJson(refundOrderInfo));
        return json.toString();
    }
}
