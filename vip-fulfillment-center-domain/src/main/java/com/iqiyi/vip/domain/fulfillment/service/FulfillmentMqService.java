/**
 *
 */
package com.iqiyi.vip.domain.fulfillment.service;

import com.iqiyi.vip.domain.rights.entity.OpenOrderRights;
import com.iqiyi.vip.domain.rights.entity.ReceiveRightRecord;
import com.iqiyi.vip.dto.order.RefundOrderInfo;
import com.iqiyi.vip.dto.rights.PartnerRightsResponse;

/**
 * 履约消息Service
 */
public interface FulfillmentMqService {

    /**
     * 发送权益开通通知消息
     */
    void sendOpenRightsMsg(ReceiveRightRecord receiveRightRecord, OpenOrderRights openRights, PartnerRightsResponse partnerRightsResponse);

    /**
     * 发送权益回收通知消息
     */
    void sendRecycleRightsMsg(ReceiveRightRecord receiveRightRecord, RefundOrderInfo refundOrderInfo, PartnerRightsResponse partnerRightsResponse);
}
