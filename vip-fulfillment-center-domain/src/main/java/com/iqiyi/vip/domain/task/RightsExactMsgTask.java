package com.iqiyi.vip.domain.task;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.iqiyi.vip.context.ApplicationContextUtil;
import com.iqiyi.vip.domain.exact.service.ExactMqService;
import com.iqiyi.vip.domain.rights.entity.ReceiveRightRecord;
import com.iqiyi.vip.domain.rights.factory.ReceiveRightRecordFactory;
import com.iqiyi.vip.domain.rights.repository.ReceiveRecordRepository;
import com.iqiyi.vip.dto.exact.RightsExactTaskMsg;
import com.iqiyi.vip.enums.ReceiveStatusEnum;
import com.iqiyi.vip.enums.TaskTypeEnum;
import com.iqiyi.vip.threadpool.AbstractTask;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * 给精准触达发权益领取提醒
 *
 * <AUTHOR>
 * @date 2023/9/20 10:26
 */
@Data
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class RightsExactMsgTask extends AbstractTask {

    private RightsExactTaskMsg msg;

    @Override
    protected boolean execute() {
        try {
            log.info("[start][msg:{}]", msg);
            ReceiveRecordRepository receiveRecordRepository = (ReceiveRecordRepository) ApplicationContextUtil.getBean("receiveRecordRepositoryImpl");
            //验证记录是否已领取，已领取无需再给用户发信息提醒领取
            ReceiveRightRecord receiveRightRecord = receiveRecordRepository.queryByOrderCode(ReceiveRightRecordFactory.buildByMsg(msg));
            if (null != receiveRightRecord && null != receiveRightRecord.getReceiveStatus()
                && ReceiveStatusEnum.RECEIVED.getStatus() == receiveRightRecord.getReceiveStatus()) {
                log.info("[received ignore send draw sms][msg:{}]", msg);
                return true;
            }

            ExactMqService exactMqService = (ExactMqService) ApplicationContextUtil.getBean("exactMqService");
            exactMqService.sendRightsMsg(msg);
            log.info("[end][msg:{}]", msg);
            return true;
        } catch (Exception e) {
            log.error("RightsExactMsgTask, msg:{}", msg, e);
            return false;
        }
    }

    @SneakyThrows
    @Override
    public void deserialize(String data) throws IllegalArgumentException {
        ObjectMapper objectMapper = new ObjectMapper();
        msg = objectMapper.readValue(data, RightsExactTaskMsg.class);
    }

    @SneakyThrows
    @Override
    public String serialize() {
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.writeValueAsString(msg);
    }

    @Override
    public int getDefaultPoolType() {
        return TaskTypeEnum.RIGHTS_EXACT_MSG.getType();
    }
}
