package com.iqiyi.vip.domain.task;

import com.iqiyi.vip.context.ApplicationContextUtil;
import com.iqiyi.vip.domain.inventory.service.InventoryService;
import com.iqiyi.vip.dto.order.OrderPaidMsg;
import com.iqiyi.vip.enums.RetryEnum;
import com.iqiyi.vip.enums.TaskTypeEnum;
import com.iqiyi.vip.threadpool.AbstractTask;
import com.iqiyi.vip.utils.JacksonUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * 扣减库存任务
 *
 * <AUTHOR>
 * @date 2024/1/4 13:50
 */
@Data
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class DeductStockTask extends AbstractTask {

    private OrderPaidMsg orderPaidMsg;

    @Override
    protected boolean execute() {
        try {
            log.info("[start][orderInfo:{}]", orderPaidMsg);
            InventoryService inventoryService = (InventoryService) ApplicationContextUtil.getBean("inventoryService");
            RetryEnum retryEnum = inventoryService.deductStock(orderPaidMsg);
            log.info("[end][orderInfo:{},retryEnum:{}]", orderPaidMsg, retryEnum);
            return retryEnum.dealSuc();
        } catch (Exception e) {
            log.error("[Exception] orderInfo:{}", orderPaidMsg, e);
            return false;
        }
    }

    @SneakyThrows
    @Override
    public void deserialize(String data) throws IllegalArgumentException {
        orderPaidMsg = JacksonUtil.json2obj(data, OrderPaidMsg.class);
    }

    @SneakyThrows
    @Override
    public String serialize() {
        return JacksonUtil.writeValueAsString(orderPaidMsg);
    }

    @Override
    public int getDefaultPoolType() {
        return TaskTypeEnum.DEDUCT_STOCK.getType();
    }
}
