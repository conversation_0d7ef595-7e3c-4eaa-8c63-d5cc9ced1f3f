package com.iqiyi.vip.domain.ruleengine.entity;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;


@Data
@Builder
@Slf4j
public class RuleCheckReqDto {
    private String uid = "";
    private String deviceId = "";
    private String limitCode;
    private String account;
    private String addAmount;

    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("uid", uid);
        map.put("deviceId", deviceId);
        map.put("limitCode", limitCode);
        map.put("account", account);
        map.put("addAmount", addAmount);
        map.put("messageId", UUID.randomUUID().toString().replaceAll("-", ""));
        map.put("businessCode", "vip-fulfillment-center");
        return map;
    }
}
