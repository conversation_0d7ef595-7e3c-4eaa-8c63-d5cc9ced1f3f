package com.iqiyi.vip.domain.constraint.entity;

import lombok.Data;
import org.apache.commons.lang.time.DateUtils;

import java.util.Date;

import com.iqiyi.vip.enums.ReceiveDeadlineTypeEnum;
import com.iqiyi.vip.enums.SendTypeEnum;

/**
 * <AUTHOR>
 * @date 2023/9/8 15:11
 */
@Data
public class Constraint {

    /**
     * 权益充值时机（买完即充，需要领取）
     */
    private SendTypeEnum sendTypeEnum;
    /**
     * 领取截止时间类型
     */
    private ReceiveDeadlineTypeEnum receiveDeadlineTypeEnum;
    private Integer receiveDeadlineRelativeDays;
    private Date receiveDeadlineAbsolute;

    public Date getReceiveDeadline(Date payTime) {
        // 没有领取截止时间限制
        if (null == receiveDeadlineTypeEnum || ReceiveDeadlineTypeEnum.NO_LIMIT.equals(receiveDeadlineTypeEnum)) {
            return null;
        }
        // 相对时间限制
        if (ReceiveDeadlineTypeEnum.RELATIVE_LIMIT.equals(receiveDeadlineTypeEnum)) {
            return DateUtils.addDays(payTime, receiveDeadlineRelativeDays);
        }
        // 绝对时间限制
        return receiveDeadlineAbsolute;
    }
}
