package com.iqiyi.vip.domain.task;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.iqiyi.vip.context.ApplicationContextUtil;
import com.iqiyi.vip.domain.viptag.service.VipTagService;
import com.iqiyi.vip.dto.viptag.VipTagSaveReq;
import com.iqiyi.vip.enums.TaskTypeEnum;
import com.iqiyi.vip.threadpool.AbstractTask;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * 更新用户标签信息
 *
 * <AUTHOR>
 * @date 2023/9/20 11:23
 */
@Data
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class VipTagTask extends AbstractTask {

    private VipTagSaveReq req;

    @Override
    protected boolean execute() {
        try {
            log.info("[start][req:{}]", req);
            VipTagService vipTagService = (VipTagService) ApplicationContextUtil.getBean("vipTagService");
            boolean dealSuccess = vipTagService.saveCustomTag(req);
            log.info("[end][req:{},res:{}]", req, dealSuccess);
            return dealSuccess;
        } catch (Exception e) {
            log.error("VipTagTask, req:{}", req, e);
            return false;
        }
    }

    @SneakyThrows
    @Override
    public void deserialize(String data) throws IllegalArgumentException {
        ObjectMapper objectMapper = new ObjectMapper();
        req = objectMapper.readValue(data, VipTagSaveReq.class);
    }

    @SneakyThrows
    @Override
    public String serialize() {
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.writeValueAsString(req);
    }

    @Override
    public int getDefaultPoolType() {
        return TaskTypeEnum.VIP_TAG.getType();
    }
}
