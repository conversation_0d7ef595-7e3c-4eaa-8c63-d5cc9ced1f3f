package com.iqiyi.vip.domain.limit.service;

import java.util.List;

import com.iqiyi.vip.domain.rights.entity.FulfillOrderAggregate;
import com.iqiyi.vip.domain.sku.entity.SkuBeforeBuyCheckAggregate;

/**
 * <AUTHOR>
 * @date 2023/9/1 16:51
 */
public interface LimitService {

    void checkPurchaseLimit(FulfillOrderAggregate fulfillOrderAggregate);

    void checkPurchaseLimit(Long uid, List<SkuBeforeBuyCheckAggregate> skuBeforeBuyCheckAggregateList);
}
