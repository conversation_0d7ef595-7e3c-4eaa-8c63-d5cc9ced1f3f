package com.iqiyi.vip.domain.sku.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.iqiyi.vip.domain.gift.entity.Gift;
import com.iqiyi.vip.domain.spufulfillment.entity.SpuFulfillmentConfig;
import com.iqiyi.vip.dto.sku.SkuBeforeBuyCheckDetailRes;

/**
 * <AUTHOR>
 * @date 2023/11/29 16:20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SkuBeforeBuyCheckAggregate {

    private String skuId;
    private Integer skuAmount;

    private Sku sku;
    private Gift gift;
    private SpuFulfillmentConfig spuFulfillmentConfig;
    private boolean needNextCheck;
    private String actCenterRuleCode;

    /**
     * 校验结果
     */
    private SkuBeforeBuyCheckDetailRes checkResult;

}
