package com.iqiyi.vip.domain.sku.factory;

import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.iqiyi.vip.domain.rights.entity.FulfillOrderAggregate;
import com.iqiyi.vip.domain.sku.entity.Sku;
import com.iqiyi.vip.domain.sku.entity.SkuBeforeBuyCheckAggregate;
import com.iqiyi.vip.dto.sku.SkuBeforeBuyCheckDetailRes;
import com.iqiyi.vip.dto.sku.SkuCheckReq;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.enums.SpuEnum;
import com.iqiyi.vip.log.Constants;

/**
 * <AUTHOR>
 * @date 2023/9/28 10:52
 */
public class SkuFactory {

    public static List<SkuBeforeBuyCheckAggregate> buildSkuBeforeBuyCheckAggregateList(Map<String, Sku> skuId2SkuMap, List<SkuCheckReq> skuCheckList) {
        //构建商品校验聚合对象
        List<SkuBeforeBuyCheckAggregate> skuBeforeBuyCheckAggregateList = Lists.newArrayList();
        for (SkuCheckReq skuCheckReq : skuCheckList) {
            if (StringUtils.isBlank(skuCheckReq.getSkuId())) {
                continue;
            }
            if (null == skuCheckReq.getSkuAmount()) {
                skuCheckReq.setSkuAmount(1);
            }
            skuBeforeBuyCheckAggregateList.addAll(buildSkuBeforeBuyCheckAggregate(skuId2SkuMap.get(skuCheckReq.getSkuId()), skuCheckReq));
        }
        skuBeforeBuyCheckAggregateList.stream().filter(s -> null != s).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuBeforeBuyCheckAggregateList)) {
            return Collections.EMPTY_LIST;
        }
        return skuBeforeBuyCheckAggregateList;
    }

    public static List<SkuBeforeBuyCheckAggregate> buildSkuBeforeBuyCheckAggregate(Sku sku, SkuCheckReq skuCheckReq) {
        String reqSkuId = skuCheckReq.getSkuId();
        SkuBeforeBuyCheckAggregate skuBeforeBuyCheckAggregate = new SkuBeforeBuyCheckAggregate();
        skuBeforeBuyCheckAggregate.setSkuId(reqSkuId);
        skuBeforeBuyCheckAggregate.setSkuAmount(skuCheckReq.getSkuAmount());
        skuBeforeBuyCheckAggregate.setActCenterRuleCode(skuCheckReq.getActCenterRuleCode());
        if (null == sku) {
            skuBeforeBuyCheckAggregate.setCheckResult(new SkuBeforeBuyCheckDetailRes(skuCheckReq, CodeEnum.ERROR_SKU_INVALID));
            skuBeforeBuyCheckAggregate.setNeedNextCheck(false);
            return Lists.newArrayList(skuBeforeBuyCheckAggregate);
        }
        skuBeforeBuyCheckAggregate.setNeedNextCheck(true);
        skuBeforeBuyCheckAggregate.setSku(sku);
        List<SkuBeforeBuyCheckAggregate> skuAgList = Lists.newArrayList(skuBeforeBuyCheckAggregate);
        List<SkuBeforeBuyCheckAggregate> packSubSkuCheckList = buildPackSkuBeforeBuyCheckAggregate(sku);
        if (CollectionUtils.isNotEmpty(packSubSkuCheckList)) {
            skuAgList.addAll(packSubSkuCheckList);
        }
        return skuAgList;
    }

    /**
     * 构建打包购商品的子商品校验
     */
    public static List<SkuBeforeBuyCheckAggregate> buildPackSkuBeforeBuyCheckAggregate(Sku sku) {
        if (!SpuEnum.isPackageSpu(sku.getSpuId())) {
            return Collections.EMPTY_LIST;
        }
        if (CollectionUtils.isEmpty(sku.getSubSkuList())) {
            return Collections.EMPTY_LIST;
        }
        List<SkuBeforeBuyCheckAggregate> subSkuBeforeBuyCheckAggregateList = Lists.newArrayList();
        for (Sku subSku : sku.getSubSkuList()) {
            SkuCheckReq subSkuReq = new SkuCheckReq();
            subSkuReq.setSkuId(subSku.getSkuId());
            subSkuReq.setSkuAmount(Constants.PACK_SUB_SKU_DEFAULT_SKU_AMOUNT);
            subSkuBeforeBuyCheckAggregateList.addAll(buildSkuBeforeBuyCheckAggregate(subSku, subSkuReq));
        }
        return subSkuBeforeBuyCheckAggregateList;
    }

    public static List<SkuBeforeBuyCheckAggregate> filterNextCheckList(List<SkuBeforeBuyCheckAggregate> skuBeforeBuyCheckAggregateList) {
        if (CollectionUtils.isEmpty(skuBeforeBuyCheckAggregateList)) {
            return Collections.EMPTY_LIST;
        }
        return skuBeforeBuyCheckAggregateList.stream()
            .filter(entry -> entry.isNeedNextCheck())
            .collect(Collectors.toList());
    }

    public static List<SkuBeforeBuyCheckAggregate> buildByFulfillOrder(FulfillOrderAggregate fulfillOrderAggregate){
        SkuCheckReq skuCheckReq = new SkuCheckReq();
        skuCheckReq.setSkuId(fulfillOrderAggregate.getSku().getSkuId());
        skuCheckReq.setSkuAmount(fulfillOrderAggregate.getOrderInfo().getSkuAmount());
        List<SkuBeforeBuyCheckAggregate> skuBeforeBuyCheckAggregateList =buildSkuBeforeBuyCheckAggregate(fulfillOrderAggregate.getSku(),skuCheckReq);
        for (SkuBeforeBuyCheckAggregate skuBeforeBuyCheckAggregate : skuBeforeBuyCheckAggregateList){
            skuBeforeBuyCheckAggregate.setGift(fulfillOrderAggregate.getGift());
            skuBeforeBuyCheckAggregate.setSpuFulfillmentConfig(fulfillOrderAggregate.getSpuFulfillmentConfig());
        }
        return skuBeforeBuyCheckAggregateList;
    }
}
