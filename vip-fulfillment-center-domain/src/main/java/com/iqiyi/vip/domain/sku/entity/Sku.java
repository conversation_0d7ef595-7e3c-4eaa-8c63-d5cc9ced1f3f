package com.iqiyi.vip.domain.sku.entity;

import com.iqiyi.vip.domain.gift.entity.SkuGiftDTO;
import com.iqiyi.vip.domain.spufulfillment.entity.SpuFulfillmentConfig;
import com.iqiyi.vip.domain.spufulfillment.repository.SpuFulfillmentRepository;
import com.iqiyi.vip.enums.*;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * SKU 信息
 *
 * <AUTHOR>
 * @date 2023/8/1 13:59
 */
@Data
public class Sku {

    private String skuId;

    private String skuName;

    private String spuId;

    private Integer status;

    /**
     * 类目
     */
    private String catalogId;

    private Date startTime;

    private Date endTime;

    private String updator;

    private SkuSpecAttributes specAttributes;

    private List<Sku> subSkuList;

    public String getRightRecordPromotionCode() {
        //第三方商品有promotionCode
        if (SpuEnum.THIRD_RIGHTS_SPU.getSpuId().equals(spuId)) {
            return specAttributes.getPromotionCode();
        }
        //其他品类都用skuId作为商品标识，例如，代金券品类商品没有promotionCode，只能用skuId作为promotionCode
        return skuId;
    }

    public SkuGiftDTO obtainSkuGiftDTO(SpuFulfillmentRepository spuFulfillmentRepository) {
        if (StringUtils.isBlank(skuId) || StringUtils.isBlank(spuId)) {
            return null;
        }
        //由于福利品类于2023年底下线废弃，所以新履约系统不考虑为福利品类履约
        if (SpuEnum.BENEFIT_SPU.getSpuId().equals(spuId)) {
            return null;
        }
        //跳转类不履约
        if (null != specAttributes.getGiftType() && GiftTypeEnum.PAGE_REDIRECT.getType() == specAttributes.getGiftType()) {
            return null;
        }
        //会员商品
        String giftCode = VipGiftCodeMappingEnum.getGiftCodeByCategoryId(catalogId);
        if (StringUtils.isNotBlank(giftCode)) {
            return SkuGiftDTO.builder().giftCode(giftCode).build();
        }

        //根据[spu]直接履约
        SpuFulfillmentConfig spuFulfillmentConfig = spuFulfillmentRepository.queryBySpuId(spuId);
        if (null != spuFulfillmentConfig) {
            return SkuGiftDTO.builder().giftCode(spuFulfillmentConfig.getGiftCode()).spuFulfillmentConfig(spuFulfillmentConfig).build();
        }
        //根据[权益发放类型]履约
        if (null != specAttributes.getGiftType()) {
            //权益直充
            if (GiftTypeEnum.DIRECT_CHARGE.getType() == specAttributes.getGiftType()) {
                return SkuGiftDTO.builder().giftCode(specAttributes.getGiftCode()).build();
            } else {
                SpuFulfillmentConfig spuFulfillmentConfigByGiftType = spuFulfillmentRepository.queryByGiftType(specAttributes.getGiftType());
                return null == spuFulfillmentConfigByGiftType
                    ? null : SkuGiftDTO.builder().giftCode(spuFulfillmentConfigByGiftType.getGiftCode())
                    .spuFulfillmentConfig(spuFulfillmentConfigByGiftType).build();
            }
        }
        return null;
    }

    public String obtainGiftCode(SpuFulfillmentRepository spuFulfillmentRepository) {
        SkuGiftDTO skuGiftDTO = obtainSkuGiftDTO(spuFulfillmentRepository);
        return null == skuGiftDTO ? null : skuGiftDTO.getGiftCode();
    }

    public boolean limitInventory() {
        if (null == specAttributes) {
            return false;
        }
        return specAttributes.getTotalInventory() != null ? true : false;
    }

    /**
     * 是否预付费
     *
     * @return ture:是，false:否
     */
    public boolean isPrePaid() {
        return YesOrNoEnum.YES.getValue().equals(specAttributes.getPrePaid());
    }

    /**
     * 是否需要通知标签服务
     */
    public boolean needNotifyTag() {
        return SpuCategoryEnum.PARTNER.getCategoryId().equals(catalogId);
    }

    /**
     * 是否是兑换升级
     *
     * @return
     */
    public boolean isExchangeUpgrade() {
        return specAttributes != null && SkuIdentifierEnum.FREE_EXCHANGE_UPGRADE.getValue().equals(specAttributes.getSkuIdentifier());
    }

    public String getDisplayName() {
        if (specAttributes == null) {
            return "";
        }
        return specAttributes.getDisplayName();
    }
}