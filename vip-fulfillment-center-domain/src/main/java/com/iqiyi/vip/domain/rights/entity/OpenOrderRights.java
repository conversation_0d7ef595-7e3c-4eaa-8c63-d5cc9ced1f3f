package com.iqiyi.vip.domain.rights.entity;

import lombok.Data;

import com.iqiyi.vip.domain.gift.entity.Gift;
import com.iqiyi.vip.domain.settlement.entity.Settlement;
import com.iqiyi.vip.domain.sku.entity.Sku;
import com.iqiyi.vip.domain.spufulfillment.entity.SpuFulfillmentConfig;
import com.iqiyi.vip.dto.rights.OpenPartnerRightsOrderReq;

/**
 * 开通订单权益
 *
 * <AUTHOR>
 * @date 2023/9/25 14:36
 */
@Data
public class OpenOrderRights {

    private Long uid;
    private String promotionCode;

    private Sku sku;
    private OpenPartnerRightsOrderReq orderInfo;
    private Settlement settlement;
    private String receiveAccount;
    private Gift gift;
    private SpuFulfillmentConfig spuFulfillmentConfig;
    /**
     * 是否需要回调交易
     */
    private Boolean needCallBackOrder;

}
