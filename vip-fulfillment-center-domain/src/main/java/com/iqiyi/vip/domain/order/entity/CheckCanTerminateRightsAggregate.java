package com.iqiyi.vip.domain.order.entity;

import lombok.Data;

import java.util.List;
import java.util.Map;

import com.iqiyi.vip.domain.sku.entity.Sku;
import com.iqiyi.vip.dto.order.CheckOrderCanTerminateDetailReq;

/**
 * <AUTHOR>
 * @date 2023/9/28 9:22
 */
@Data
public class CheckCanTerminateRightsAggregate {

    private List<CheckOrderCanTerminateDetailReq> orderList;
    private Map<String, Sku> skuId2SkuMap;

}
