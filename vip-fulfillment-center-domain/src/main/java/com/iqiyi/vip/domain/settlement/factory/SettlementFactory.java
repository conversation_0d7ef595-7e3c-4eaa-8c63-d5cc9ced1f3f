package com.iqiyi.vip.domain.settlement.factory;

import com.iqiyi.vip.domain.settlement.entity.Settlement;
import com.iqiyi.vip.domain.sku.entity.Sku;
import com.iqiyi.vip.domain.sku.entity.SkuSpecAttributes;
import com.iqiyi.vip.enums.SettlementTypeEnum;

/**
 * <AUTHOR>
 * @date 2023/9/11 21:00
 */
public class SettlementFactory {

    public static Settlement buildSettlement(Sku sku) {
        SkuSpecAttributes specAttributes = sku.getSpecAttributes();
        if (null == specAttributes) {
            return null;
        }

        Settlement settlement = new Settlement();
        settlement.setSettleCalculateType(SettlementTypeEnum.of(specAttributes.getSettleCalculateType()));
        settlement.setSettlePercent(specAttributes.getSettlePercent());
        settlement.setSettlePrice(specAttributes.getSettlePrice());
        return settlement;
    }
}
