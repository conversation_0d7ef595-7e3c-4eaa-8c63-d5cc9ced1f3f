package com.iqiyi.vip.domain.task;

import com.iqiyi.vip.context.ApplicationContextUtil;
import com.iqiyi.vip.domain.fulfillment.service.FulfillmentEventService;
import com.iqiyi.vip.dto.base.BaseResponse;
import com.iqiyi.vip.dto.order.PaidOrderInfo;
import com.iqiyi.vip.enums.TaskTypeEnum;
import com.iqiyi.vip.enums.YesOrNoEnum;
import com.iqiyi.vip.threadpool.AbstractTask;
import com.iqiyi.vip.utils.JacksonUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * VMC履约重试task
 *
 * <AUTHOR>
 * @date 2023/9/18 14:43
 */
@Data
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class VipFulfillOrderTask extends AbstractTask {

    private PaidOrderInfo orderInfo;

    @Override
    protected boolean execute() {
        try {
            log.info("[start][orderInfo:{}]", orderInfo);
            FulfillmentEventService fulfillmentEventService = (FulfillmentEventService) ApplicationContextUtil
                .getBean("fulfillmentEventService");
            BaseResponse response = fulfillmentEventService.fulfillOrder(orderInfo, null);

            boolean dealSuccess = true;
            if (null == response || YesOrNoEnum.YES.getValue().equals(response.getRetry())) {
                dealSuccess = false;
            }
            log.info("[end][orderInfo:{},dealSuccess:{}]", orderInfo, dealSuccess);
            return dealSuccess;
        } catch (Exception e) {
            log.error("FulfillmentTask, orderInfo:{}", orderInfo, e);
            return false;
        } finally {
            //递增重试次数
            incrRetryCount(orderInfo);
        }
    }

    @SneakyThrows
    @Override
    public void deserialize(String s) throws IllegalArgumentException {
        orderInfo = JacksonUtil.json2obj(s, PaidOrderInfo.class);
    }

    @SneakyThrows
    @Override
    public String serialize() {
        return JacksonUtil.writeValueAsString(orderInfo);
    }

    @Override
    public int getDefaultPoolType() {
        return TaskTypeEnum.VIP_FULFILL_ORDER.getType();
    }

    private void incrRetryCount(PaidOrderInfo orderInfo) {
        log.info("Incr or init retry count. orderCode:{}, currentCount:{}", orderInfo.getOrderCode(), orderInfo.getCurrentRetryCount());
        if (orderInfo.getCurrentRetryCount() == null) {
            orderInfo.setCurrentRetryCount(1);
        } else {
            orderInfo.setCurrentRetryCount(orderInfo.getCurrentRetryCount()+1);
        }
    }
}
