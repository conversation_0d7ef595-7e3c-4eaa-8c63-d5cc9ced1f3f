package com.iqiyi.vip.domain.limit.entity;

import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.Date;
import java.util.List;

import com.iqiyi.vip.domain.rights.entity.ReceiveRightRecord;
import com.iqiyi.vip.enums.PurchaseLimitTimeUnitEnum;
import com.iqiyi.vip.enums.ReceiveRightRecordRefundStatus;
import com.iqiyi.vip.enums.ReceiveStatusEnum;
import com.iqiyi.vip.utils.DateUtils;

/**
 * <AUTHOR>
 * @date 2023/9/8 14:44
 */
@Slf4j
@Data
public class PurchaseLimit {

    private Integer limitTimeType;

    private Integer limitMaxCount;

    public Boolean checkPurchaseLimit(List<ReceiveRightRecord> userRecordList) {
        if (CollectionUtils.isEmpty(userRecordList) || null == limitMaxCount) {
            return true;
        }
        List<ReceiveRightRecord> costTimesRightRecords = Lists.newArrayList();
        for (ReceiveRightRecord rightRecord : userRecordList) {
            //待领
            if (ReceiveStatusEnum.PENDING_RECEIVE.getStatus() == rightRecord.getReceiveStatus()) {
                if (ReceiveRightRecordRefundStatus.UN_REFUND.getStatus() == rightRecord.getRefundStatus()
                    && DateUtils.verifyReceiveDeadlineTime(rightRecord.getReceiveDeadlineTime())) {
                    costTimesRightRecords.add(rightRecord);
                }
            }
            //已领
            if (ReceiveStatusEnum.RECEIVED.getStatus() == rightRecord.getReceiveStatus()) {
                if (ReceiveRightRecordRefundStatus.UN_REFUND.getStatus() == rightRecord.getRefundStatus()) {
                    if (null != limitTimeType && PurchaseLimitTimeUnitEnum.NATURAL_YEAR.getType() == limitTimeType) {
                        //自然年限制，已领，且是今年领的，才算耗费资格
                        if (DateUtils.isSameYear(new Date(), rightRecord.getReceiveTime())) {
                            costTimesRightRecords.add(rightRecord);
                        }
                    } else {
                        costTimesRightRecords.add(rightRecord);
                    }
                }
            }
        }
        if (CollectionUtils.isEmpty(costTimesRightRecords)) {
            return true;
        }
        if (costTimesRightRecords.size() >= limitMaxCount) {
            log.info("Reach the max num of records, uid:{}, record num:{}, limit max count:{}, records:{}",
                costTimesRightRecords.get(0).getUid(), costTimesRightRecords.size(), limitMaxCount, costTimesRightRecords);
            return false;
        }
        return true;
    }
}
