package com.iqiyi.vip.domain.fulfillment.factory;

import com.iqiyi.vip.domain.fulfillment.entity.FulfillmentConfig;
import com.iqiyi.vip.domain.sku.entity.Sku;
import com.iqiyi.vip.dto.act.ActMsg;
import com.iqiyi.vip.dto.act.SubActExtraMsg;
import com.iqiyi.vip.dto.act.SubActMsg;
import com.iqiyi.vip.dto.act.SubActSubSkuSendMsg;
import com.iqiyi.vip.enums.ActTypeEnum;
import com.iqiyi.vip.utils.DateUtils;
import com.iqiyi.vip.enums.ReceiveDeadlineTypeEnum;

/**
 * <AUTHOR>
 * @date 2023/7/31 16:21
 */
public class FulfillmentFactory {

    public static FulfillmentConfig buildFulfillmentConfig(Sku sku) {
        FulfillmentConfig fulfillmentConfig = new FulfillmentConfig();
        fulfillmentConfig.setSkuId(sku.getSkuId());
        fulfillmentConfig.setSpuId(sku.getSpuId());
        fulfillmentConfig.setName(sku.getSkuName());
        fulfillmentConfig.setActType(ActTypeEnum.SKU.getType());
        fulfillmentConfig.setActCode("");
        return fulfillmentConfig;
    }

    public static FulfillmentConfig buildFulfillmentConfig(ActMsg actMsg) {
        FulfillmentConfig fulfillmentConfig = new FulfillmentConfig();

        SubActMsg subActConfig = actMsg.getSubActList().get(0);
        SubActExtraMsg subActExtraData = subActConfig.getSubExtraData();

        fulfillmentConfig.setSkuId(subActExtraData.getSubSkuId());
        fulfillmentConfig.setSpuId(subActExtraData.getSubSpuId());
        fulfillmentConfig.setActCode(actMsg.getActCode());
        fulfillmentConfig.setName(actMsg.getName());
        fulfillmentConfig.setActType(ActTypeEnum.getTypeByActCenterType(actMsg.getType()));
        fulfillmentConfig.setVersion(actMsg.getVersion());
        fulfillmentConfig.setOperator(actMsg.getUpdateUser());

        //子商品发放方式
        SubActSubSkuSendMsg subSkuSendInfo = subActExtraData.getSubSkuSendInfo();
        fulfillmentConfig.setSendType(subSkuSendInfo.getConstraintSendType());
        fulfillmentConfig.setReceiveDeadlineRelativeDays(subSkuSendInfo.getReceiveDeadlineRelativeDays());
        fulfillmentConfig.setReceiveDeadlineAbsolute(DateUtils.longToDate(subSkuSendInfo.getReceiveDeadlineAbsolute()));
        fulfillmentConfig.setReceiveDeadlineType(ReceiveDeadlineTypeEnum.getReceiveDeadlineType(subSkuSendInfo.getReceiveDeadlineRelativeDays()));
        fulfillmentConfig.setRefundPartnerType(subSkuSendInfo.getRefundPartnerType());
        fulfillmentConfig.setRefundPartnerDeadline(DateUtils.longToDate(subSkuSendInfo.getRefundPartnerDeadline()));

        return fulfillmentConfig;
    }
}
