package com.iqiyi.vip.domain.order.factory;

import com.google.common.collect.Lists;
import com.iqiyi.vip.domain.order.entity.CheckCanTerminateRightsAggregate;
import com.iqiyi.vip.domain.order.entity.CheckOrderCanTerminateDetail;
import com.iqiyi.vip.domain.sku.entity.Sku;
import com.iqiyi.vip.domain.sku.repository.SkuRepository;
import com.iqiyi.vip.dto.order.CheckOrderCanTerminateDetailReq;
import com.iqiyi.vip.dto.order.CheckOrderCanTerminateRightsReq;
import com.iqiyi.vip.dto.sku.SkuBatchQuery;
import com.iqiyi.vip.enums.CodeEnum;
import com.iqiyi.vip.utils.AssertUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/9/28 9:25
 */
public class CheckCanTerminateFactory {

    public static CheckCanTerminateRightsAggregate buildCheckCanTerminateRightsAggregate(CheckOrderCanTerminateRightsReq req, SkuRepository skuRepository) {
        AssertUtils.notNull(req, CodeEnum.ERROR_PARAM);
        AssertUtils.notEmpty(req.getOrderList(), CodeEnum.ERROR_PARAM);

        CheckCanTerminateRightsAggregate aggregate = new CheckCanTerminateRightsAggregate();
        aggregate.setOrderList(req.getOrderList());
        String skuIds = req.getOrderList().stream().map(CheckOrderCanTerminateDetailReq::getSkuId).collect(Collectors.joining(","));
        Map<String, Sku> skuId2SkuMap = skuRepository.batchQueryFromCache(SkuBatchQuery.builder().skuIds(skuIds).onlyQueryValid(false).build());
        AssertUtils.mapNotEmpty(skuId2SkuMap, CodeEnum.ERROR_PARAM);
        aggregate.setSkuId2SkuMap(skuId2SkuMap);
        return aggregate;
    }

    public static List<CheckOrderCanTerminateDetail> buildCheckOrderCanTerminateDetail(CheckCanTerminateRightsAggregate aggregate) {
        Map<String, Sku> skuId2SkuMap = aggregate.getSkuId2SkuMap();
        List<CheckOrderCanTerminateDetail> orderList = Lists.newArrayList();
        for (CheckOrderCanTerminateDetailReq req : aggregate.getOrderList()) {
            CheckOrderCanTerminateDetail detail = new CheckOrderCanTerminateDetail();
            Sku sku = skuId2SkuMap.get(req.getSkuId());
            AssertUtils.notNull(sku, CodeEnum.ERROR_PARAM);
            detail.setSku(sku);
            detail.setReq(req);
            detail.setPromotionCode(sku.getRightRecordPromotionCode());
            orderList.add(detail);
        }
        return orderList;
    }
}
