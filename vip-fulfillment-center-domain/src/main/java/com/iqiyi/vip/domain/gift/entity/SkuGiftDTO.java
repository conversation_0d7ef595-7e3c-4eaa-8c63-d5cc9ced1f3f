package com.iqiyi.vip.domain.gift.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.iqiyi.vip.domain.spufulfillment.entity.SpuFulfillmentConfig;

/**
 * <AUTHOR>
 * @date 2023/10/31 14:49
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SkuGiftDTO {

    private String giftCode;
    private SpuFulfillmentConfig spuFulfillmentConfig;
}
