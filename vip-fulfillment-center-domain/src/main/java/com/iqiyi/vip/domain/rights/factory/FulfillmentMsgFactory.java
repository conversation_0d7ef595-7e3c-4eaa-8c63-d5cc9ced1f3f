/**
 *
 */
package com.iqiyi.vip.domain.rights.factory;

import com.iqiyi.vip.domain.rights.entity.OpenOrderRights;
import com.iqiyi.vip.domain.rights.entity.ReceiveRightRecord;
import com.iqiyi.vip.dto.order.RefundOrderInfo;
import com.iqiyi.vip.dto.rights.FulfillmentMsg;
import com.iqiyi.vip.dto.rights.PartnerRightsResponse;

/**
 * 履约消息工厂
 */
public class FulfillmentMsgFactory {

    /** 开通权益 */
    private static final String TYPE_GRANT = "GRANT";

    /** 回收权益 */
    private static final String TYPE_RECYCLE = "RECYCLE";

    /**
     * 创建履约开通权益消息
     * @return
     */
    public static FulfillmentMsg buildGrantRightsMsg(ReceiveRightRecord receiveRightRecord, OpenOrderRights openRights, PartnerRightsResponse partnerRightsResponse) {
        return FulfillmentMsg.builder()
            .orderCode(receiveRightRecord.getOrderCode())
            .type(TYPE_GRANT)
            .uid(receiveRightRecord.getUid())
            .skuId(receiveRightRecord.getSkuId())
            .status(receiveRightRecord.getSendStatus())
            .res(partnerRightsResponse.getSendRes())
            .account(receiveRightRecord.getEncryptAccount())
            .couponCode(receiveRightRecord.getCouponCode())
            .orderTime((receiveRightRecord.getOrderTime() != null) ? receiveRightRecord.getOrderTime().getTime() : null)
            .sendTime((receiveRightRecord.getSendTime() != null) ? receiveRightRecord.getSendTime().getTime() : null)
            .tradeNo((openRights.getOrderInfo() != null) ? openRights.getOrderInfo().getTradeNo() : null)
            .tradeCode((openRights.getOrderInfo() != null) ? openRights.getOrderInfo().getTradeCode() : null)
            .platformCode((openRights.getOrderInfo() != null) ? openRights.getOrderInfo().getPlatformCode() : null)
            .sellScene((openRights.getOrderInfo() != null) ? openRights.getOrderInfo().getSellScene() : null)
            .build();
    }

    /**
     * 创建履约回收权益消息
     * @return
     */
    public static FulfillmentMsg buildRecycleRightsMsg(ReceiveRightRecord receiveRightRecord, RefundOrderInfo refundOrderInfo, PartnerRightsResponse partnerRightsResponse) {
        return FulfillmentMsg.builder()
            .orderCode(receiveRightRecord.getOrderCode())
            .type(TYPE_RECYCLE)
            .uid(receiveRightRecord.getUid())
            .skuId(receiveRightRecord.getSkuId())
            .status(receiveRightRecord.getSendStatus())
            .res(partnerRightsResponse.getSendRes())
            .account(receiveRightRecord.getEncryptAccount())
            .couponCode(receiveRightRecord.getCouponCode())
            .orderTime((receiveRightRecord.getOrderTime() != null) ? receiveRightRecord.getOrderTime().getTime() : null)
            .sendTime((receiveRightRecord.getSendTime() != null) ? receiveRightRecord.getSendTime().getTime() : null)
            .refundTime((receiveRightRecord.getRefundTime() != null) ? receiveRightRecord.getRefundTime().getTime() : null)
            .tradeNo(refundOrderInfo.getTradeNo())
            .tradeCode(refundOrderInfo.getTradeCode())
            .platformCode(refundOrderInfo.getPlatformCode())
            .sellScene(refundOrderInfo.getSellScene())
            .build();
    }
}
