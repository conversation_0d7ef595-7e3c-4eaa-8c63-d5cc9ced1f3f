package com.iqiyi.vip.controller;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;

import com.iqiyi.vip.domain.rights.service.RightsService;
import com.iqiyi.vip.domain.sku.repository.SkuRepository;
import com.iqiyi.vip.dto.rights.KeFuUserRightsListQry;
import com.iqiyi.vip.dto.sku.SkuByAttrQry;
import com.iqiyi.vip.dto.sku.SkuByAttrQry.CommonAttrQueryParam;

/**
 * <AUTHOR>
 * @date 2024/7/4 14:12
 */
@Slf4j
public class RightsTest extends BaseTest {

    @Resource
    private RightsService rightsService;
    @Resource
    private SkuRepository skuRepository;

    @Test
    public void testQuery() {
        KeFuUserRightsListQry qry = new KeFuUserRightsListQry();
        qry.setUid(1480441855L);
        qry.setOrderCodeList("20220719152614_202403221210,20220719152614_202403221218,20220719152614_202403280953");
        log.info("res:{}", rightsService.keFuUserRightsList(qry));
    }

    @Test
    public void testQuerySkuAttr() {
        SkuByAttrQry skuByAttrQry = new SkuByAttrQry();
        skuByAttrQry.setSkuId(new CommonAttrQueryParam("sku_513349804882713665", true));
        skuRepository.queryByAttribute(skuByAttrQry);
    }
}
