package com.iqiyi.vip.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import com.iqiyi.vip.aspect.WebLog;
import com.iqiyi.vip.domain.fulfillment.service.FulfillmentEventService;
import com.iqiyi.vip.domain.rights.service.RightsService;
import com.iqiyi.vip.domain.tool.service.ToolService;
import com.iqiyi.vip.dto.base.BaseListResponse;
import com.iqiyi.vip.dto.base.BaseResponse;
import com.iqiyi.vip.dto.rights.BatchFulfillOrderReq;
import com.iqiyi.vip.dto.rights.KeFuUserRightsListQry;
import com.iqiyi.vip.enums.CodeEnum;

/**
 * 权益相关接口
 *
 * <AUTHOR>
 * @date 2023/9/25 10:12
 */
@Api("权益接口")
@Slf4j
@RestController
@RequestMapping(value = "rights")
public class RightsController {

    @Resource
    private RightsService rightsService;
    @Resource
    private FulfillmentEventService fulfillmentEventService;
    @Resource
    private ToolService toolService;

    @WebLog
    @ApiOperation(value = "客服系统-查询用户权益记录", httpMethod = "GET")
    @RequestMapping(value = "/keFuUserRightsList")
    public BaseListResponse keFuUserRightsList(KeFuUserRightsListQry qry) {
        return new BaseListResponse(CodeEnum.SUCCESS, rightsService.keFuUserRightsList(qry));
    }

    @WebLog
    @ApiOperation(value = "客服-根据orderCode重新履约", httpMethod = "GET")
    @RequestMapping(value = "/fulfillOrder")
    public BaseResponse fulfillOrder(String orderCode) {
        return fulfillmentEventService.fulfillOrder(orderCode);
    }

    @WebLog
    @ApiOperation(value = "交易监控到权益未到账调用该接口重试履约", httpMethod = "POST")
    @RequestMapping(value = "/batchFulfillOrder")
    public BaseResponse batchFulfillOrder(@RequestBody BatchFulfillOrderReq req) {
        return BaseResponse.createSuccessResponse(toolService.batchFulfillOrder(req));
    }
}
