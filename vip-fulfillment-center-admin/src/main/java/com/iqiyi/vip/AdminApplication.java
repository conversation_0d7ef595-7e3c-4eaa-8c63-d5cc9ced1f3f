package com.iqiyi.vip;

import com.alicp.jetcache.anno.config.EnableMethodCache;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.metrics.jdbc.DataSourcePoolMetricsAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;

import com.iqiyi.solar.config.client.spring.annotation.EnableCloudConfig;

/**
 * <AUTHOR>
 */
@EnableCloudConfig
@EnableCaching
@EnableMethodCache(basePackages = "com.iqiyi")
@SpringBootApplication(exclude = {DataSourcePoolMetricsAutoConfiguration.class})
public class AdminApplication {

    public static void main(String[] args) {
        SpringApplication s = new SpringApplication(AdminApplication.class);
        s.setAllowCircularReferences(Boolean.TRUE);
        s.run(args);
    }

}
