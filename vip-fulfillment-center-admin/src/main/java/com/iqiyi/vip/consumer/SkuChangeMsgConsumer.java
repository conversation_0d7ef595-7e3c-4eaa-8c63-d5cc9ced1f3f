package com.iqiyi.vip.consumer;

import com.alibaba.fastjson.JSON;
import com.iqiyi.vip.aspect.WebLog;
import com.iqiyi.vip.domain.fulfillment.repository.FulfillmentTaskRepository;
import com.iqiyi.vip.dto.sku.SkuMsg;
import com.iqiyi.vip.enums.SkuChangeTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 监听sku变化
 *
 * <AUTHOR>
 * @date 2023/7/31 10:17
 */
@Slf4j
@Service
@RocketMQMessageListener(
    nameServer = "${rocketmq.sku.nameServer}",
    topic = "${rocketmq.sku.topic}",
    consumerGroup = "${rocketmq.sku.consumerGroup}",
    token = "${rocketmq.sku.token}")
public class SkuChangeMsgConsumer implements RocketMQListener<String> {

    @Resource
    private FulfillmentTaskRepository fulfillmentTaskRepository;

    @WebLog
    @Override
    public void onMessage(String originalMsg) {
        long startTime = System.currentTimeMillis();
        try {
            SkuMsg skuMsg = JSON.parseObject(originalMsg, SkuMsg.class);
            log.info("[start][originalMsg:{}]", originalMsg);
            if (match(skuMsg)) {
                fulfillmentTaskRepository.saveFulfillmentConfigFailRetry(skuMsg.getSkuId(), null);
            }
        } catch (Exception e) {
            log.error("[dealMsg Exception][originalMsg:{}]", originalMsg, e);
            throw e;
        } finally {
            log.info("[end][originalMsg:{},cost:{}]", originalMsg, System.currentTimeMillis() - startTime);
        }
    }

    private boolean match(SkuMsg skuMsg) {
        if (null != skuMsg && null != skuMsg.getChangeType()) {
            if (SkuChangeTypeEnum.SKU_CREATE.getType().equals(skuMsg.getChangeType())
                || SkuChangeTypeEnum.SKU_UPDATE.getType().equals(skuMsg.getChangeType())) {
                return true;
            }
        }
        return false;
    }
}
