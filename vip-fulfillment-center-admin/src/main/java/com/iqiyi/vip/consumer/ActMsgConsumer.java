package com.iqiyi.vip.consumer;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.iqiyi.vip.aspect.WebLog;
import com.iqiyi.vip.domain.fulfillment.repository.FulfillmentTaskRepository;
import com.iqiyi.vip.dto.act.ActMsg;

/**
 * 活动中心配置数据通知（数据生成方：奇悦2.0） http://wiki.qiyi.domain/pages/viewpage.action?pageId=542769996
 *
 * <AUTHOR>
 * @date 2023/8/3 14:59
 */
@Slf4j
@Service
@RocketMQMessageListener(
    nameServer = "${rocketmq.act.nameServer}",
    topic = "${rocketmq.act.topic}",
    consumerGroup = "${rocketmq.act.consumerGroup}",
    token = "${rocketmq.act.token}",
    selectorExpression = "${rocketmq.act.tag}")
public class ActMsgConsumer implements RocketMQListener<String> {

    @Resource
    private FulfillmentTaskRepository fulfillmentTaskRepository;

    @WebLog
    @Override
    public void onMessage(String originalMsg) {
        long startTime = System.currentTimeMillis();
        try {
            log.info("[start][originalMsg:{}]", originalMsg);
            ActMsg actMsg = JSON.parseObject(originalMsg, ActMsg.class);
            fulfillmentTaskRepository.saveFulfillmentConfigFailRetry(null, actMsg);
        } catch (Exception e) {
            log.error("[dealMsg Exception][originalMsg:{}]", originalMsg, e);
            throw e;
        } finally {
            log.info("[end][originalMsg:{}, cost:{}]", originalMsg, System.currentTimeMillis() - startTime);
        }
    }
}
