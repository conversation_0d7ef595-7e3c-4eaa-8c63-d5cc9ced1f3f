logging:
  config: classpath:logback-spring.xml

spring:
  profiles:
    active: test
  jackson:
    serialization:
      write-dates-as-timestamps: true
  cache:
    type: caffeine
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

mybatis:
  mapper-locations: classpath:mapper/*.xml,classpath:mapper/extend/*.xml,classpath:mapper/tidb/*.xml

rocketmq:
  producer:
    enabled: true

management:
  server:
    port: 8099
  endpoints:
    web:
      exposure:
        include: health,prometheus
v-eagle:
  request:
    publish-tp: true

log:
  home: /qke/log

#优雅下线开关，如果当前架构为NGINX->QLB->RS,则需要关闭优雅下线，默认true开启
#v.spring.cloud.service-registry.graceful-shutdown.enabled=false
v:
  spring:
    cloud:
      service-registry:
        graceful-shutdown:
          wait-timeout-millis: 20000
vip:
  kms:
    cache: hutool-fifo
    cache-capacity: 200
    cache-timeout: 60000
    close-parameter-tampering: true
    refresh-time: 1800
    table-for-encrypt:
      rights_record:
        close-def-encrypt-decrypt: false
        table-column: encrypt_mobile,encrypt_account,coupon_code
    valid-period: 300

async:
  task:
    execute: false
    execute.cluster: false
    table:
      name: fulfillment_center_async_task

# jetcache缓存配置
jetcache:
  penetrationProtect: true
  areaInCacheName: false
  statIntervalMinutes: 3
  hidePackages: com.alibaba
  local:
    default:
      type: caffeine
      keyConvertor: fastjson2
      limit: 10000
      expireAfterWriteInMillis: 1800000