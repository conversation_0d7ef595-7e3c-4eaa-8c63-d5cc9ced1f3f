spring:
  application:
    name: vip-fulfillment-center-admin-test
  shardingsphere:
    props:
      sql:
        show: true
    dataSource:
      names: tidb,old-ds,fulfill-ds0,fulfill-ds1,fulfill-ds2,fulfill-ds3,fulfill-ds4,fulfill-ds5,fulfill-ds6,fulfill-ds7,fulfill-ds8,fulfill-ds9,fulfill-ds10,fulfill-ds11,fulfill-ds12,fulfill-ds13,fulfill-ds14,fulfill-ds15
      tidb:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbc-url: *****************************************************************************************************************************************************************************************
        username: vipqa_iqiyi
        password: vipqa@IQIYI
        minimum-idle: 5
        maximum-pool-size: 20
        connection-timeout: 60000
        idle-timeout: 1800000
        max-lifetime: 1800000
        connection-test-query: SELECT 1
      old-ds:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbc-url: *******************************************************************************************************************************************************************************************************************
        username: vipqa_iqiyi
        password: vipqa@IQIYI
        minimum-idle: 5
        maximum-pool-size: 20
        connection-timeout: 60000
        idle-timeout: 1800000
        max-lifetime: 1800000
        connection-test-query: SELECT 1
      fulfill-ds0:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbc-url: *********************************************************************************************************************************************************************************************************
        username: vipqa_iqiyi
        password: vipqa@IQIYI
        minimum-idle: 5
        maximum-pool-size: 20
        connection-timeout: 60000
        idle-timeout: 1800000
        max-lifetime: 1800000
        connection-test-query: SELECT 1
      fulfill-ds1:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbc-url: *********************************************************************************************************************************************************************************************************
        username: vipqa_iqiyi
        password: vipqa@IQIYI
        minimum-idle: 5
        maximum-pool-size: 20
        connection-timeout: 60000
        idle-timeout: 1800000
        max-lifetime: 1800000
        connection-test-query: SELECT 1
      fulfill-ds2:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbc-url: *********************************************************************************************************************************************************************************************************
        username: vipqa_iqiyi
        password: vipqa@IQIYI
        minimum-idle: 5
        maximum-pool-size: 20
        connection-timeout: 60000
        idle-timeout: 1800000
        max-lifetime: 1800000
        connection-test-query: SELECT 1
      fulfill-ds3:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbc-url: *********************************************************************************************************************************************************************************************************
        username: vipqa_iqiyi
        password: vipqa@IQIYI
        minimum-idle: 5
        maximum-pool-size: 20
        connection-timeout: 60000
        idle-timeout: 1800000
        max-lifetime: 1800000
        connection-test-query: SELECT 1
      fulfill-ds4:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbc-url: *********************************************************************************************************************************************************************************************************
        username: vipqa_iqiyi
        password: vipqa@IQIYI
        minimum-idle: 5
        maximum-pool-size: 20
        connection-timeout: 60000
        idle-timeout: 1800000
        max-lifetime: 1800000
        connection-test-query: SELECT 1
      fulfill-ds5:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbc-url: *********************************************************************************************************************************************************************************************************
        username: vipqa_iqiyi
        password: vipqa@IQIYI
        minimum-idle: 5
        maximum-pool-size: 20
        connection-timeout: 60000
        idle-timeout: 1800000
        max-lifetime: 1800000
        connection-test-query: SELECT 1
      fulfill-ds6:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbc-url: *********************************************************************************************************************************************************************************************************
        username: vipqa_iqiyi
        password: vipqa@IQIYI
        minimum-idle: 5
        maximum-pool-size: 20
        connection-timeout: 60000
        idle-timeout: 1800000
        max-lifetime: 1800000
        connection-test-query: SELECT 1
      fulfill-ds7:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbc-url: *********************************************************************************************************************************************************************************************************
        username: vipqa_iqiyi
        password: vipqa@IQIYI
        minimum-idle: 5
        maximum-pool-size: 20
        connection-timeout: 60000
        idle-timeout: 1800000
        max-lifetime: 1800000
        connection-test-query: SELECT 1
      fulfill-ds8:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbc-url: *********************************************************************************************************************************************************************************************************
        username: vipqa_iqiyi
        password: vipqa@IQIYI
        minimum-idle: 5
        maximum-pool-size: 20
        connection-timeout: 60000
        idle-timeout: 1800000
        max-lifetime: 1800000
        connection-test-query: SELECT 1
      fulfill-ds9:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbc-url: *********************************************************************************************************************************************************************************************************
        username: vipqa_iqiyi
        password: vipqa@IQIYI
        minimum-idle: 5
        maximum-pool-size: 20
        connection-timeout: 60000
        idle-timeout: 1800000
        max-lifetime: 1800000
        connection-test-query: SELECT 1
      fulfill-ds10:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbc-url: **********************************************************************************************************************************************************************************************************
        username: vipqa_iqiyi
        password: vipqa@IQIYI
        minimum-idle: 5
        maximum-pool-size: 20
        connection-timeout: 60000
        idle-timeout: 1800000
        max-lifetime: 1800000
        connection-test-query: SELECT 1
      fulfill-ds11:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbc-url: **********************************************************************************************************************************************************************************************************
        username: vipqa_iqiyi
        password: vipqa@IQIYI
        minimum-idle: 5
        maximum-pool-size: 20
        connection-timeout: 60000
        idle-timeout: 1800000
        max-lifetime: 1800000
        connection-test-query: SELECT 1
      fulfill-ds12:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbc-url: **********************************************************************************************************************************************************************************************************
        username: vipqa_iqiyi
        password: vipqa@IQIYI
        minimum-idle: 5
        maximum-pool-size: 20
        connection-timeout: 60000
        idle-timeout: 1800000
        max-lifetime: 1800000
        connection-test-query: SELECT 1
      fulfill-ds13:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbc-url: **********************************************************************************************************************************************************************************************************
        username: vipqa_iqiyi
        password: vipqa@IQIYI
        minimum-idle: 5
        maximum-pool-size: 20
        connection-timeout: 60000
        idle-timeout: 1800000
        max-lifetime: 1800000
        connection-test-query: SELECT 1
      fulfill-ds14:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbc-url: **********************************************************************************************************************************************************************************************************
        username: vipqa_iqiyi
        password: vipqa@IQIYI
        minimum-idle: 5
        maximum-pool-size: 20
        connection-timeout: 60000
        idle-timeout: 1800000
        max-lifetime: 1800000
        connection-test-query: SELECT 1
      fulfill-ds15:
        type: com.zaxxer.hikari.HikariDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        jdbc-url: **********************************************************************************************************************************************************************************************************
        username: vipqa_iqiyi
        password: vipqa@IQIYI
        minimum-idle: 5
        maximum-pool-size: 20
        connection-timeout: 60000
        idle-timeout: 1800000
        max-lifetime: 1800000
        connection-test-query: SELECT 1
    sharding:
      default-data-source-name: old-ds
      tables:
        partner_rights_receive_record_tidb:
          actual-data-nodes: tidb.partner_rights_receive_record
        rights_record:
          actual-data-nodes: fulfill-ds$->{0..15}.rights_record_0$->{0..9},fulfill-ds$->{0..15}.rights_record_$->{10..99}
          database-strategy:
            standard:
              sharding-column: uid
              precise-algorithm-class-name: com.iqiyi.vip.config.DataBaseShardingAlgorithm
          table-strategy:
            standard:
              sharding-column: uid
              precise-algorithm-class-name: com.iqiyi.vip.config.RightsRecordShardingAlgorithm
  cache:
    caffeine:
      spec: initialCapacity=100,maximumSize=1000,expireAfterWrite=1s
  redis:
    host: ottvipqa.w.qiyi.redis
    port: 7677
    password: 7E8zdV7qLe3U
    timeout: 1000
    testOnBorrow: true
    testOnReturn: true
    jedis:
      pool:
        max-active: 2
        max-idle: 50
        max-wait: 1000
        min-idle: 20

server:
  port: 8080

# eureka config
eureka:
  instance:
    hostname: ${HOST}
    non-secure-port: 8080
    instance-id: ${HOST}:${spring.application.name}:${eureka.instance.non-secure-port}
    prefer-ip-address: false
    lease-renewal-interval-in-seconds: 3
    lease-expiration-duration-in-seconds: 5
  client:
    service-url:
      defaultZone: http://************:8080/eureka/
    healthcheck:
      enabled: false
  server:
    enable-self-preservation: false
hystrix:
  metrics:
    enabled: false
management:
  metrics:
    enable:
      all: false

ribbon:
  eureka:
    enabled: true
  eager-load:
    enabled: true
    clients: VIP-INFO-SERVER-TEST
  restclient:
    enabled: true
  ConnectTimeout: 3000
  ReadTimeout: 5000

vip:
  kms:
    access-key: C7430F340A60E2A697481AFAE4FB4AF2
    decrypt-type: sm4-decrypt-withlv
    def-sk: 05A4F51B31A2D8CB667D869873521F4E9D9E262B7AED18E5F984DA2D87C36407F6CAC36DC6F0B7478345D5F144FD7DF2
    encrypt-type: sm4-encrypt-withlv
    key-id: 59666BD5143635AB
    request:
      env: prod
    secret-key: 1A5FF53F218400973336FD5BEAA098AF

async:
  task:
    execute: false
    execute.cluster: false

rocketmq:
  sku:
    nameServer: iqiyi-cnhb4-2.vip-dba-center.dev001.rocketmq.qiyi.middle:9876;iqiyi-cnhb4-2.vip-dba-center.dev002.rocketmq.qiyi.middle:9876
    topic: vcc_data_change_notify_test
    consumerGroup: CG-vip-fulfillment-center
    token: CT-b3ed65b8-cb89-4325-b3b4-216a4c530b3c

  act:
    nameServer: service-cloud-dc-research-rocketmq-dev001-whdx.qiyi.virtual:9876;service-cloud-dc-research-rocketmq-dev003-whdx.qiyi.virtual:9876
    topic: vip_activity
    consumerGroup: CG-vip-fulfillment-center
    token: CT-ef25fc7b-f166-45d1-a33a-3e1b2eb86c7f
    tag: ADDITIONAL_CASHIER

  name-server: iqiyi-cnhb4-2.vip-dba-center.dev001.rocketmq.qiyi.middle:9876;iqiyi-cnhb4-2.vip-dba-center.dev002.rocketmq.qiyi.middle:9876
  producers[0]:
    topic: vip_partner_rights_draw_remind_test
    group: PG-vip-fulfillment-center_rights_remind_test
    token: PT-2f1a8c99-f3fc-4445-907e-ba7800ec91f7
  producers[1]:
    topic: FULFILLMENT_NOTIFY
    group: PG-vip-fulfillment-center_rights_notify
    token: PT-d092e970-538e-4ba8-9151-5fef16acbf56

sku:
  caller: vip-fulfillment-center
  http:
    url:
      prefix: http://vcc-test.vip.qiyi.domain/vip-commodity
  sign:
    key: 123456

query:
  order:
    signKey: f24d31f28ad963c6f48679c668cc092d
    channel: qiyue-api
    url: http://VIPTRADE-DATASERVICE-TEST

refund:
  eureka:
    host: http://VIPTRADE-REFUNDSERVICE-API-TEST
  sign:
    key: 123456