package com.iqiyi.vip;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

import com.iqiyi.vip.dto.rights.ReceiveRightRecordQryCon;
import com.iqiyi.vip.po.RightsRecord;
import com.iqiyi.vip.tidbmapper.TiDBReceiveRightRecordMapper;

/**
 * <AUTHOR>
 * @date 2023/8/3 10:41
 */
@Slf4j
public class TiDBTest extends BaseTest {

    @Resource
    private TiDBReceiveRightRecordMapper tiDBReceiveRightRecordMapper;

    @Test
    public void testQuery() {
        Long uid = 1400690042L;
        String orderCode = "20180921101239964081";
        List<RightsRecord> list = tiDBReceiveRightRecordMapper.selectByCon(ReceiveRightRecordQryCon.builder().uid(uid).build());
        log.info("{list:{}}", list);
    }
}
