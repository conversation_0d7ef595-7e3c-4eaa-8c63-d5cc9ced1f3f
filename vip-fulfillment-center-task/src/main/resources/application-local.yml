spring:
  application:
    name: vip-fulfillment-center-task-test
  datasource:
    mysql:
      driver-class-name: com.mysql.cj.jdbc.Driver
      initial-size: 5
      max-active: 20
      max-wait: 60000
      min-evictable-idle-time-millis: 1800000
      min-idle: 5
      password: vipqa@IQIYI
      pool-prepared-statements: true
      test-on-borrow: false
      test-on-return: false
      test-while-idle: true
      time-between-eviction-runs-millis: 60000
      type: com.alibaba.druid.pool.DruidDataSource
      jdbcUrl: *******************************************************************************************************************************************************************************************************************
      username: vipqa_iqiyi
      validation-query: SELECT 1
    tidb:
      driver-class-name: com.mysql.cj.jdbc.Driver
      initial-size: 5
      max-active: 20
      max-wait: 60000
      min-evictable-idle-time-millis: 1800000
      min-idle: 5
      password: vipqa@IQIYI
      pool-prepared-statements: true
      test-on-borrow: false
      test-on-return: false
      test-while-idle: true
      time-between-eviction-runs-millis: 60000
      type: com.alibaba.druid.pool.DruidDataSource
      jdbcUrl: *****************************************************************************************************************************************************************************************
      username: vipqa_iqiyi
      validation-query: SELECT 1

  cache:
    caffeine:
      spec: initialCapacity=100,maximumSize=1000,expireAfterWrite=1s
  redis:
    host: ottvipqa.w.qiyi.redis
    port: 7677
    password: 7E8zdV7qLe3U
    timeout: 1000
    testOnBorrow: true
    testOnReturn: true
    jedis:
      pool:
        max-active: 2
        max-idle: 50
        max-wait: 1000
        min-idle: 20

server:
  port: 8080

# qae eureka config
eureka:
  instance:
    hostname: 127.0.0.1
    ip-address: 127.0.0.1
    non-secure-port: 8080
    instance-id: 127.0.0.1:${spring.application.name}:8080
    prefer-ip-address: false
    lease-renewal-interval-in-seconds: 3
    lease-expiration-duration-in-seconds: 5
  client:
    service-url:
      defaultZone: http://test-eureka.vip.qiyi.domain:8080/eureka/
    healthcheck:
      enabled: true
  server:
    enable-self-preservation: false
hystrix:
  metrics:
    enabled: false
management:
  metrics:
    enable:
      all: false
  endpoint:
    health:
      enabled: false
      show-details: always
  health:
    db:
      enabled: false
    redis:
      enabled: false
    defaults:
      enabled: false
  info:
    git:
      mode: full

ribbon:
  eureka:
    enabled: true
  eager-load:
    enabled: false
    clients: VIP-INFO-SERVER-TEST
  restclient:
    enabled: true
  ConnectTimeout: 3000
  ReadTimeout: 5000

vip:
  kms:
    access-key: C7430F340A60E2A697481AFAE4FB4AF2
    decrypt-type: sm4-decrypt-withlv
    def-sk: 05A4F51B31A2D8CB667D869873521F4E9D9E262B7AED18E5F984DA2D87C36407F6CAC36DC6F0B7478345D5F144FD7DF2
    encrypt-type: sm4-encrypt-withlv
    key-id: 59666BD5143635AB
    request:
      env: prod
    secret-key: 1A5FF53F218400973336FD5BEAA098AF

zookeeper:
  password:
  url: cnhb4.public-test-baoding.dev002.zk.qiyi.middle:2181,cnhb4.public-test-baoding.dev003.zk.qiyi.middle:2181,cnhb4.public-test-baoding.dev005.zk.qiyi.middle:2181,cnhb4.public-test-baoding.dev001.zk.qiyi.middle:2181,cnhb4.public-test-baoding.dev004.zk.qiyi.middle:2181
  user:

async:
  task:
    execute: true
    execute.cluster: true

sku:
  caller: vip-fulfillment-center
  http:
    url:
      prefix: http://vcc-test.vip.qiyi.domain/vip-commodity
  sign:
    key: 123456

rocketmq:
  name-server: iqiyi-cnhb4-2.vip-dba-center.dev001.rocketmq.qiyi.middle:9876;iqiyi-cnhb4-2.vip-dba-center.dev002.rocketmq.qiyi.middle:9876
  producers[0]:
    topic: vip_partner_rights_draw_remind_test
    group: PG-vip-fulfillment-center_rights_remind_test
    token: PT-2f1a8c99-f3fc-4445-907e-ba7800ec91f7
  producers[1]:
    topic: FULFILLMENT_NOTIFY
    group: PG-vip-fulfillment-center_rights_notify
    token: PT-d092e970-538e-4ba8-9151-5fef16acbf56


query:
  order:
    signKey: f24d31f28ad963c6f48679c668cc092d

refund:
  eureka:
    host: http://VIPTRADE-REFUNDSERVICE-API-TEST
  sign:
    key: 123456