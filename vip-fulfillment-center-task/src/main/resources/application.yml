logging:
    config: classpath:logback-spring.xml

spring:
  profiles:
    active: test
  jackson:
    serialization:
      write-dates-as-timestamps: true
  cache:
    type: caffeine
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true

mybatis:
  mapper-locations: classpath:mapper/*.xml,classpath:mapper/extend/*.xml,classpath:mapper/tidb/*.xml

rocketmq:
  producer:
    enabled: true

management:
  server:
    port: 8099
  endpoints:
    web:
      exposure:
        include: health,prometheus
log:
  home: /qke/log

vip:
  kms:
    cache: hutool-fifo
    cache-capacity: 200
    cache-timeout: 60000
    close-parameter-tampering: true
    refresh-time: 1800
    table-for-encrypt:
      rights_record:
        close-def-encrypt-decrypt: false
        table-column: encrypt_mobile,encrypt_account,coupon_code
    valid-period: 300

async:
  task:
    table:
      name: fulfillment_center_async_task
    zookeeper:
      root: /vip-fulfillment-center-task
    serverName: ${HOST}
    save: true
    poolType:
      exe: 101,102,103,104,105,106,107,108,109,110,111,112
      failover: 4
    pool:
      101:
        name: SaveFulfillmentConfigTask
        corePoolSize: 5
        maximumPoolSize: 10
        workQueueNum: 1000
      102:
        name: FulfillOrderTask
        corePoolSize: 5
        maximumPoolSize: 10
        workQueueNum: 1000
      103:
        name: PaidOrderCallBackTask
        corePoolSize: 10
        maximumPoolSize: 20
        workQueueNum: 2000
      104:
        name: RightsExactMsgTask
        corePoolSize: 5
        maximumPoolSize: 10
        workQueueNum: 1000
      105:
        name: VipTagTask
        corePoolSize: 5
        maximumPoolSize: 10
        workQueueNum: 1000
      106:
        name: RefundOrderTask
        corePoolSize: 5
        maximumPoolSize: 10
        workQueueNum: 1000
      107:
        name: RefundOrderCallBackTask
        corePoolSize: 5
        maximumPoolSize: 10
        workQueueNum: 1000
      108:
        name: DeductStockTask
        corePoolSize: 5
        maximumPoolSize: 10
        workQueueNum: 1000
      109:
        name: FulfillmentGrantMsgTask
        corePoolSize: 5
        maximumPoolSize: 10
        workQueueNum: 1000
      110:
        name: FulfillmentRecycleMsgTask
        corePoolSize: 5
        maximumPoolSize: 10
        workQueueNum: 1000
      111:
        name: VipFulfillOrderTask
        corePoolSize: 30
        maximumPoolSize: 50
        workQueueNum: 2000

# jetcache缓存配置
jetcache:
  penetrationProtect: true
  areaInCacheName: false
  statIntervalMinutes: 3
  hidePackages: com.alibaba
  local:
    default:
      type: caffeine
      keyConvertor: fastjson2
      limit: 10000
      expireAfterWriteInMillis: 1800000