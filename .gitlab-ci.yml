image: docker-registry.qiyi.virtual/library/ci-env-jdk8-maven:3.6.0-iqiyi-2
sonarqube_master_job:
  stage: test
  only:
    - master
    - tags
  script:
    - mvn clean --batch-mode verify sonar:sonar -Dmaven.test.skip=true -Dsonar.host.url=http://sonarqube.cloud.qiyi.domain/ -Dsonar.login=**************************************** -Dsonar.projectKey=vip.$CI_PROJECT_NAME -Dsonar.gitlab.commit_sha=$CI_COMMIT_SHA -Dsonar.gitlab.ref_name=$CI_COMMIT_REF_NAME
