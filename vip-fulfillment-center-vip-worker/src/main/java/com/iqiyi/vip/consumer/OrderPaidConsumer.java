package com.iqiyi.vip.consumer;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.iqiyi.vip.aspect.WebLog;
import com.iqiyi.vip.domain.fulfillment.service.FulfillmentEventService;
import com.iqiyi.vip.domain.rights.factory.RightsFactory;
import com.iqiyi.vip.dto.order.OrderPaidMsg;
import com.iqiyi.vip.enums.DealModuleEnum;

/**
 * orderPaid消息
 *
 * <AUTHOR>
 * @date 2023/8/29 18:24
 */
@Slf4j
@Service
@RocketMQMessageListener(
    nameServer = "${rocketmq.orderPaid.nameServer}",
    topic = "${rocketmq.orderPaid.topic}",
    consumerGroup = "${rocketmq.orderPaid.consumerGroup}",
    token = "${rocketmq.orderPaid.token}")
public class OrderPaidConsumer implements RocketMQListener<String> {

    @Resource
    private FulfillmentEventService fulfillmentEventService;

    @WebLog
    @Override
    public void onMessage(String originalMsg) {
        try {
            OrderPaidMsg orderPaidMsg = JSON.parseObject(originalMsg, OrderPaidMsg.class);
            log.info("[start][orderPaidMsg:{}]", orderPaidMsg);
            //正单履约
            fulfillmentEventService.fulfillOrderFailRetry(RightsFactory.buildPaidOrderInfo(orderPaidMsg), DealModuleEnum.VIP_WORKER);
        } catch (Exception e) {
            log.error("[dealMsg Exception][originalMsg:{}]", originalMsg, e);
            throw e;
        }
    }

}
