package com.iqiyi.vip;

import com.alicp.jetcache.anno.config.EnableMethodCache;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.metrics.jdbc.DataSourcePoolMetricsAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import springfox.documentation.oas.annotations.EnableOpenApi;

import com.iqiyi.solar.config.client.spring.annotation.EnableCloudConfig;

/**
 * <AUTHOR>
 * @date 2024/10/31 15:57
 */
@EnableCloudConfig
@EnableOpenApi
@EnableCaching
@EnableMethodCache(basePackages = "com.iqiyi")
@SpringBootApplication(exclude = {DataSourcePoolMetricsAutoConfiguration.class})
public class VipWorkerApplication {

    public static void main(String[] args) {
        SpringApplication s = new SpringApplication(VipWorkerApplication.class);
        s.setAllowCircularReferences(Boolean.TRUE);
        s.run(args);
    }

}
