<?xml version="1.0" encoding="UTF-8"?>

<configuration>

  <!-- 根据模块ID调整日志文件命名 -->
  <springProperty scope="context" name="LOG_HOME" source="log.home"/>
  <property name="MODULE_NAME" value="vip-fulfillment-center-vip-worker" />
  <property name="LOG_FILE" value="${LOG_HOME}/${MODULE_NAME}.log" />
  <property name="ERROR_FILE" value="${LOG_HOME}/${MODULE_NAME}-error.log" />

  <!-- RollingFileAppender滚动记录文件，先将日志记录到指定文件，当符合某个条件时，将日志记录到其他文件 -->
  <appender name="ERROR_DAILY_ROLLING_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${ERROR_FILE}</file>
    <encoder>
      <!-- 设置日志记录行格式 -->
      <pattern>[%-5p] [%d{yyyy-MM-dd HH:mm:ss.SSS}] [%X{traceId}] [%t] [%C{1}:%M:%L] %m%n</pattern>
    </encoder>
    <!-- 只记录ERROR级别日志，添加范围过滤，可以将该类型的日志特殊记录到某个位置 -->
    <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
      <level>ERROR</level>
    </filter>
      <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
          <fileNamePattern>${ERROR_FILE}.%d{yyyy-MM-dd}</fileNamePattern>
      </rollingPolicy>
  </appender>

  <!--异步日志配置-->
  <appender name="ASYNC_ERROR_DAILY_ROLLING_FILE" class="ch.qos.logback.classic.AsyncAppender">
    <!-- 默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志.0表示queque满了，不丢弃，block线程 -->
    <!--<discardingThreshold >0</discardingThreshold>-->
    <!-- 阻塞队列的最大容量,该值会影响性能.默认值为256.
    如服务QPS为80，且没给请求答应一条日志，考虑到80%丢弃策略，则queueSize至少设置为100 -->
    <queueSize>512</queueSize>
    <!--因为性能原因 logback 的 AsyncAppender 默认是不记录该信息，需要开启-->
    <includeCallerData>true</includeCallerData>
    <!--设置为true时，不会让你的应用程序线程发生阻塞，队列如果超过限制，会执行丢弃日志操作-->
    <neverBlock>true</neverBlock>
    <!-- 添加附加的appender,最多只能添加一个 -->
    <appender-ref ref="ERROR_DAILY_ROLLING_FILE"/>
  </appender>

  <appender name="DAILY_ROLLING_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${LOG_FILE}</file>
    <encoder>
      <pattern>[%-5p] [%d{yyyy-MM-dd HH:mm:ss.SSS}] [%X{traceId}] [%t] [%C{1}:%M:%L] %m%n</pattern>
    </encoder>
    <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
      <level>DEBUG</level>
    </filter>
      <!-- 滚动策略， 最常用的滚动策略，它根据时间来制定滚动策略-->
      <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
          <!-- 定义滚动记录文件的名字，动态配置-->
          <fileNamePattern>${LOG_FILE}.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
          <!-- 单个文件最大容量 -->
          <maxFileSize>512MB</maxFileSize>
          <!-- 最多保留文件数 -->
          <maxHistory>400</maxHistory>
          <!-- 日志总量 -->
          <totalSizeCap>200GB</totalSizeCap>
      </rollingPolicy>
  </appender>

  <appender name="ASYNC_DAILY_ROLLING_FILE" class="ch.qos.logback.classic.AsyncAppender">
    <discardingThreshold>0</discardingThreshold>
    <queueSize>512</queueSize>
    <includeCallerData>true</includeCallerData>
    <neverBlock>true</neverBlock>
    <appender-ref ref="DAILY_ROLLING_FILE"/>
  </appender>

  <!-- ConsoleAppender代表输出到控制台 -->
  <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
      <pattern>%highlight([%-5p]) [%d{yyyy-MM-dd HH:mm:ss.SSS}] [%t] [%C{1}:%M:%L] %X{traceId} -> %m%n</pattern>
    </encoder>
  </appender>

    <!--sharding-->
    <logger name="Sharding-Sphere-SQL" level="WARN" />
    <!--eureka-->
    <logger name="com.netflix" level="OFF"> </logger>
    <logger name="org.springframework.cloud.netflix.eureka" level="WARN"> </logger>

  <!--根据环境参数配置跟节点日志级别，可以引入多个appender规则-->
  <springProfile name="prod">
    <root level="INFO">
      <appender-ref ref="ASYNC_DAILY_ROLLING_FILE"/>
      <appender-ref ref="ASYNC_ERROR_DAILY_ROLLING_FILE"/>
    </root>
  </springProfile>

  <springProfile name="test">
    <root level="INFO">
      <appender-ref ref="CONSOLE"/>
      <appender-ref ref="ASYNC_DAILY_ROLLING_FILE"/>
      <appender-ref ref="ASYNC_ERROR_DAILY_ROLLING_FILE"/>
    </root>
  </springProfile>

    <springProfile name="local">
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="ASYNC_DAILY_ROLLING_FILE"/>
            <appender-ref ref="ASYNC_ERROR_DAILY_ROLLING_FILE"/>
        </root>
    </springProfile>
</configuration>